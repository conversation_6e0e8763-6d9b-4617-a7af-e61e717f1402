<?php
namespace Model\Teasx;

use Model\Mispush\JXTJpushModel;
use Model\modelTpl;

class KidSchoolModel extends modelTpl
{
    public $error = false;
    public $errortip = false;
    public $versionsType = 'CN';
    public $schoolOne = array();//校区
    public $postbeOne = array();//权限
    public $classOne = array();//班级
    public $teacherOne = array();
    public $taskyear = '2025';
    public $semester = '1';//1上学期2下学期

    function __construct($publicarray = array())
    {
        parent::__construct();
        if (APPVER == 'TW') {
            $this->versionsType = 'TW';
        }

        $this->Schoolkddpark = new \Dbsqlplay(KddparkServerName, KddparkUserName, KddparkPassWord, KddparkDBName);

        $termrange = $this->Schoolkddpark->getFieldquery("kmc_code_termrange", "termrange_starttime,termrange_endtime", "company_id = '8888'");
        //年份处理
        $nowday = date("Y-m-d");
        if ($termrange) {
            foreach ($termrange as $item) {
                if ($nowday >= $item['termrange_starttime'] && $nowday <= $item['termrange_endtime']) {
                    $start_year = date("Y", strtotime($item['termrange_starttime']));
                    $end_year = date("Y", strtotime($item['termrange_endtime']));
                    $this->taskyear = $start_year;
                    if ($start_year == $end_year) {
                        $this->semester = '2';
                    } else {
                        $this->semester = '1';
                    }
                    break;
                } else {
                    $this->taskyear = date("Y") + 1;
                    $this->semester = '2';
                }
            }
        } else {
            if ($nowday > '2024-02-15' && $nowday <= '2024-08-01') {
                $this->taskyear = '2024';
                $this->semester = '2';
            } elseif ($nowday > '2024-08-01' && $nowday <= '2025-02-15') {
                $this->taskyear = '2024';
                $this->semester = '1';
            } elseif ($nowday > '2025-02-15' && $nowday <= '2025-07-31') {
                $this->taskyear = '2025';
                $this->semester = '2';
            } elseif ($nowday > '2025-08-01' && $nowday <= '2026-02-15') {
                $this->taskyear = '2025';
                $this->semester = '1';
            } elseif ($nowday > '2026-02-15' && $nowday <= '2026-07-31') {
                $this->taskyear = '2026';
                $this->semester = '2';
            }
        }

        if (is_array($publicarray)) {
            $this->setPublic($publicarray);
        }
        if (isset($publicarray['class_id'])) {
            $this->verdictClass($publicarray['class_id']);
        }
    }

    function setPublic($publicarray)
    {
        if (isset($publicarray['school_id'])) {
            $this->verdictSchool($publicarray['school_id']);
        } else {
            $this->error = true;
            $this->errortip = "学校ID必须传入";
            return false;
        }
        if (isset($publicarray['postbe_id'])) {
            $this->verdictPower($publicarray['postbe_id']);
        } else {
            $this->error = true;
            $this->errortip = "PostbeID必须传入";
            return false;
        }
        if (isset($publicarray['teacher_id'])) {
            $this->verdictTeacher($publicarray['teacher_id']);
        } else {
            $this->error = 0;
            $this->errortip = "操作ID必须传入";
            return false;
        }
    }

    //验证校园信息
    function verdictSchool($school_id)
    {
        $this->schoolOne = $this->Show_css->getFieldOne("app_school", "school_id,fromchannel,school_nature,school_class,school_branch,school_fullname,school_cnname,school_istest", "school_id = '{$school_id}'");
        if (!$this->schoolOne) {
            $this->error = true;
            $this->errortip = "校园信息不存在";
            return false;
        } else {
            if ($this->schoolOne['school_nature'] == 'school') {
                $this->schoolOne['school_mold'] = '0';
            } elseif ($this->schoolOne['school_nature'] == 'kid') {
                $this->schoolOne['school_mold'] = '1';
            } else {
                $this->schoolOne['school_mold'] = '2';
            }
            return true;
        }
    }

    //验证权限信息
    function verdictPower($postbe_id)
    {
        $this->postbeOne = $this->Show_css->selectOne("SELECT p.*,tp.organize_id,tp.post_id FROM eas_teacher_postbe as tp,eas_postrole as p WHERE tp.postrole_id = p.postrole_id and tp.postbe_id = '{$postbe_id}'");
        if (!$this->postbeOne) {
            $this->error = true;
            $this->errortip = "角色信息不存在";
            return false;
        } else {
            return true;
        }
    }

    //验证教师信息
    function verdictTeacher($teacher_id)
    {
        $this->teacherOne = $this->Show_css->getFieldOne("app_teacher", "teacher_id,teacher_branch,teacher_cnname,teacher_enname,teacher_mobile", "teacher_id = '{$teacher_id}'");
        if (!$this->teacherOne) {
            $this->error = 0;
            $this->errortip = "教师信息不存在";
            return false;
        } else {
            return true;
        }
    }

    //验证班级信息
    function verdictClass($class_id)
    {
        $this->classOne = $this->Show_css->getFieldOne("eas_classes", "class_id,class_branch,class_cnname,class_enname,class_enddate,course_branch", "class_id = '{$class_id}'");
        if (!$this->classOne) {
            $this->error = true;
            $this->errortip = "班级信息不存在";
            return false;
        } else {
            return true;
        }
    }

    function getClassListApi($paramArray)
    {
        if($this->teacherOne){
            $Model = new \Model\SchoolManageModel();
            $Model->UpdataTeacherTeaching($this->teacherOne['teacher_id'], $this->teacherOne['teacher_branch']);
        }
        $day = date("Ymd");
        $datawhere = "cl.school_branch = '{$this->schoolOne['school_branch']}' and cl.class_isdel = '0'";
        if ($this->postbeOne['postrole_isschoolleader'] !== '1') {
            $datawhere .= " and cl.class_id in ( SELECT t.class_id FROM eas_classes_teach as t where t.teacher_id = '{$paramArray['teacher_id']}' and t.teach_status = '0')";
        } else {
            if (!empty($paramArray['is_teacher'])) {
                $datawhere .= " and cl.class_id in ( SELECT t.class_id FROM eas_classes_teach as t where t.teacher_id = '{$paramArray['teacher_id']}' and t.teach_status = '0')";
            }
        }
        if (!empty($paramArray['class_id'])) {
            $datawhere .= " and cl.class_id = '{$paramArray['class_id']}'";
        }
        //班级状态
        if (!empty($paramArray['class_status'])) {
            if ($paramArray['class_status'] == 1) {
                $datawhere .= " and (cl.class_stdate > '{$day}' or cl.class_enddate = '19700101')";
            } elseif ($paramArray['class_status'] == 2) {
                $datawhere .= " and cl.class_stdate <= '{$day}' and cl.class_enddate >= '{$day}'";
            } elseif ($paramArray['class_status'] == -1) {
                $datawhere .= " and cl.class_enddate < '{$day}' and cl.class_enddate <> '19700101'";
            }
        }

        //关键词
        if (!empty($paramArray['keyword'])) {
            $datawhere .= " and (cl.class_cnname like '%{$paramArray['keyword']}%' or cl.class_enname like '%{$paramArray['keyword']}%' or cl.class_branch like '%{$paramArray['keyword']}%')";
        }

        if (!empty($paramArray['p'])) {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (!empty($paramArray['num'])) {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;


        $sql = "SELECT
                    cl.class_id,cl.class_cnname,cl.class_enname,cl.class_branch,cl.class_stdate,cl.class_enddate,
                    IFNULL((select 1 from eas_classes_teach as ct where ct.class_id = cl.class_id and ct.teacher_id = '{$paramArray['teacher_id']}' and ct.teach_status = '0' limit 1), '0') as is_teacher,
                    IFNULL((SELECT COUNT(s.log_id) FROM app_student_stationlog as s WHERE s.class_id = cl.class_id AND s.submit_status = '2' GROUP BY s.submit_time ORDER BY s.submit_time DESC LIMIT 0,1), '0') as stu_nums,
                    IFNULL((SELECT SUM(s.is_read) FROM app_student_stationlog as s WHERE s.class_id = cl.class_id AND s.submit_status = '2' GROUP BY s.submit_time ORDER BY s.submit_time DESC LIMIT 0,1), '0') as read_num,
                    IFNULL(q.taskCom, '0') AS task_comnums,
                    IFNULL(q.taskNum, '0') AS task_allnums
                FROM
                    eas_classes AS cl
                LEFT JOIN (
                    SELECT
                        c.class_id,
                        count(c.student_id) AS taskNum,
                        count(lg.learnitemslog_id) AS taskCom
                    FROM
                        eas_classes_tasks_learnitems AS a
                    INNER JOIN eas_classes_tasks AS b ON a.classtasks_id = b.classtasks_id
                    INNER JOIN eas_classes_studytimes AS c ON c.times_id = b.times_id
                    AND c.class_id = b.class_id
                    AND c.studytimes_status > 0
                    LEFT JOIN eas_classes as cl on cl.class_id = b.class_id
                    INNER JOIN app_taskitems AS d ON d.taskitems_id = a.taskitems_id
                    LEFT JOIN eas_classes_study_notextbook AS e ON e.class_id = c.class_id
                    AND e.student_id = c.student_id
                    AND e.textbook_id = d.textbook_id
                    LEFT JOIN app_student AS s ON s.student_id = c.student_id
                    LEFT JOIN app_student_learnitemslog AS lg ON lg.learnitems_id = a.learnitems_id AND lg.student_id = c.student_id
                    left join eas_stu_course_module_authority as kk on kk.student_id=c.student_id and kk.course_branch=cl.course_branch
                    WHERE {$datawhere}
                    and (
                            d.textbook_id = 0
                            OR e.notextbook_id IS NULL
                        )
                    AND b.classtasks_tasksviewtimes <= unix_timestamp(now())
                    AND (
                        d.taskitems_week = ''
                        OR (
                            from_unixtime(
                                b.classtasks_tasksviewtimes,
                                '%Y-%m-%d'
                            ) < (
                                DATE_SUB(
                                    CURDATE(),
                                    INTERVAL WEEKDAY(CURDATE()) + 0 DAY
                                )
                            )
                            OR d.taskitems_week <= (WEEKDAY(CURDATE()) + 1)
                        )
                    )
                    AND EXISTS (
                        SELECT
                            dd.fucmodule_id
                        FROM
                            pro_carditem AS aa
                            INNER JOIN pro_sales_channel AS bb ON aa.channel_id = bb.channel_id
                            INNER JOIN pro_sales_fucmodule AS dd ON dd.plan_id = bb.plan_id
                            INNER JOIN pro_product_fucmodule AS ee ON ee.fucmodule_id = dd.fucmodule_id
                            INNER JOIN app_student AS ff ON ff.student_branch = aa.student_branch
                        WHERE
                            aa.course_branch = d.course_branch
                            AND ff.student_id = c.student_id
                            AND ee.fucmodule_branch = 'Taskstu'
                        )
                    ".self::$moduleStr."
                    GROUP BY
                        b.class_id
                ) AS q ON q.class_id = cl.class_id
                WHERE 
                    {$datawhere}
                ORDER BY
                    is_teacher DESC,cl.class_enddate DESC,cl.class_stdate DESC,cl.class_id ASC
                LIMIT
                    {$pagestart},{$num}";
        $dataList = $this->Show_css->selectClear($sql);
        if ($dataList) {
            foreach ($dataList as &$value) {
                if ($value['class_stdate'] > $day || $value['class_enddate'] == '19700101') {
                    $value['class_status'] = "1";
                    $value['class_status_name'] = "待开班";
                } elseif ($value['class_enddate'] < $day) {
                    $value['class_status'] = "-1";
                    $value['class_status_name'] = "已结束";
                } elseif ($value['class_stdate'] <= $day && $value['class_enddate'] >= $day) {
                    $value['class_status'] = "2";
                    $value['class_status_name'] = "进行中";
                }
                $value['task_comrate'] = $value['task_comnums'] > 0 ? (round($value['task_comnums'] / $value['task_allnums'], 4) * 100) . '%' : '0%';
            }
        } else {
            $dataList = array();
        }

        $data['list'] = $dataList;
        return $data;
    }

    //获取奇趣任务列表
    function getQiQuTaskListApi($paramArray)
    {
        if (!empty($paramArray['course_mold'])) {
            $course_mold = $paramArray['course_mold'];
        } else {
            $course_mold = 1;
        }
        //仅学前支持批量开启班级任务
        $datawhere = "co.course_mold = '{$course_mold}' and cl.school_branch = '{$this->schoolOne['school_branch']}'";
        //班种
        if (!empty($paramArray['coursecat_branch'])) {
            $datawhere .= " and co.coursecat_branch = '{$paramArray['coursecat_branch']}'";
        }
        if ($course_mold == 1) {
            //学期
            if (!empty($paramArray['seriesType'])) {
                if($paramArray['seriesType'] == '1'){
                    $datawhere .= " and (co.course_cnname LIKE '%上学期%' or co.course_cnname LIKE '%上學期%')";
                }elseif($paramArray['seriesType'] == '2'){
                    $datawhere .= " and (co.course_cnname LIKE '%下学期%' or co.course_cnname LIKE '%下學期%')";
                }
            } else {
                if($this->semester == '1'){
                    $datawhere .= " and (co.course_cnname LIKE '%上学期%' or co.course_cnname LIKE '%上學期%')";
                }else{
                    $datawhere .= " and (co.course_cnname LIKE '%下学期%' or co.course_cnname LIKE '%下學期%')";
                }
            }
        }
        if ($course_mold == 2) {
            //兴趣班学期
            if (!empty($paramArray['semesterType'])) {
                if($paramArray['semesterType'] == '1'){
                    $datawhere .= " and co.course_branch IN ('KIRC1', 'KIRC3', 'KIRC5')";
                }elseif($paramArray['semesterType'] == '2'){
                    $datawhere .= " and co.course_branch IN ('KIRC2', 'KIRC4', 'KIRC6')";
                }
            } else {
                if($paramArray['semesterType'] == '1'){
                    $datawhere .= " and co.course_branch IN ('KIRC1', 'KIRC3', 'KIRC5')";
                }elseif($paramArray['semesterType'] == '2'){
                    $datawhere .= " and co.course_branch IN ('KIRC2', 'KIRC4', 'KIRC6')";
                }
            }
        }

        $day = date("Ymd");
        if ($this->schoolOne['fromchannel'] == 'centschool') {
            $daywhere = "ss.study_beginday <= '{$day}' and (ss.study_endday >= '{$day}' or (c.class_enddate < '{$day}' and ss.study_endday = c.class_enddate))";
        } else {
            $daywhere = "ss.study_beginday <= '{$day}' and ss.study_endday >= '{$day}'";
        }

        if (!empty($paramArray['p'])) {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (!empty($paramArray['num'])) {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT co.course_id,co.course_cnname,co.course_branch,co.course_times,IFNULL(q.class_num, '0') AS class_num,IFNULL(q.stu_num, '0') AS stu_num,IFNULL(q.taskNum, '0') AS taskNum,IFNULL(q.taskCom, '0') AS taskCom,
                       (select count(t.taskslog_id) from app_school_taskslog as t where t.school_id = '{$paramArray['school_id']}' and t.course_id = co.course_id and t.tasklog_year = '{$this->taskyear}') as task_num
                FROM eas_course as co 
                LEFT JOIN eas_classes AS cl ON cl.course_branch = co.course_branch
                LEFT JOIN (SELECT
                        f.course_branch,
                        (select count(c.class_id) from eas_classes as c where c.school_branch = f.school_branch and c.course_branch = f.course_branch and c.class_stdate <= '{$day}' and c.class_enddate >= '{$day}' and c.class_isdel = '0') as class_num,
                        (select count(distinct ss.student_id) from eas_classes as c,app_student_study as ss where ss.class_id = c.class_id and c.school_branch = f.school_branch and c.course_branch = f.course_branch and c.class_stdate <= '{$day}' and c.class_enddate >= '{$day}' and c.class_isdel = '0' and {$daywhere}) as stu_num,
                        count(c.student_id) AS taskNum,
                        count(lg.learnitemslog_id) as taskCom
                    FROM
                        eas_classes_tasks_learnitems AS a
                    INNER JOIN eas_classes_tasks AS b ON a.classtasks_id = b.classtasks_id
                    INNER JOIN eas_classes_studytimes AS c ON c.times_id = b.times_id
                    AND c.class_id = b.class_id
                    AND c.studytimes_status > 0
                    INNER JOIN app_taskitems AS d ON d.taskitems_id = a.taskitems_id
                    LEFT JOIN eas_classes_study_notextbook AS e ON e.class_id = c.class_id
                    AND e.student_id = c.student_id
                    AND e.textbook_id = d.textbook_id
                    LEFT JOIN eas_classes AS f ON f.class_id = b.class_id
                    LEFT JOIN app_student_learnitemslog as lg ON lg.learnitems_id = a.learnitems_id AND lg.student_id = c.student_id
                    left join eas_stu_course_module_authority as kk on kk.student_id=c.student_id and kk.course_branch=d.course_branch
                    WHERE 
                        f.school_branch = '{$this->schoolOne['school_branch']}' and f.class_stdate <= '{$day}' and f.class_enddate >= '{$day}'
                        AND EXISTS (SELECT 1 FROM app_school_taskslog as tl,eas_course as co WHERE tl.course_id = co.course_id and tl.school_id = b.school_id and co.course_branch = f.course_branch and tl.tasklog_year = '{$this->taskyear}')
                        AND (
                            d.textbook_id = 0
                            OR e.notextbook_id IS NULL
                        )
                        AND b.classtasks_tasksviewtimes <= unix_timestamp(now())
                        AND from_unixtime(b.classtasks_tasksviewtimes, '%Y') = '{$this->taskyear}'
                        AND (
                            d.taskitems_week = ''
                            OR (
                                from_unixtime(
                                    b.classtasks_tasksviewtimes,
                                    '%Y-%m-%d'
                                ) < (
                                    DATE_SUB(
                                        CURDATE(),
                                        INTERVAL WEEKDAY(CURDATE()) + 0 DAY
                                    )
                                )
                                OR d.taskitems_week <= (WEEKDAY(CURDATE()) + 1)
                            )
                        )
                        AND EXISTS (
                        SELECT
                            dd.fucmodule_id 
                        FROM
                            pro_carditem AS aa
                            INNER JOIN pro_sales_channel AS bb ON aa.channel_id = bb.channel_id
                            INNER JOIN pro_sales_fucmodule AS dd ON dd.plan_id = bb.plan_id
                            INNER JOIN pro_product_fucmodule AS ee ON ee.fucmodule_id = dd.fucmodule_id
                            INNER JOIN app_student AS ff ON ff.student_branch = aa.student_branch 
                        WHERE
                            aa.course_branch = f.course_branch 
                            AND ff.student_id = c.student_id 
                            AND ee.fucmodule_branch = 'Taskstu' 
                        ) 
                    ".self::$moduleStr."
                    GROUP BY 
                        f.course_branch
                    ) as q on q.course_branch = co.course_branch 
                WHERE {$datawhere}
                GROUP BY co.course_branch";

        $data = array();
        if (!empty($paramArray['is_count'])) {
            $db_nums = $this->Show_css->selectClear($sql);
            if ($db_nums) {
                $allnum = count($db_nums);
            } else {
                $allnum = 0;
            }
            $data['allnum'] = $allnum;
        }

        $dataList = $this->Show_css->selectClear($sql . " LIMIT {$pagestart},{$num}");
        if ($dataList) {
            foreach ($dataList as &$value) {
                if ($value['task_num']) {
                    $taskslog = $this->Show_css->selectOne("select t.times_id,t.taskslog_time from app_school_taskslog as t where t.school_id = '{$paramArray['school_id']}' and t.course_id = '{$value['course_id']}' and t.tasklog_year = '{$this->taskyear}' ORDER BY t.taskslog_time DESC");
                    if ($taskslog) {
                        $value['times_id'] = $taskslog['times_id'];
                        $value['task_time'] = date("Y-m-d H:i", $taskslog['taskslog_time']);
                    }
                    $value['task_nums'] = $value['task_num'] . '/' . $value['course_times'];
                } else {
                    $value['task_nums'] = '0/' . $value['course_times'];
                    $value['task_time'] = '--';
                }

                $value['task_rate'] = $value['taskNum'] > 0 ? (round($value['taskCom'] / $value['taskNum'], 4) * 100) . '%' : '--';
            }
        } else {
            $dataList = array();
        }

        $data['list'] = $dataList;
        return $data;
    }

    //园批量开启任务
    function openTaskApi($paramArray)
    {
        $classList = $this->Show_css->selectClear("SELECT class_id,class_cnname,class_enname FROM eas_classes 
                             WHERE course_branch = '{$paramArray['course_branch']}' and DATE_FORMAT(class_stdate, '%Y-%m-%d') <= CURDATE() 
                               and DATE_FORMAT(class_enddate, '%Y-%m-%d') >= CURDATE() AND school_branch = '{$this->schoolOne['school_branch']}' and class_isdel = '0'");
        if(!$classList){
            $this->error = 1;
            $this->errortip = "此年级不存在进行中的班级";
            return false;
        }
        $times_one = $this->Show_css->selectOne("SELECT t.times_sort FROM eas_course_times as t,eas_course as co 
                  WHERE t.course_branch = co.course_branch and t.course_branch = '{$paramArray['course_branch']}' 
                   and t.times_id NOT IN (select st.times_id from app_school_taskslog as st where st.school_id = '{$paramArray['school_id']}' and st.course_id = co.course_id and st.tasklog_year = '{$this->taskyear}') 
                  ORDER BY t.times_sort ASC");
        $times_two = $this->Show_css->selectOne("SELECT st.taskslog_time FROM eas_course_times as t,eas_course as co,app_school_taskslog as st
                  WHERE t.course_branch = co.course_branch and co.course_id = st.course_id and t.times_id = st.times_id and st.school_id = '{$paramArray['school_id']}' and t.course_branch = '{$paramArray['course_branch']}' and st.tasklog_year = '{$this->taskyear}' and t.times_sort < '{$times_one['times_sort']}'
                  ORDER BY t.times_sort DESC");
        if ($times_two['taskslog_time'] && (time() - $times_two['taskslog_time']) < 3600 && $this->schoolOne['school_istest'] == '0') {
            $this->error = 1;
            $this->errortip = "开启进度操作过于频繁，请在上一课次开启1小时后再次操作！";
            return false;
        }
        $timesList = $this->Show_css->selectClear("SELECT times_id FROM eas_course_times WHERE course_branch = '{$paramArray['course_branch']}' and times_sort <= '{$times_one['times_sort']}' ORDER BY times_sort ASC");
        if ($timesList) {
            $oknums = 0;
            $okclass = "开启成功班级：";
            $errorclass = "开启失败班级：";
            foreach ($timesList as $timesOne) {
                foreach ($classList as $value) {
                    if (!$this->Show_css->getFieldOne("eas_classes_tasks", "classtasks_id", "class_id='{$value['class_id']}' and times_id='{$timesOne['times_id']}'")) {
                        $paramArray['class_id'] = $value['class_id'];
                        $paramArray['times_id'] = $timesOne['times_id'];
                        $Model = new ClassModel($paramArray);
                        $res = $Model->addHourTaskApi($paramArray, 1);
                        if ($res) {
                            $oknums++;
                            $okclass .= $value['class_cnname'] . "/" . $value['class_enname'] . ",";
                        } else {
                            $errorclass .= $value['class_cnname'] . "/" . $value['class_enname'] . $Model->errortip . ",";
                        }
                    }
                }
            }

            if($oknums >= 0){
                if (!$this->Show_css->getFieldOne("app_school_taskslog", "taskslog_id"
                    , "school_id = '{$paramArray['school_id']}' and course_id = '{$paramArray['course_id']}' and times_id = '{$timesOne['times_id']}' and tasklog_year = '{$this->taskyear}' ")) {
                    $data = array();
                    $data['school_id'] = $paramArray['school_id'];
                    $data['course_id'] = $paramArray['course_id'];
                    $data['times_id'] = $timesOne['times_id'];
                    $data['teacher_id'] = $paramArray['teacher_id'];
                    $data['tasklog_year'] = $this->taskyear;
                    $data['taskslog_time'] = time();
                    $this->Show_css->insertData("app_school_taskslog", $data);
                }
                $this->error = 0;
//                $this->errortip = "{$okclass}{$errorclass}";
                $this->errortip = "开启成功";
                return true;
            }else{
                $this->error = 1;
                $this->errortip = "没有一个班级进度开启成功,{$errorclass}！";
                return true;
            }
        } else {
            $this->error = 1;
            $this->errortip = "此进度已开启，请勿重复开启！";
            return false;
        }
    }

    //园兴趣班批量开启任务
    function openInterestClassTaskApi($paramArray)
    {
        $classList = $this->Show_css->selectClear("SELECT class_id,class_cnname,class_enname FROM eas_classes 
                             WHERE course_branch = '{$paramArray['course_branch']}' and DATE_FORMAT(class_stdate, '%Y-%m-%d') <= CURDATE() 
                               and DATE_FORMAT(class_enddate, '%Y-%m-%d') >= CURDATE() AND school_branch = '{$this->schoolOne['school_branch']}' and class_isdel = '0'");
        if(!$classList){
            $this->error = 1;
            $this->errortip = "此年级不存在进行中的班级";
            return false;
        }
        $times_one = $this->Show_css->selectOne("SELECT t.times_sort FROM eas_course_times as t,eas_course as co 
                  WHERE t.course_branch = co.course_branch and t.course_branch = '{$paramArray['course_branch']}' 
                   and t.times_id NOT IN (select st.times_id from app_school_taskslog as st where st.school_id = '{$paramArray['school_id']}' and st.course_id = co.course_id and st.tasklog_year = '{$this->taskyear}') 
                  ORDER BY t.times_sort ASC");
        $times_two = $this->Show_css->selectOne("SELECT st.taskslog_time FROM eas_course_times as t,eas_course as co,app_school_taskslog as st
                  WHERE t.course_branch = co.course_branch and co.course_id = st.course_id and t.times_id = st.times_id and st.school_id = '{$paramArray['school_id']}' and t.course_branch = '{$paramArray['course_branch']}' and st.tasklog_year = '{$this->taskyear}' and t.times_sort < '{$times_one['times_sort']}'
                  ORDER BY t.times_sort DESC");
        if ($times_two['taskslog_time'] && (time() - $times_two['taskslog_time']) < 3600 && $this->schoolOne['school_istest'] == '0') {
            $this->error = 1;
            $this->errortip = "开启进度操作过于频繁，请在上一课次开启1小时后再次操作！";
            return false;
        }
        $timesList = $this->Show_css->selectClear("SELECT times_id FROM eas_course_times WHERE course_branch = '{$paramArray['course_branch']}' and times_sort <= '{$times_one['times_sort']}' ORDER BY times_sort ASC");
        if ($timesList) {
            $oknums = 0;
            $okclass = "开启成功班级：";
            $errorclass = "开启失败班级：";
            foreach ($timesList as $timesOne) {
                foreach ($classList as $value) {
                    if (!$this->Show_css->getFieldOne("eas_classes_tasks", "classtasks_id", "class_id='{$value['class_id']}' and times_id='{$timesOne['times_id']}'")) {
                        $paramArray['class_id'] = $value['class_id'];
                        $paramArray['times_id'] = $timesOne['times_id'];
                        $Model = new ClassModel($paramArray);
                        $res = $Model->addHourTaskApi($paramArray, 1);
                        if ($res) {
                            $oknums++;
                            $okclass .= $value['class_cnname'] . "/" . $value['class_enname'] . ",";
                        } else {
                            $errorclass .= $value['class_cnname'] . "/" . $value['class_enname'] . $Model->errortip . ",";
                        }
                    }
                }
            }

            if($oknums >= 0){
                if (!$this->Show_css->getFieldOne("app_school_taskslog", "taskslog_id"
                    , "school_id = '{$paramArray['school_id']}' and course_id = '{$paramArray['course_id']}' and times_id = '{$timesOne['times_id']}' and tasklog_year = '{$this->taskyear}' ")) {
                    $data = array();
                    $data['school_id'] = $paramArray['school_id'];
                    $data['course_id'] = $paramArray['course_id'];
                    $data['times_id'] = $timesOne['times_id'];
                    $data['teacher_id'] = $paramArray['teacher_id'];
                    $data['tasklog_year'] = $this->taskyear;
                    $data['taskslog_time'] = time();
                    $this->Show_css->insertData("app_school_taskslog", $data);
                }
                $this->error = 0;
                $this->errortip = "开启成功";
                return true;
            }else{
                $this->error = 1;
                $this->errortip = "没有一个班级进度开启成功,{$errorclass}！";
                return true;
            }
        } else {
            $this->error = 1;
            $this->errortip = "此进度已开启，请勿重复开启！";
            return false;
        }
    }

    //改变读书检核主管权限
    function changeMiniManager($paramArray)
    {
        $effectOne = $this->Show_css->getFieldOne("eas_teacher_postbe", "postbe_ismanager", "postbe_id = '{$paramArray['re_postbe_id']}'");
        $data = array();
        if ($effectOne['postbe_ismanager'] == '1') {
            $data['postbe_ismanager'] = '0';
        } else {
            $data['postbe_ismanager'] = '1';
        }

        if ($this->Show_css->updateData("eas_teacher_postbe", "postbe_id = '{$paramArray['re_postbe_id']}'", $data)) {
            $this->error = 0;
            $this->errortip = "改变状态成功";
            return true;
        } else {
            $this->error = 1;
            $this->errortip = "改变状态失败";
            return false;
        }
    }

    //教师带班明细
    function getTeacherClassList($paramArray)
    {
        //仅学前支持批量开启班级任务
        $datawhere = " p.school_branch = '{$this->schoolOne['school_branch']}' ";
        if (!empty($paramArray['teacher_status'])) {
            $datawhere .= " and t.teacher_status = '{$paramArray['teacher_status']}'";
        }
        if (!empty($paramArray['keyword'])) {
            $datawhere .= " and (t.teacher_cnname like '%{$paramArray['keyword']}%' or t.teacher_enname like '%{$paramArray['keyword']}%' or t.teacher_branch like '%{$paramArray['keyword']}%' or t.teacher_mobile like '%{$paramArray['keyword']}%')";
        }
        if (!empty($paramArray['p'])) {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (!empty($paramArray['num'])) {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $yes = APPVER == 'TW' ? '在職' : '在职';
        $not = APPVER == 'TW' ? '離職' : '离职';
        $day = date("Ymd");
        $sql = "SELECT 
                    p.postbe_id as re_postbe_id,
                    p.teacher_id,
                    p.postbe_ismanager,
                    p.postbe_status,
                    GROUP_CONCAT(DISTINCT( cl.class_cnname)) as class_name,
                    t.teacher_cnname,
                    t.teacher_enname,
                    t.teacher_sex,
                    t.teacher_branch,
                    t.teacher_mobile,
                    IF(t.teacher_status, '{$yes}', '{$not}') as teacher_status_name,
                    po.post_name,
                    count( DISTINCT( cl.class_id )) as class_num
                FROM
                    eas_teacher_postbe AS p 
                    left join eas_classes_teach as te on te.teacher_id = p.teacher_id
                    left join eas_classes as cl on cl.class_id = te.class_id and cl.school_branch = p.school_branch and cl.class_stdate <= '{$day}' and cl.class_enddate >= '{$day}'
                    left join app_teacher as t on t.teacher_id = p.teacher_id 
                    left join eas_post as po on po.post_id = p.post_id
                WHERE
                    {$datawhere}
                GROUP BY 
                    p.teacher_id";

        $data = array();
        if (!empty($paramArray['is_count'])) {
            $db_nums = $this->Show_css->selectClear($sql);
            if ($db_nums) {
                $allnum = count($db_nums);
            } else {
                $allnum = 0;
            }
            $data['allnum'] = $allnum;
        }

        $dataList = $this->Show_css->selectClear($sql . " LIMIT {$pagestart},{$num}");

        $data['list'] = $dataList ?: array();
        return $data;
    }

    //编辑教师职务
    function editTeacherPostApi($paramArray)
    {
        $data = array();
        $data['post_id'] = $paramArray['post_id'];
        $data['postbe_updatetime'] = time();
        if ($this->Show_css->updateData("eas_teacher_postbe", "postbe_id = '{$paramArray['re_postbe_id']}'", $data)) {
            $this->error = 0;
            $this->errortip = "编辑成功";
            return true;
        } else {
            $this->error = 1;
            $this->errortip = "编辑失败";
            return false;
        }
    }

    //获取家联本列表
    function getStationlogApi($paramArray)
    {
        $day = date("Ymd");
        //只有带班老师才可以发布家联本
        $datawhere = "c.school_branch = '{$this->schoolOne['school_branch']}' and c.class_isdel = '0' and (c.coursecat_branch = 'HS' or u.course_mold > 0)";
        if ($this->schoolOne['fromchannel'] == 'centschool') {
            $datawhere .= " and ss.study_beginday <= '{$day}' and (ss.study_endday >= '{$day}' or (c.class_enddate < '{$day}' and ss.study_endday = c.class_enddate))";
        } else {
            $datawhere .= " and ss.study_beginday <= '{$day}' and ss.study_endday >= '{$day}'";
        }
        if ($this->postbeOne['postrole_isschoolleader'] == '0') {
            $datawhere .= " AND c.class_id in ( SELECT t.class_id FROM eas_classes_teach as t where t.teacher_id = '{$paramArray['teacher_id']}' and t.teach_status = '0')";
        }

        if (!empty($paramArray['day'])) {
            $time = $paramArray['day'];
        } else {
            $time = date("Y-m-d");
        }

        if (!empty($paramArray['class_id'])) {
            $datawhere .= " and c.class_id = '{$paramArray['class_id']}'";
            if ($this->schoolOne['fromchannel'] == 'centschool') {
                $daywhere = "(ss.study_endday >= '{$day}' or ('{$this->classOne['class_enddate']}' < '{$day}' and ss.study_endday = '{$this->classOne['class_enddate']}'))";
            } else {
                $daywhere = "ss.study_endday >= '{$day}'";
            }
            if (isset($paramArray['status']) && $paramArray['status'] !== '') {
                if ($paramArray['status']) {
                    $datawhere .= " and EXISTS (select sl.log_id from app_student_stationlog as sl where sl.class_id = ss.class_id and sl.student_id = ss.student_id and sl.submit_time = '{$time}')";
                } else {
                    $datawhere .= " and NOT EXISTS (select sl.log_id from app_student_stationlog as sl where sl.class_id = ss.class_id and sl.student_id = ss.student_id and sl.submit_time = '{$time}')";
                }
            }
        } else {
            $daywhere = "ss.study_endday >= '{$day}'";
        }
        if (isset($paramArray['submit_status']) && $paramArray['submit_status'] !== '') {
            if ($paramArray['submit_status'] == 0) {
                $datawhere .= " and (NOT EXISTS (select sl.student_id from app_student_stationlog as sl where sl.class_id = ss.class_id and sl.student_id = ss.student_id and sl.submit_time = '{$time}')
                                or EXISTS (select sl.student_id from app_student_stationlog as sl where sl.class_id = ss.class_id and sl.student_id = ss.student_id and sl.submit_status = '-1' and sl.submit_time = '{$time}'))";
            } elseif ($paramArray['submit_status'] == 1) {
                $datawhere .= " and EXISTS (select sl.student_id from app_student_stationlog as sl where sl.class_id = ss.class_id and sl.student_id = ss.student_id and sl.submit_status <> '2' and sl.submit_status >= 0 and sl.submit_time = '{$time}')";
            } elseif ($paramArray['submit_status'] == 2) {
                $datawhere .= " and EXISTS (select sl.student_id from app_student_stationlog as sl where sl.class_id = ss.class_id and sl.student_id = ss.student_id and sl.submit_status = '2' and sl.submit_time = '{$time}')";
            }
        }
        if (!empty($paramArray['keyword'])) {
            $datawhere .= " and (s.student_cnname like '%{$paramArray['keyword']}%' or s.student_enname like '%{$paramArray['keyword']}%' or s.student_branch like '%{$paramArray['keyword']}%')";
        }

        if (!empty($paramArray['p'])) {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (!empty($paramArray['num'])) {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT 
                    ss.student_id,s.student_cnname,s.student_enname,s.student_branch,s.student_img,s.student_sex,c.class_id,c.class_cnname,c.class_enname,'{$time}' as hour_day,u.course_branch,
                    IFNULL((SELECT COUNT(sl.log_id) FROM app_student_stationlog as sl WHERE sl.class_id = c.class_id AND sl.submit_status = '2' GROUP BY sl.submit_time ORDER BY sl.submit_time DESC LIMIT 0,1),0) as stu_nums,
                    IFNULL((SELECT COUNT(sl.log_id) FROM app_student_stationlog as sl WHERE sl.class_id = c.class_id AND sl.submit_status = '2' AND sl.is_read = '1' GROUP BY sl.submit_time ORDER BY sl.submit_time DESC LIMIT 0,1),0) as read_num,
                    IFNULL((select sl.log_id from app_student_stationlog as sl where sl.class_id = ss.class_id and sl.student_id = ss.student_id and sl.submit_time = '{$time}' limit 1),0) as log_id,
                    IFNULL((select sl.submit_status from app_student_stationlog as sl where sl.class_id = ss.class_id and sl.student_id = ss.student_id and sl.submit_time = '{$time}' limit 1),0) as submit_status
                FROM 
                    app_student_study as ss 
                    LEFT JOIN app_student as s ON s.student_id = ss.student_id
                    LEFT JOIN eas_classes as c ON c.class_id = ss.class_id
                    LEFT JOIN eas_course as u ON c.course_branch = u.course_branch
                WHERE 
                    {$datawhere} 
                ORDER BY 
                    log_id ASC,ss.class_id DESC,ss.student_id DESC";

        $data = array();
        if (!empty($paramArray['is_count'])) {
            $db_nums = $this->Show_css->selectClear($sql);
            if ($db_nums) {
                $allnum = count($db_nums);
            } else {
                $allnum = 0;
            }
            $data['allnum'] = $allnum;
        }

        $dataList = $this->Show_css->selectClear($sql . " LIMIT {$pagestart},{$num}");
        if ($dataList) {
            $status = array("0" => "未阅读", "1" => "已阅读", "2" => "已回复");
            $type = array("0" => "正常", "1" => "缺勤", "2" => "异常");
            foreach ($dataList as &$value) {
                if ($this->Show_css->getFieldOne("eas_classes_teach", "teach_id", "teacher_id = '{$paramArray['teacher_id']}' and class_id = '{$value['class_id']}' and teach_status = '0'")) {
                    $value['is_purview'] = '1';
                } else {
                    $value['is_purview'] = '0';
                }
                $value['student_cnname'] = $value['student_enname'] ? $value['student_cnname'] . '/' . $value['student_enname'] : $value['student_cnname'];
                if ($value['log_id']) {
                    if ($value['submit_status'] == '2') {
                        $value['is_comment'] = '已发布';
                        $value['status'] = '1';
                    } elseif ($value['submit_status'] == '-1') {
                        $value['is_comment'] = '已撤回';
                        $value['status'] = '-1';
                    } else {
                        $station = $this->Show_css->getFieldOne("app_student_stationlog", "stationlog_isfixtimesend,stationlog_send_fixeddate", "log_id = '{$value['log_id']}'");
                        if ($station['stationlog_isfixtimesend']) {
                            $value['stationlog_send_fixeddate'] = $station['stationlog_send_fixeddate'];
                        }
                        $value['is_comment'] = '已编辑';
                        $value['status'] = '2';
                    }
                    $stationlog = $this->Show_css->selectOne("select sl.is_read,sl.status from app_student_stationlog as sl where sl.class_id = '{$value['class_id']}' and sl.student_id = '{$value['student_id']}' and sl.submit_time = '{$time}' and sl.submit_status = '2'");
                    if ($stationlog) {
                        $chatting = $this->Show_css->getOne("app_student_stationlog_chatting", "log_id = '{$value['log_id']}' and chatting_status = 0");
                        if ($chatting) {
                            $stationlog['is_read'] = '2';
                        }
                        $value['is_read'] = $stationlog['is_read'];
                        $value['read_status'] = $status[$stationlog['is_read']];
                        $value['check_status'] = $type[$stationlog['status']];
                    } else {
                        $value['is_read'] = '0';
                        $value['read_status'] = '--';
                        $value['check_status'] = '--';
                    }
                } else {
                    $value['is_comment'] = '未发布';
                    $value['status'] = '0';
                    $value['is_read'] = '0';
                    $value['read_status'] = '--';
                    $value['check_status'] = '--';
                }
            }
        } else {
            $dataList = array();
        }

        $stuOne = $this->Show_css->selectOne("SELECT count(ss.student_id) as num FROM app_student_study as ss WHERE ss.class_id = '{$paramArray['class_id']}' and ss.study_beginday <= '{$day}' and {$daywhere}");
        $data['stu_allnums'] = $stuOne['num'] ?: 0;
        $stuTwo = $this->Show_css->selectOne("SELECT count(student_id) as num FROM app_student_stationlog WHERE class_id = '{$paramArray['class_id']}' and submit_time = '{$time}' and submit_status = '2'");
        $data['comment_num'] = $stuTwo['num'] ?: 0;
        $data['list'] = $dataList;
        return $data;
    }

    //获取家联本学员数量
    function getStationStuNumApi($paramArray)
    {
        $day = date("Ymd");
        //只有带班老师才可以发布家联本
        $datawhere = "c.school_branch = '{$this->schoolOne['school_branch']}' and c.class_isdel = '0'";
        $datawhere .= " and (c.coursecat_branch = 'HS' or u.course_mold > 0) and c.class_stdate <= '{$day}' and c.class_enddate >= '{$day}'";
        if ($this->postbeOne['postrole_isschoolleader'] == '0') {
            $datawhere .= " and c.class_id in ( SELECT t.class_id FROM eas_classes_teach as t where t.teacher_id = '{$paramArray['teacher_id']}' and t.teach_status = '0')";
        }

        if (!empty($paramArray['class_id'])) {
            $datawhere .= " and c.class_id = '{$paramArray['class_id']}'";
            if ($this->schoolOne['fromchannel'] == 'centschool') {
                $datawhere .= " and ss.study_beginday <= '{$day}' and (ss.study_endday >= '{$day}' or ('{$this->classOne['class_enddate']}' < '{$day}' and ss.study_endday = '{$this->classOne['class_enddate']}'))";
            } else {
                $datawhere .= " and ss.study_beginday <= '{$day}' and ss.study_endday >= '{$day}'";
            }
        } else {
            $datawhere .= " and ss.study_beginday <= '{$day}' and ss.study_endday >= '{$day}'";
        }

        if (!empty($paramArray['day'])) {
            $time = $paramArray['day'];
        } else {
            $time = date("Y-m-d");
        }

        $sql = "SELECT 
                    count(ss.student_id) as num
                FROM app_student_study as ss 
                    LEFT JOIN app_student as s ON s.student_id = ss.student_id
                    LEFT JOIN eas_classes as c ON c.class_id = ss.class_id
                    LEFT JOIN eas_course as u ON c.course_branch = u.course_branch
                WHERE {$datawhere}";
        $stationOne = $this->Show_css->selectOne($sql. " and (ss.student_id not in (select sl.student_id from app_student_stationlog as sl where sl.class_id = ss.class_id and sl.submit_time = '{$time}') or ss.student_id in (select sl.student_id from app_student_stationlog as sl where sl.class_id = ss.class_id and sl.submit_status = '-1' and sl.submit_time = '{$time}'))");
        $stationTwo = $this->Show_css->selectOne($sql. " and ss.student_id in (select sl.student_id from app_student_stationlog as sl where sl.class_id = ss.class_id and sl.submit_status <> '2' and sl.submit_status >= 0 and sl.submit_time = '{$time}')");
        $stationSan = $this->Show_css->selectOne($sql. " and ss.student_id in (select sl.student_id from app_student_stationlog as sl where sl.class_id = ss.class_id and sl.submit_status = '2' and sl.submit_time = '{$time}')");

        $data = array();
        $data['not_edit_num'] = $stationOne['num'] ?: '0';
        $data['yes_edit_num'] = $stationTwo['num'] ?: '0';
        $data['send_num'] = $stationSan['num'] ?: '0';

        return $data;
    }

    //查看点评详情
    function getStationOneApi($paramArray)
    {
        $sql = "SELECT 
                   s.student_cnname,s.student_enname,s.student_branch,s.student_img,s.student_sex,l.log_id,l.class_id,ifnull(l.lesson_note,'') as lesson_note,
                   l.status,l.submit_time,l.note_json,l.familyfield_json,l.stationlog_evaldimensjson,ifnull(l.classhour_comment,'') as classhour_comment,
                   l.stationlog_isfixtimesend,l.stationlog_send_fixeddate
                FROM 
                    app_student_stationlog as l 
                    LEFT JOIN app_student as s ON s.student_id = l.student_id
                WHERE 
                    l.log_id = '{$paramArray['log_id']}'";
        $dataOne = $this->Show_css->selectOne($sql);
        if ($dataOne['stationlog_evaldimensjson']) {
            $dataOne['stationlog_evaldimensjson'] = json_decode($dataOne['stationlog_evaldimensjson'], true);
        }
        $Model = new ClassModel();
        $dataOne['commentList'] = $Model->getCommentList('', $dataOne['log_id'], $dataOne['class_id']);

        return $dataOne;
    }

    //获取套用内容
    function getApplyNoteApi($paramArray)
    {
        $data = array();
        $logOne = $this->Show_css->getFieldOne("app_student_stationlog","lesson_note","class_id = '{$paramArray['class_id']}' and lessonnote_isapply = '1' and submit_time = CURDATE()","Order by createtime Desc");
        if ($logOne['lesson_note']) {
            $data['lesson_note'] = $logOne['lesson_note'];
        }
        $logTwo = $this->Show_css->getFieldOne("app_student_stationlog","note_json","class_id = '{$paramArray['class_id']}' and matter_isapply = '1' and submit_time = CURDATE()","Order by createtime Desc");
        if ($logTwo['note_json']) {
            $note_json = json_decode($logTwo['note_json'], true);
            if($note_json){
                foreach ($note_json as $jsonOne) {
                    if($jsonOne['field_name'] == '家长配合事项'){
                        if ($jsonOne['list']) {
                            foreach ($jsonOne['list'] as $value) {
                                if ($value['value'] && !is_array($value['value'])) {
                                    $data['matter_note'] = trim($value['value']);
                                }
                            }
                        }
                    }
                }
            }
        }
        $logSan = $this->Show_css->getFieldOne("app_student_stationlog","classhour_comment","class_id = '{$paramArray['class_id']}' and comment_isapply = '1' and submit_time = CURDATE()","Order by createtime Desc");
        if ($logSan['classhour_comment']) {
            $data['comment_note'] = $logSan['classhour_comment'];
        }

        return $data;
    }

    //获取家联本字段
    function getStationFieldApi($paramArray)
    {
        $datawhere = "f.field_status = '1' and f.field_id NOT IN (SELECT field_id FROM app_famschool_unmatch as u WHERE u.school_id = '{$paramArray['school_id']}')";
        if (isset($paramArray['field_language']) && $paramArray['field_language'] !== '') {
            $datawhere .= " and f.field_language = '{$paramArray['field_language']}'";
        } else {
            $datawhere .= " and f.field_language = 0";
        }

        $sql = "SELECT 
                   f.field_id,f.field_name,f.field_pid,f.field_type,f.field_json,f.field_default
                FROM 
                    app_family_fields as f 
                WHERE 
                     {$datawhere}
                ORDER BY 
                    f.field_pid ASC,f.field_sort ASC";
        $dataList = $this->Show_css->selectClear($sql);
        if ($dataList) {
            $dataList = $this->getChildField($dataList);
        } else {
            $dataList = array();
        }

        $data = array();

        $student = $this->Show_css->getFieldOne("app_student", "student_cnname,student_enname", "student_id = '{$paramArray['student_id']}'");
        $student['hour_day'] = date("Y-m-d");
        $data['list'] = $dataList;
        $data['student'] = $student;
        return $data;
    }

    //处理家联本的字段
    function getChildField($familyFields)
    {
        $re_familyFields = array();
        if ($familyFields) {
            foreach ($familyFields as $value) {
                if (!$value['field_pid']) {
                    $re_familyFields[$value['field_id']]['field_id'] = $value['field_id'];
                    $re_familyFields[$value['field_id']]['field_name'] = $value['field_name'];
                    $re_familyFields[$value['field_id']]['field_pid'] = $value['field_pid'];
                    $re_familyFields[$value['field_id']]['field_type'] = $value['field_type'];
                }
                if ($value['field_name'] == '家长配合事项') {
                    $re_familyFields[$value['field_id']]['is_apply'] = '1';
                }
                if ($value['field_pid']) {
                    $re_familyFields[$value['field_pid']]['field_array'][] = $value;
                    unset($value);
                }
            }
        }
        if ($re_familyFields){
            foreach ($re_familyFields as $k => $v) {
                if ($v['field_array']) {
                    foreach ($v['field_array'] as $key => $value) {
                        $re_familyFields[$k]['field_array'][$key]['field_json'] = $value['field_json'] == '' ? array() : json_decode($value['field_json'],true);
                    }
                }
            }
        }

        return $re_familyFields;
    }

    //学员点评
    function addStudentCommentsApi($paramArray)
    {
        $student_list = json_decode(stripslashes($paramArray['student_list']), true);
        if (empty($student_list)) {
            $this->error = 1;
            $this->errortip = '请选择学员';
            return false;
        }
        if (!$this->Show_css->getFieldOne("eas_classes_teach", "teach_id", "teacher_id = '{$paramArray['teacher_id']}' and class_id = '{$paramArray['class_id']}' and teach_status = '0'")) {
            $this->error = 1;
            $this->errortip = '不是该班级带班教师，不能点评！';
            return false;
        }
        if (APPVER == 'TW' && $paramArray['field_language'] && $paramArray['stationlog_isfixtimesend'] && date("w", strtotime($paramArray['stationlog_send_fixeddate'])) != 5) {
            $this->error = 1;
            $this->errortip = '英文家联本，请选择在周五发送！';
            return false;
        }
        if ($paramArray['stationlog_isfixtimesend'] && $paramArray['day'] == date("Y-m-d") && explode(' ', $paramArray['stationlog_send_fixeddate'])[1] < date("H:i")) {
            $this->error = 1;
            $this->errortip = '发布时间不可早于当前时间！';
            return false;
        }
        if (!empty($paramArray['day'])) {
            $time = $paramArray['day'];
        } else {
            $time = date('Y-m-d');
        }
        if ($paramArray['submit_status'] == '3') {
            $tip = "保存成功";
        } else{
            $tip = "点评成功";
        }
        if (!isset($paramArray['field_language'])) {
            $paramArray['field_language'] = 0;
        }
        $this->Show_css->query("SET NAMES utf8mb4");
        foreach ($student_list as $value) {
            $stationlog = $this->Show_css->getFieldOne("app_student_stationlog", "log_id,field_language", "class_id = '{$paramArray['class_id']}' and student_id = '{$value['student_id']}' and field_language = '{$paramArray['field_language']}' and submit_time = '{$time}'");
            if (!$stationlog) {
                $data = array();
                $data['class_id'] = $paramArray['class_id'];
                $data['student_id'] = $value['student_id'];
                $data['student_branch'] = $value['student_branch'];
                $data['lesson_note'] = emoji_encode($paramArray['lesson_note']);
                $data['iteacher_id'] = $paramArray['teacher_id'];
                $data['status'] = $paramArray['status'];
                $data['is_read'] = '0';
                $data['classhour_comment'] = emoji_encode($paramArray['classhour_comment']);
                $data['stationlog_evaldimensjson'] = $paramArray['stationlog_evaldimensjson'];
                $data['lessonnote_isapply'] = $paramArray['lessonnote_isapply'];
                $data['matter_isapply'] = $paramArray['matter_isapply'];
                $data['comment_isapply'] = $paramArray['comment_isapply'];
                $data['note_json'] = emoji_encode($paramArray['note_json']);
                $data['familyfield_json'] = emoji_encode($paramArray['familyfield_json']);
                if ($paramArray['status'] == '2') {
                    $data['reason'] = emoji_encode($paramArray['reason']);
                }
                if ($paramArray['field_language']) {
                    $todayweek = date("w", strtotime($time));
                    if ($todayweek == 6) {
                        $addnum = $todayweek;
                    } else {
                        $addnum = 5 - $todayweek;
                    }
                    if (!$paramArray['stationlog_isfixtimesend'] && $time <> date("Y-m-d")) {
                        $paramArray['submit_status'] = 3;
                        $paramArray['stationlog_isfixtimesend'] = 1;
                        $paramArray['stationlog_send_fixeddate'] = date("Y-m-d", strtotime("$time +$addnum Days")).' 16:30';
                    }
                    $data['field_language'] = $paramArray['field_language'];
                }
                $data['stationlog_isfixtimesend'] = $paramArray['stationlog_isfixtimesend'];
                if ($paramArray['stationlog_isfixtimesend']) {
                    $data['submit_status'] = '0';
                    $data['stationlog_send_fixeddate'] = $paramArray['stationlog_send_fixeddate'];
                } else {
                    $data['submit_status'] = $paramArray['submit_status'];
                }
                $data['submit_time'] = $time;
                $data['createtime'] = time();
                $dataid = $this->Show_css->insertData("app_student_stationlog", $data);
                $log_id = $dataid;

                $info = array();
                $info['log_id'] = $log_id;
                $info['info_createtime'] = time();
                $dataid = $this->Show_css->insertData("app_student_stationlog_info", $info);
            } else {
                $data = array();
                $data['lesson_note'] = emoji_encode($paramArray['lesson_note']);
                $data['iteacher_id'] = $paramArray['teacher_id'];
                $data['status'] = $paramArray['status'];
                $data['classhour_comment'] = emoji_encode($paramArray['classhour_comment']);
                $data['stationlog_evaldimensjson'] = $paramArray['stationlog_evaldimensjson'];
                $data['lessonnote_isapply'] = $paramArray['lessonnote_isapply'];
                $data['matter_isapply'] = $paramArray['matter_isapply'];
                $data['comment_isapply'] = $paramArray['comment_isapply'];
                $data['note_json'] = emoji_encode($paramArray['note_json']);
                $data['familyfield_json'] = emoji_encode($paramArray['familyfield_json']);
                if ($paramArray['status'] == '2') {
                    $data['reason'] = emoji_encode($paramArray['reason']);
                }
                if ($stationlog['field_language']) {
                    $todayweek = date("w", strtotime($time));
                    if ($todayweek == 6) {
                        $addnum = $todayweek;
                    } else {
                        $addnum = 5 - $todayweek;
                    }
                    if (!$paramArray['stationlog_isfixtimesend'] && $time <> date("Y-m-d")) {
                        $paramArray['submit_status'] = 3;
                        $paramArray['stationlog_isfixtimesend'] = 1;
                        $paramArray['stationlog_send_fixeddate'] = date("Y-m-d", strtotime("$time +$addnum Days")).' 16:30';
                    }
                }
                $data['stationlog_isfixtimesend'] = $paramArray['stationlog_isfixtimesend'];
                if ($paramArray['stationlog_isfixtimesend']) {
                    $data['submit_status'] = '0';
                    $data['stationlog_send_fixeddate'] = $paramArray['stationlog_send_fixeddate'];
                } else {
                    $data['submit_status'] = $paramArray['submit_status'];
                }
                $data['submit_time'] = $time;
                $dataid = $this->Show_css->updateData("app_student_stationlog", "log_id = '{$stationlog['log_id']}'", $data);
                $log_id = $stationlog['log_id'];
            }
        }
        if ($dataid) {
            if ($data['submit_status'] == '2') {
                $ApiModel = new ApiModel();
                $ApiModel->StationRemind($log_id, $this->schoolOne['school_class']);
            }

            if(isset($paramArray['lessonnote_isapply']) && $paramArray['lessonnote_isapply'] == '1'){
                $this->Show_css->updateData("app_student_stationlog","class_id = '{$paramArray['class_id']}' and log_id <> '{$log_id}' and submit_time = '{$time}'",array("lessonnote_isapply" => 0));
            }
            if(isset($paramArray['matter_isapply']) && $paramArray['matter_isapply'] == '1'){
                $this->Show_css->updateData("app_student_stationlog","class_id = '{$paramArray['class_id']}' and log_id <> '{$log_id}' and submit_time = '{$time}'",array("matter_isapply" => 0));
            }
            if(isset($paramArray['comment_isapply']) && $paramArray['comment_isapply'] == '1'){
                $this->Show_css->updateData("app_student_stationlog","class_id = '{$paramArray['class_id']}' and log_id <> '{$log_id}' and submit_time = '{$time}'",array("comment_isapply" => 0));
            }
        }
        $this->error = 0;
        $this->errortip = $tip;
        return true;
    }

    //批量发送/设置定时发送
    function batchStationlogApi($paramArray)
    {
        $log_list = json_decode(stripslashes($paramArray['log_list']), true);
        if (empty($log_list)) {
            $this->error = 1;
            $this->errortip = '请选择学员';
            return false;
        }
        if (!empty($paramArray['day'])) {
            $time = $paramArray['day'];
        } else {
            $time = date('Y-m-d');
        }
        foreach ($log_list as $value) {
            $logOne = $this->Show_css->getFieldOne("app_student_stationlog", "field_language", "log_id = '{$value['log_id']}'");
            $data = array();
            if ($logOne['field_language']) {
                $todayweek = date("w", strtotime($time));
                if ($todayweek == 6) {
                    $addnum = $todayweek;
                } else {
                    $addnum = 5 - $todayweek;
                }
                if (!$paramArray['stationlog_isfixtimesend'] && $time <> date("Y-m-d")) {
                    $paramArray['submit_status'] = 3;
                    $paramArray['stationlog_isfixtimesend'] = 1;
                    $paramArray['stationlog_send_fixeddate'] = date("Y-m-d", strtotime("$time +$addnum Days")).' 16:30';
                }
            }
            $data['stationlog_isfixtimesend'] = $paramArray['stationlog_isfixtimesend'];
            if ($paramArray['stationlog_isfixtimesend']) {
                $data['submit_status'] = '0';
                $data['stationlog_send_fixeddate'] = $paramArray['stationlog_send_fixeddate'];
            } else {
                $data['submit_status'] = '2';
            }
            if ($this->Show_css->updateData("app_student_stationlog", "log_id = '{$value['log_id']}'", $data)) {
                if ($data['submit_status'] == '2') {
                    $ApiModel = new ApiModel();
                    $ApiModel->StationRemind($value['log_id'], $this->schoolOne['school_class']);
                }
            }
        }
        $this->error = 0;
        $this->errortip = "发布成功";
        return true;
    }

    //撤回家联本
    function recallStationlogApi($paramArray)
    {
        $data = array();
        $data['submit_status'] = '-1';
        $data['is_read'] = '0';
        $data['lesson_note'] = '';
        $data['classhour_comment'] = '';
        $data['matter_isapply'] = '0';
        $data['comment_isapply'] = '0';
        $data['lessonnote_isapply'] = '0';
        $data['stationlog_isfixtimesend'] = '0';
        $data['stationlog_send_fixeddate'] = '';
        $data['stationlog_evaldimensjson'] = '';
        if ($this->Show_css->updateData("app_student_stationlog", "log_id = '{$paramArray['log_id']}'", $data)) {
            $this->Show_css->delData("app_student_stationlog_chatting", "log_id = '{$paramArray['log_id']}'");
            $this->error = 0;
            $this->errortip = "撤回成功";
            return true;
        } else {
            $this->error = 1;
            $this->errortip = "撤回失败";
            return false;
        }
    }

    //获取家长回复
    function getParentReplyListApi($paramArray)
    {
        $datawhere = "c.school_branch = '{$this->schoolOne['school_branch']}' and c.class_id in ( SELECT t.class_id FROM eas_classes_teach as t where t.teacher_id = '{$paramArray['teacher_id']}' and t.teach_status = '0')";

        if (!empty($paramArray['class_id'])) {
            $datawhere .= " and l.class_id = '{$paramArray['class_id']}'";
        }
        $datawhere .= " and l.submit_status = '2' and l.is_read = '1'";

        if (!empty($paramArray['replystatus'])) {
            $datawhere .= " and exists (select sc.chatting_id from app_student_stationlog_chatting as sc where sc.log_id = l.log_id)";
        }
        if ($paramArray['replystatus'] == '0') {
            $datawhere .= " and exists (select sc.chatting_id from app_student_stationlog_chatting as sc where sc.log_id = l.log_id and sc.chatting_status = '0')";
        } elseif ($paramArray['replystatus'] == '1') {
            $datawhere .= " and not exists (select sc.chatting_id from app_student_stationlog_chatting as sc where sc.log_id = l.log_id and sc.chatting_status = '0')";
        }
        if (isset($paramArray['is_reply'])) {
            $datawhere .= " and exists (select sc.chatting_id from app_student_stationlog_chatting as sc where sc.log_id = l.log_id and sc.chatting_status = '0')";
        }

        if (!empty($paramArray['keyword'])) {
            $datawhere .= " and (s.student_cnname like '%{$paramArray['keyword']}%' or s.student_enname like '%{$paramArray['keyword']}%' or s.student_branch like '%{$paramArray['keyword']}%')";
        }

        if (!empty($paramArray['p'])) {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (!isset($paramArray['num'])) {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT l.log_id,l.submit_time,l.classhour_comment,s.student_id,s.student_cnname,s.student_enname,s.student_branch,c.class_id,c.class_cnname,c.class_enname
                FROM app_student_stationlog as l 
                LEFT JOIN app_student as s ON s.student_id = l.student_id
                LEFT JOIN eas_classes as c ON c.class_id = l.class_id 
                WHERE {$datawhere} 
                ORDER BY l.submit_time DESC";

        $data = array();
        if (!empty($paramArray['is_count'])) {
            $db_nums = $this->Show_css->selectClear($sql);
            if ($db_nums) {
                $allnum = count($db_nums);
            } else {
                $allnum = 0;
            }
            $data['allnum'] = $allnum;
        }

        $dataList = $this->Show_css->selectClear($sql. " LIMIT {$pagestart},{$num}");
        if ($dataList) {
            foreach ($dataList as &$value) {
                $value['student_cnname'] = $value['student_enname'] ? $value['student_cnname'] . '/' . $value['student_enname'] : $value['student_cnname'];
                $chatOne = $this->Show_css->getFieldOne("app_student_stationlog_chatting", "chatting_type", "log_id = '{$value['log_id']}' order by chatting_id desc");
                $chatTwo = $this->Show_css->getFieldOne("app_student_stationlog_chatting", "chatting_note", "log_id = '{$value['log_id']}' and chatting_type = '1' and chatting_status = '0' order by chatting_id desc");
                $value['chatting_type'] = $chatOne['chatting_type'];
                $value['chatting_note'] = $chatTwo['chatting_note'];
                $member = $this->Show_css->selectClear("SELECT FROM_UNIXTIME(chatting_createtime,'%Y-%m-%d %H:%i') AS chatting_createtime,chatting_note FROM app_student_stationlog_chatting WHERE log_id = '{$value['log_id']}' and chatting_type = '1' ORDER BY chatting_id DESC");
                if ($member) {
                    $value['chatting_note'] = $member;
                } else {
                    $value['chatting_note'] = '--';
                }
                $teacher = $this->Show_css->selectClear("SELECT FROM_UNIXTIME(chatting_createtime,'%Y-%m-%d %H:%i') AS chatting_createtime,chatting_note FROM app_student_stationlog_chatting WHERE log_id = '{$value['log_id']}' and chatting_type = '2' and chatting_status <> '2' and chatting_isrevoke = '0' ORDER BY chatting_id DESC");
                if ($teacher) {
                    $value['append_replay'] = $teacher;
                } else {
                    $value['append_replay'] = '--';
                }
                if($this->Show_css->getOne("app_student_stationlog_chatting", "log_id = '{$value['log_id']}' and chatting_status = '0'")){
                    $value['is_replay'] = '0';
                }else{
                    $value['is_replay'] = '1';
                }
            }
        } else {
            $dataList = array();
        }

        $data['list'] = $dataList;
        return $data;
    }

    //获取家长回复 -- 园移动端
    function getParentReplyAppApi($paramArray)
    {
        $datawhere = "cs.school_branch = '{$this->schoolOne['school_branch']}' and cs.class_isdel = '0'";
        if ($this->postbeOne['postrole_isschoolleader'] !== '1') {
            $datawhere .= " and l.class_id in ( SELECT t.class_id FROM eas_classes_teach as t where t.teacher_id = '{$paramArray['teacher_id']}' and t.teach_status = '0')";
        }
        if (!empty($paramArray['class_id'])) {
            $datawhere .= " and l.class_id = '{$paramArray['class_id']}'";
        }

        if (!empty($paramArray['p'])) {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (!empty($paramArray['num'])) {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT 
                    s.student_cnname,s.student_enname,cs.class_cnname,l.chatting_id,l.log_id,c.chatting_note
                FROM 
                    app_student_stationlog_chatting as c 
                    LEFT JOIN app_student_stationlog as l ON l.log_id = c.log_id
                    LEFT JOIN app_student as s ON s.student_id = l.student_id
                    LEFT JOIN eas_classes as cs ON cs.class_id = l.class_id 
                WHERE 
                    {$datawhere} and c.chatting_type = '1' and l.submit_status = '2'
                ";

        $db_nums = $this->Show_css->selectClear($sql);
        if ($db_nums) {
            $allnum = count($db_nums);
        } else {
            $allnum = 0;
        }
        $data['allnum'] = $allnum;

        $dataList = $this->Show_css->selectClear($sql . " LIMIT {$pagestart},{$num}");
        if (!$dataList) {
            $dataList = array();
        }

        $data['list'] = $dataList;
        return $data;
    }

    //回复/批量回复
    function batchReplayApi($paramArray)
    {
        $log_list = json_decode(stripslashes($paramArray['log_list']), true);
        if (empty($log_list)) {
            $this->error = 1;
            $this->errortip = '请选择学员';
            return false;
        }
        $this->Show_css->query("SET NAMES utf8mb4");
        foreach ($log_list as $log_id) {
            $data = array();
            $data['log_id'] = $log_id;
            $data['chatting_type'] = '2';
            $data['teacher_id'] = $paramArray['teacher_id'];
            $data['chatting_status'] = $paramArray['chatting_status'];
            if ($paramArray['chatting_status'] == '1') {
                $data['chatting_note'] = emoji_encode($paramArray['chatting_note']);
            }
            $data['chatting_createtime'] = time();
            $dataid = $this->Show_css->insertData("app_student_stationlog_chatting", $data);
            if ($dataid) {
                if ($paramArray['chatting_status'] == '1') {
                    $ApiModel = new ApiModel();
                    $ApiModel->StationRemind($log_id, $this->schoolOne['school_class'], 1);
                }
                $this->Show_css->updateData("app_student_stationlog_chatting", "log_id = '{$log_id}' and chatting_status = '0'", array("chatting_status" => '1'));
            } else {
                $this->error = 1;
                $this->errortip = "回复失败";
                return false;
            }
        }
        $this->error = 0;
        $this->errortip = "回复成功";
        return true;
    }

    //一键无需回复
    function oneKeyReplyApi($paramArray)
    {
        $sql = "SELECT 
                    s.log_id
                FROM 
                    app_student_stationlog as s
                WHERE 
                    s.class_id = '{$paramArray['class_id']}' AND s.submit_status = '2'
                    and exists (select 1 from app_student_stationlog_chatting as r where r.log_id = s.log_id and r.chatting_status = '0')
                ";
        $dataList = $this->Show_css->selectClear($sql);
        if ($dataList) {
            foreach ($dataList as $value) {
                $data = array();
                $data['log_id'] = $value['log_id'];
                $data['teacher_id'] = $paramArray['teacher_id'];
                $data['chatting_type'] = '2';
                $data['chatting_status'] = '2';
                $data['chatting_createtime'] = time();
                $dataid = $this->Show_css->insertData("app_student_stationlog_chatting", $data);
                if ($dataid) {
                    $this->Show_css->updateData("app_student_stationlog_chatting", "log_id = '{$value['log_id']}' and chatting_status = '0'", array("chatting_status" => '1'));
                }
            }
        }
        $this->error = 0;
        $this->errortip = "一键无需回复成功";
        return true;
    }

    //撤回教师回复
    function revokeReplyApi($paramArray)
    {
        if ($this->Show_css->updateData("app_student_stationlog_chatting", "chatting_id = '{$paramArray['chatting_id']}'", array("chatting_isrevoke" => '1'))) {
            $this->Show_css->updateData("app_student_stationlog_chatting", "log_id = '{$paramArray['log_id']}' and member_id > 0 and chatting_status = '1'", array("chatting_status" => '0'));
            $this->error = 0;
            $this->errortip = "撤回成功";
            return true;
        } else {
            $this->error = 1;
            $this->errortip = "撤回失败";
            return false;
        }
    }

    //获取配音作品
    function getDubbingWorksApi($paramArray)
    {
        $datawhere = "w.class_id = '{$paramArray['class_id']}' and w.work_status = '1'";
        if (!empty($paramArray['keyword'])) {
            $datawhere .= " and (s.student_cnname like '%{$paramArray['keyword']}%' or s.student_enname like '%{$paramArray['keyword']}%' or s.student_branch like '%{$paramArray['keyword']}%')";
        }
        if (!empty($paramArray['starttime'])) {
            $start_time = strtotime($paramArray['starttime']);
            $datawhere .= "  and w.work_createtime >= '{$start_time}'";
        }
        if (!empty($paramArray['endtime'])) {
            $endtime = strtotime($paramArray['endtime'] . ' 23:59:59');
            $datawhere .= "  and w.work_createtime <= '{$endtime}'";
        }

        $having = "1=1";
        if (isset($paramArray['comment_status']) && $paramArray['comment_status'] !== '') {
            if ($paramArray['comment_status']) {
                $having .= " and is_comment > 0";
            } else {
                $having .= " and is_comment = 0";
            }
        }
        if (!empty($paramArray['work_type'])) {
            $having .= " and work_type = '{$paramArray['work_type']}'";
        }

        if (!empty($paramArray['p'])) {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (!empty($paramArray['num'])) {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT  s.student_id,s.student_cnname,s.student_enname,s.student_branch,s.student_sex,s.student_img,'1' as work_type,
                        w.work_id,ar.audiorecords_name as work_name,w.work_score,FROM_UNIXTIME(w.work_createtime,'%Y-%m-%d %H:%i') as work_time,
                        IFNULL((select COUNT(dr.reviewlog_id) from app_dubbingworks_reviewlog as dr where dr.work_id = w.work_id and dr.work_type = '1' and dr.teacher_id > 0), 0) as is_comment,count(aw.workpages_id) as work_thisnum,
                        (select count(aw.workpages_id) from app_audiorecords_work as wk,app_audiorecords_workpages as aw where wk.work_id = aw.work_id and wk.class_id = w.class_id and wk.student_id = w.student_id) as work_allnum,
                        (select count(wk.work_id) from app_audiorecords_work as wk where wk.class_id = w.class_id and wk.student_id = w.student_id) as work_dubnum,ec.class_cnname,tb.textbook_coverminimg as work_img,w.work_type as work_class,'0' as week_id,'0' as textbook_id,ar.audiorecords_type,ar.audiorecords_url,ar.audiorecords_id
                FROM app_audiorecords_workpages as aw
                LEFT JOIN app_audiorecords_work as w ON w.work_id = aw.work_id
                LEFT JOIN app_audiorecords as ar ON ar.audiorecords_id = aw.audiorecords_id
                LEFT JOIN app_student AS s ON s.student_id = w.student_id
                LEFT JOIN eas_classes as ec on ec.class_id = w.class_id
                LEFT JOIN app_textbook AS tb ON tb.textbook_id = ar.textbook_id
                WHERE {$datawhere}
                GROUP BY w.work_id
                HAVING {$having}
                UNION ALL ";

        if ($paramArray['school_nature'] == 'kid') {
            $sql .= "SELECT s.student_id,s.student_cnname,s.student_enname,s.student_branch,s.student_sex,s.student_img,'3' as work_type,
                        w.work_id,ca.textbookcate_name as work_name,w.work_score,FROM_UNIXTIME(w.work_createtime, '%Y-%m-%d %H:%i:%s') AS work_time,
                        IFNULL((select COUNT(dr.reviewlog_id) from app_dubbingworks_reviewlog as dr where dr.work_id = w.work_id and dr.work_type = '3' and dr.teacher_id > 0), 0) as is_comment,count(e.workpages_id) as work_thisnum,
                        (select count(wp.workpages_id) from app_ekidbook_work as ew,app_ekidbook_workpages as wp where ew.work_id = wp.work_id and ew.class_id = w.class_id and ew.student_id = w.student_id) as work_allnum,
                        (select count(ew.work_id) from app_ekidbook_work as ew where ew.class_id = w.class_id and ew.student_id = w.student_id) as work_dubnum,ec.class_cnname,ca.textbookcate_coverimg as work_img,w.work_type as work_class,es.week_id,es.textbook_id,'0' as audiorecords_type,'' as audiorecords_url,'0' as audiorecords_id
                    FROM app_ekidbook_work AS w
                    LEFT JOIN app_ekidbook_workpages AS e ON e.work_id = w.work_id
                    LEFT JOIN app_ekidbook_steps AS es ON es.steps_id = w.steps_id
                    LEFT JOIN app_ekidbook_textbook AS te ON te.textbook_id = es.textbook_id
                    LEFT JOIN app_student AS s ON s.student_id = w.student_id
                    LEFT JOIN app_ekidbook_textbookcate AS ca ON ca.textbookcate_id = te.textbookcate_id
                    LEFT JOIN eas_classes as ec on ec.class_id = w.class_id
                    WHERE {$datawhere}
                    GROUP BY w.work_id
                    HAVING {$having}
                    ORDER BY work_time DESC
                    LIMIT {$pagestart},{$num}";
        } else {
            $sql .= "SELECT s.student_id,s.student_cnname,s.student_enname,s.student_branch,s.student_sex,s.student_img,'2' as work_type,
                            w.work_id,pb.picbooks_title as work_name,w.work_score,FROM_UNIXTIME(w.work_createtime,'%Y-%m-%d %H:%i') as work_time,
                            IFNULL((select COUNT(dr.reviewlog_id) from app_dubbingworks_reviewlog as dr where dr.work_id = w.work_id and dr.work_type = '2' and dr.teacher_id > 0), 0) as is_comment,count(pw.workpages_id) as work_thisnum,
                            (select count(pw.workpages_id) from app_picbooks_work as wk,app_picbooks_workpages as pw where wk.work_id = pw.work_id and wk.class_id = w.class_id and wk.student_id = w.student_id) as work_allnum,
                            (select count(wk.work_id) from app_picbooks_work as wk where wk.class_id = w.class_id and wk.student_id = w.student_id) as work_dubnum,ec.class_cnname,pb.picbooks_imgurl as work_img,w.work_type as work_class,'0' as week_id,'0' as textbook_id,'0' as audiorecords_type,'' as audiorecords_url,'0' as audiorecords_id
                    FROM app_picbooks_work as w
                    LEFT JOIN app_picbooks_workpages AS pw ON pw.work_id = w.work_id
                    LEFT JOIN app_picbooks as pb ON pb.picbooks_id = w.picbooks_id
                    LEFT JOIN app_student AS s ON s.student_id = w.student_id
                    LEFT JOIN eas_classes as ec on ec.class_id = w.class_id
                    WHERE {$datawhere}
                    GROUP BY w.work_id
                    HAVING {$having}
                    ORDER BY work_time DESC
                    LIMIT {$pagestart},{$num}";
        }
        $dataList = $this->Show_css->selectClear($sql);

        if($dataList){
            foreach($dataList as &$val){
                if ($val['work_type'] == '1') {
                    $audioList = $this->Show_css->selectClear("select w.workpages_audio,r.resource_imgurl as work_imgurl from app_audiorecords_workpages as w,app_audiorecords_resource as r where w.resource_id = r.resource_id and w.work_id = '{$val['work_id']}'");
                    $thumb = $this->Show_css->selectOne("select thumb_num,dynamic_id from ptc_dynamic_audio where awork_id = '{$val['work_id']}'");
                    $val['thumb'] = $thumb['thumb_num'];
                    $val['dynamic_id'] = $thumb['dynamic_id'];
                    $val['work_class'] = '0';


                    if($val['audiorecords_type']==0){
                        $val['work_img']=$val['audiorecords_url'].'?x-oss-process=video/snapshot,t_10000,m_fast,w_488,h_273,f_png';
                    }else{
                        $img = $this->Show_css->selectOne("select resource_imgurl from app_audiorecords_resource where audiorecords_id = '{$val['audiorecords_id']}'");
                        $val['work_img']=$img['resource_imgurl'];
                    }
                } elseif ($val['work_type'] == '2') {
                    $audioList = $this->Show_css->selectClear("select w.workpages_audio,p.pages_imageurl as work_imgurl from app_picbooks_workpages as w,app_picbooks_pages as p where w.pages_id = p.pages_id and w.work_id = '{$val['work_id']}'");
                    $thumb = $this->Show_css->selectOne("select thumb_num,dynamic_id from ptc_dynamic_audio where pwork_id = '{$val['work_id']}'");
                    $val['thumb'] = $thumb['thumb_num'];
                    $val['dynamic_id'] = $thumb['dynamic_id'];
                    $val['work_class'] = '0';
                } elseif ($val['work_type'] == '3') {
                    $audioList = $this->Show_css->selectClear("select w.workpages_audio,m.medias_img as work_imgurl from app_ekidbook_workpages as w,app_ekidbook_steps_medias as m where w.medias_id = m.medias_id and w.work_id = '{$val['work_id']}'");
                    $thumb = $this->Show_css->selectOne("select thumb_num,dynamic_id from ptc_dynamic_audio where ework_id = '{$val['work_id']}'");
                    $val['thumb'] = $thumb['thumb_num'];
                    $val['dynamic_id'] = $thumb['dynamic_id'];
                    $img = $this->Show_css->getFieldOne("app_ekidbook_textbook_week", "week_img", "week_id = '{$val['week_id']}' and textbook_id = '{$val['textbook_id']}'");
                    $val['work_img'] = $img['week_img'].'?x-oss-process=style/wmxa';
                    if($val['work_class'] == '0'){
                        $num = $this->Show_css->selectOne("select count(*) as num from app_ekidbook_workpages where work_id = '{$val['work_id']}'");
                        $all = $this->Show_css->selectOne("select sum(workpages_score) as allscore from app_ekidbook_workpages where work_id = '{$val['work_id']}'");
                        $val['work_score'] = ceil($all['allscore']/$num['num']);
                    }
                }
                if ($audioList) {
                    $work = array();
                    foreach ($audioList as $key => $value) {
                        $work[$key]['workpages_audio'] = $value['workpages_audio'];
                        $work[$key]['work_imgurl'] = $value['work_imgurl'];
                    }
                    $val['work_audio'] = json_encode($work);
                } else {
                    $val['work_audio'] = '';
                }


            }
        }

        return $dataList ? $dataList : array();
    }

    //获取作品详情
    function getWorksDetailApi($paramArray)
    {
        if ($paramArray['work_type'] == '1') {
            $sql = "SELECT  s.student_id,s.student_cnname,s.student_enname,s.student_branch,s.student_sex,s.student_img,'1' as work_type,
                            w.work_id,ar.audiorecords_name as work_name,tb.textbook_coverminimg as work_img,w.work_score,w.work_thumbs,
                            FROM_UNIXTIME(w.work_createtime,'%Y-%m-%d %H:%i') as work_time,count(aw.workpages_id) as work_thisnum,
                            (select count(aw.workpages_id) from app_audiorecords_work as wk,app_audiorecords_workpages as aw where wk.work_id = aw.work_id and wk.class_id = w.class_id and wk.student_id = w.student_id) as work_allnum,
                            (select count(wk.work_id) from app_audiorecords_work as wk where wk.class_id = w.class_id and wk.student_id = w.student_id) as work_dubnum,
                            (select sum(wk.work_thumbs) from app_audiorecords_work as wk where wk.class_id = w.class_id and wk.student_id = w.student_id) as thumbs_num,w.work_type as work_class,'0' as week_id,'0' as textbook_id,ar.audiorecords_type,ar.audiorecords_url,ar.audiorecords_id,w.class_id
                    FROM app_audiorecords_workpages as aw
                    LEFT JOIN app_audiorecords_work as w ON w.work_id = aw.work_id
                    LEFT JOIN app_audiorecords as ar ON ar.audiorecords_id = aw.audiorecords_id
                    LEFT JOIN app_student AS s ON s.student_id = w.student_id
                    LEFT JOIN app_textbook AS tb ON tb.textbook_id = ar.textbook_id
                    WHERE w.work_id = '{$paramArray['work_id']}'";
        } elseif ($paramArray['work_type'] == '2') {
            $sql = "SELECT s.student_id,s.student_cnname,s.student_enname,s.student_branch,s.student_sex,s.student_img,'2' as work_type,
                            w.work_id,pb.picbooks_title as work_name,pb.picbooks_imgurl as work_img,w.work_score,w.work_thumbs,
                            FROM_UNIXTIME(w.work_createtime,'%Y-%m-%d %H:%i') as work_time,IF(w.work_type = 0,count(pw.workpages_id), 1) as work_thisnum,
                            (select count(pw.workpages_id) from app_picbooks_work as wk,app_picbooks_workpages as pw where wk.work_id = pw.work_id and wk.class_id = w.class_id and wk.student_id = w.student_id) as work_allnum,
                            (select count(wk.work_id) from app_picbooks_work as wk where wk.class_id = w.class_id and wk.student_id = w.student_id) as work_dubnum,
                            (select sum(wk.work_thumbs) from app_picbooks_work as wk where wk.class_id = w.class_id and wk.student_id = w.student_id) as thumbs_num,w.work_type as work_class,'0' as week_id,'0' as textbook_id,'0' as audiorecords_type,'' as audiorecords_url,'0' as audiorecords_id,w.class_id
                    FROM app_picbooks_work as w
                    LEFT JOIN app_picbooks_workpages AS pw ON pw.work_id = w.work_id
                    LEFT JOIN app_picbooks as pb ON pb.picbooks_id = w.picbooks_id
                    LEFT JOIN app_student AS s ON s.student_id = w.student_id
                    WHERE w.work_id = '{$paramArray['work_id']}'";
        } elseif ($paramArray['work_type'] == '3') {
            $sql = "SELECT s.student_id,s.student_cnname,s.student_enname,s.student_branch,s.student_sex,s.student_img,'3' as work_type,
                        w.work_id,ca.textbookcate_name as work_name,ca.textbookcate_coverimg as work_img,w.work_score,w.work_thumbs,
                        FROM_UNIXTIME(w.work_createtime, '%Y-%m-%d %H:%i:%s') AS work_time,IF(w.work_type = 0,count(e.workpages_id), 1) as work_thisnum,
                        (select count(wp.workpages_id) from app_ekidbook_work as ew,app_ekidbook_workpages as wp where ew.work_id = wp.work_id and ew.class_id = w.class_id and ew.student_id = w.student_id) as work_allnum,
                        (select count(ew.work_id) from app_ekidbook_work as ew where ew.class_id = w.class_id and ew.student_id = w.student_id) as work_dubnum,
                        (select sum(ew.work_thumbs) from app_ekidbook_work as ew where ew.class_id = w.class_id and ew.student_id = w.student_id) as thumbs_num,w.work_type as work_class,es.week_id,es.textbook_id,'0' as audiorecords_type,'' as audiorecords_url,'0' as audiorecords_id,w.class_id
                    FROM app_ekidbook_work AS w
                    LEFT JOIN app_ekidbook_workpages AS e ON e.work_id = w.work_id
                    LEFT JOIN app_ekidbook_steps AS es ON es.steps_id = w.steps_id
                    LEFT JOIN app_ekidbook_textbook AS te ON te.textbook_id = es.textbook_id
                    LEFT JOIN app_student AS s ON s.student_id = w.student_id
                    LEFT JOIN app_ekidbook_textbookcate AS ca ON ca.textbookcate_id = te.textbookcate_id
                    WHERE w.work_id = '{$paramArray['work_id']}'";
        }
        $dataOne = $this->Show_css->selectOne($sql);
        if ($dataOne) {
            if ($dataOne['work_type'] == '1') {
                $thumb = $this->Show_css->selectOne("select thumb_num,dynamic_id from ptc_dynamic_audio where awork_id = '{$dataOne['work_id']}'");
                $dataOne['thumb_num'] = $thumb['thumb_num'];
                $dataOne['work_thumbs'] = $thumb['thumb_num'];
                if(!$thumb['thumbs_num']){
                    $dataOne['thumbs_num'] = '0';
                    $dataOne['work_thumbs'] = '0';
                }
                $allthumb = $this->Show_css->selectOne("select sum(thumb_num) as num from ptc_dynamic_audio as a left join app_audiorecords_work as e on e.work_id = a.awork_id where student_id = '{$paramArray['student_id']}' and awork_id > '0'");
                $dataOne['thumbs_num'] = $allthumb['num'];
                if(!$dataOne['thumbs_num']){
                    $dataOne['thumbs_num'] = '0';
                }
                $audioList = $this->Show_css->selectClear("select w.workpages_audio,r.resource_imgurl as work_imgurl from app_audiorecords_workpages as w,app_audiorecords_resource as r where w.resource_id = r.resource_id and w.work_id = '{$dataOne['work_id']}'");
                $dataOne['work_class'] = '0';

                if($dataOne['audiorecords_type']==0){
                    $dataOne['work_img']=$dataOne['audiorecords_url'].'?x-oss-process=video/snapshot,t_10000,m_fast,w_488,h_273,f_png';
                }else{
                    $img = $this->Show_css->selectOne("select resource_imgurl from app_audiorecords_resource where audiorecords_id = '{$dataOne['audiorecords_id']}'");
                    $dataOne['work_img']=$img['resource_imgurl'];
                }
            } elseif ($dataOne['work_type'] == '2') {
                $thumb = $this->Show_css->selectOne("select thumb_num,dynamic_id from ptc_dynamic_audio where pwork_id = '{$dataOne['work_id']}'");
                $dataOne['thumb_num'] = $thumb['thumb_num'];
                $dataOne['work_thumbs'] = $thumb['thumb_num'];
                if(!$thumb['thumbs_num']){
                    $dataOne['thumbs_num'] = '0';
                    $dataOne['work_thumbs'] = '0';
                }
                $allthumb = $this->Show_css->selectOne("select sum(thumb_num) as num from ptc_dynamic_audio as a left join app_picbooks_work as e on e.work_id = a.pwork_id where student_id = '{$paramArray['student_id']}' and pwork_id > '0'");
                $dataOne['thumbs_num'] = $allthumb['num'];
                if(!$dataOne['thumbs_num']){
                    $dataOne['thumbs_num'] = '0';
                }
                $audioList = $this->Show_css->selectClear("select w.workpages_audio,p.pages_imageurl as work_imgurl from app_picbooks_workpages as w,app_picbooks_pages as p where w.pages_id = p.pages_id and w.work_id = '{$dataOne['work_id']}'");
                $dataOne['work_class'] = '0';
            } elseif ($dataOne['work_type'] == '3') {
                $thumb = $this->Show_css->selectOne("select thumb_num,dynamic_id from ptc_dynamic_audio where ework_id = '{$dataOne['work_id']}'");
                $dataOne['thumb_num'] = $thumb['thumb_num'];
                $dataOne['work_thumbs'] = $thumb['thumb_num'];
                if(!$thumb['thumbs_num']){
                    $dataOne['thumbs_num'] = '0';
                    $dataOne['work_thumbs'] = '0';
                }
                $allthumb = $this->Show_css->selectOne("select sum(thumb_num) as num from ptc_dynamic_audio as a left join app_ekidbook_work as e on e.work_id = a.ework_id where e.student_id = '{$paramArray['student_id']}' and ework_id > '0'");
                $dataOne['thumbs_num'] = $allthumb['num'];
                if(!$dataOne['thumbs_num']){
                    $dataOne['thumbs_num'] = '0';
                }
                $img = $this->Show_css->getFieldOne("app_ekidbook_textbook_week", "week_img", "week_id = '{$dataOne['week_id']}' and textbook_id = '{$dataOne['textbook_id']}'");
                $dataOne['work_img'] = $img['week_img'].'?x-oss-process=style/wmxa';
                if($dataOne['work_class'] == '0'){
                    $num = $this->Show_css->selectOne("select count(*) as num from app_ekidbook_workpages where work_id = '{$dataOne['work_id']}'");
                    $all = $this->Show_css->selectOne("select sum(workpages_score) as allscore from app_ekidbook_workpages where work_id = '{$dataOne['work_id']}'");
                    $dataOne['work_score'] = ceil($all['allscore']/$num['num']);
                }

                if($dataOne['work_class'] == '1'){
                    $audioList = $this->Show_css->selectClear("
SELECT
	k.work_audio as workpages_audio,
	m.medias_img AS work_imgurl 
FROM
	app_ekidbook_work AS k,
	app_ekidbook_steps AS s,
	app_ekidbook_steps_medias AS m 
WHERE
	k.steps_id = s.steps_id 
	AND s.steps_id = m.steps_id 
	AND k.work_id = '{$dataOne['work_id']}'");
                }else{
                    $audioList = $this->Show_css->selectClear("select w.workpages_audio,m.medias_img as work_imgurl from app_ekidbook_workpages as w,app_ekidbook_steps_medias as m where w.medias_id = m.medias_id and w.work_id = '{$dataOne['work_id']}'");

                }
            }
            if ($audioList) {
                $work = array();
                foreach ($audioList as $key => $value) {
                    $work[$key]['workpages_audio'] = $value['workpages_audio'];
                    $work[$key]['work_imgurl'] = $value['work_imgurl'];
                }
                $dataOne['work_audio'] = json_encode($work);
            } else {
                $dataOne['work_audio'] = '';
            }
            $workArr = array();
            $workArr['work_id'] = $dataOne['work_id'];
            $workArr['work_type'] = $dataOne['work_type'];
            $Model = new ClassModel();
            $dataOne['reviewList'] = $Model->getCommentList('', '', '', '', '', $workArr);

            if($dataOne['work_score'] >= '1' && $dataOne['work_score'] <= '10'){
                $dataOne['entext'] = 'A bold attempt is half success.';
                $dataOne['cntext'] = '勇敢的尝试是成功的一半。';
            }elseif($dataOne['work_score'] >= '11' && $dataOne['work_score'] <= '20'){
                $dataOne['entext'] = 'Keep up the good work!';
                $dataOne['cntext'] = '继续努力！';
            }elseif($dataOne['work_score'] >= '21' && $dataOne['work_score'] <= '30'){
                $dataOne['entext'] = 'Give it your best shot!';
                $dataOne['cntext'] = '你要竭尽全力唷！';
            }elseif($dataOne['work_score'] >= '31' && $dataOne['work_score'] <= '40'){
                $dataOne['entext'] = 'Go the extra mile.';
                $dataOne['cntext'] = '多付出一份努力哦！';
            }elseif($dataOne['work_score'] >= '41' && $dataOne['work_score'] <= '50'){
                $dataOne['entext'] = 'Practice makes perfect.';
                $dataOne['cntext'] = '熟能生巧。';
            }elseif($dataOne['work_score'] >= '51' && $dataOne['work_score'] <= '60'){
                $dataOne['entext'] = 'As long as you are willing to work hard for yourself, the world will surprise you.';
                $dataOne['cntext'] = '只要你愿意为自己努力，世界会给你惊喜！';
            }elseif($dataOne['work_score'] >= '61' && $dataOne['work_score'] <= '70'){
                $dataOne['entext'] = 'You will do better next time. Do your best!';
                $dataOne['cntext'] = '下次你会表现得更好。尽全力去做吧！';
            }elseif($dataOne['work_score'] >= '71' && $dataOne['work_score'] <= '80'){
                $dataOne['entext'] = 'It was a nice try! Keep up the good work.';
                $dataOne['cntext'] = '这是一次很棒的尝试哦！继续加油吧！';
            }elseif($dataOne['work_score'] >= '81' && $dataOne['work_score'] <= '90'){
                $dataOne['entext'] = 'Two thumbs up!';
                $dataOne['cntext'] = '给你两个赞！';
            }elseif($dataOne['work_score'] >= '91' && $dataOne['work_score'] <= '100'){
                $dataOne['entext'] = 'Brilliant! So proud of you!';
                $dataOne['cntext'] = '太棒了！为你骄傲！';
            }

        }

        return $dataOne;
    }

    //学员作品点评/回复
    function addWorksReplyReviewApi($paramArray)
    {
        $this->Show_css->query("SET NAMES utf8mb4");
        $data = array();
        if (!empty($paramArray['reviewlog_id'])) {
            $reviewOne = $this->Show_css->getFieldOne("app_dubbingworks_reviewlog", "work_id,first_reviewlog_id", "reviewlog_id = '{$paramArray['reviewlog_id']}'");
            $data['work_id'] = $reviewOne['work_id'];
            $data['from_reviewlog_id'] = $paramArray['reviewlog_id'];
            if ($reviewOne['first_reviewlog_id']) {
                $data['first_reviewlog_id'] = $reviewOne['first_reviewlog_id'];
            } else {
                $data['first_reviewlog_id'] = $paramArray['reviewlog_id'];
            }
        } elseif (!empty($paramArray['work_id'])) {
            $data['work_id'] = $paramArray['work_id'];
        }
        $data['work_type'] = $paramArray['work_type'];
        $data['teacher_id'] = $paramArray['teacher_id'];
        $data['reviewlog_content'] = $paramArray['reviewlog_content'] ? emoji_encode($paramArray['reviewlog_content']) : '';
        $data['reviewlog_audiourl'] = $paramArray['reviewlog_audiourl'];
        $data['reviewlog_audiotime'] = $paramArray['reviewlog_audiotime'];
        $data['reviewlog_time'] = time();
        $dataid = $this->Show_css->insertData("app_dubbingworks_reviewlog", $data);
        if ($dataid) {
            $this->Show_css->updateData("app_dubbingworks_reviewlog", "work_id = '{$data['work_id']}' and member_id > 0", array("reviewlog_isreply" => "1"));
            $this->error = 0;
            $this->errortip = "点评成功";
            return true;
        } else {
            $this->error = 1;
            $this->errortip = "点评失败";
            return true;
        }
    }

    //获取中心端巡检任务配置
    function getInspecTasksApi($paramArray)
    {
        if ($this->schoolOne['school_mold']) {
            $mold = "co.course_mold > 0";
        } else {
            $mold = "co.course_mold = 0";
        }
        $datawhere = "t.school_id = '{$paramArray['school_id']}' and {$mold} and t.tasklog_year = '{$this->taskyear}'";
        if($this->semester == '1'){
            $datawhere .= " and IF(co.course_mold <> 1, co.course_branch IN ('KIRC1', 'KIRC3', 'KIRC5'), (co.course_cnname LIKE '%上学期%' or co.course_cnname LIKE '%上學期%'))";
        }else{
            $datawhere .= " and IF(co.course_mold <> 1, co.course_branch IN ('KIRC2', 'KIRC4', 'KIRC6'), (co.course_cnname LIKE '%下学期%' or co.course_cnname LIKE '%下學期%'))";
        }
        if ($paramArray['type'] == '1') {
            $datawhere .= " and exists (select 1 from eas_course_units as cu where cu.course_branch = co.course_branch)";
            if (!empty($paramArray['course_branch'])) {
                $datawhere .= " and co.course_branch = '{$paramArray['course_branch']}'";
            }

            $sql = "SELECT 
                        u.units_id,u.units_name,u.units_sort,u.course_branch
                    FROM 
                        eas_course_units as u
                        LEFT JOIN eas_course_times as t ON t.units_id = u.units_id
                        LEFT JOIN eas_course as c ON c.course_branch = u.course_branch
                    WHERE 
                        t.course_branch = (SELECT
                                            co.course_branch
                                        FROM
                                            app_school_taskslog as t
                                            LEFT JOIN eas_course AS co ON co.course_id = t.course_id
                                        WHERE
                                            {$datawhere}
                                        GROUP BY
                                            t.course_id
                                        ORDER BY
                                            count(t.course_id) DESC
                                        LIMIT 1) and exists (select 1 from app_school_taskslog as st,eas_course_times as ct where st.times_id = ct.times_id and st.school_id = '{$paramArray['school_id']}' and st.course_id = c.course_id and ct.units_id = u.units_id and st.tasklog_year = '{$this->taskyear}')
                    GROUP BY
                        t.units_id
                    ORDER BY
                        t.times_sort";
        } else {
            $sql = "SELECT 
                        t.times_name,t.times_sort,t.course_branch,u.units_sort,
                        IF((SELECT ct.times_id 
                            FROM 
                                app_school_taskslog as tl,
                                eas_course as co,
                                eas_course_times as ct 
                            WHERE 
                                tl.course_id = co.course_id and tl.times_id = ct.times_id and co.course_branch = t.course_branch
                                and tl.school_id = '{$paramArray['school_id']}' and {$mold} and tl.tasklog_year = '{$this->taskyear}'
                            ORDER BY 
                                ct.times_sort DESC 
                            LIMIT 1) = t.times_id, '1', '0') as is_default
                    FROM 
                        eas_course_times as t
                        LEFT JOIN eas_course_units as u ON u.units_id = t.units_id
                    WHERE 
                        t.course_branch = (SELECT
                                            co.course_branch
                                        FROM
                                            app_school_taskslog as t
                                            LEFT JOIN eas_course AS co ON co.course_id = t.course_id
                                        WHERE
                                            {$datawhere}
                                        GROUP BY
                                            t.course_id
                                        ORDER BY
                                            count(t.course_id) DESC
                                        LIMIT 1)";
        }
        $dataList = $this->Show_css->selectClear($sql);
        if ($dataList) {
            foreach ($dataList as &$value) {
                if ($paramArray['type'] == '1') {
                    $unitSort = $this->Show_css->selectOne("SELECT cu.units_sort FROM eas_course_times as ts,eas_course_units as cu,eas_course as c WHERE ts.units_id = cu.units_id and ts.course_branch = c.course_branch and ts.course_branch = '{$value['course_branch']}' and exists (select 1 from app_school_taskslog as st,eas_course_times as ct where st.times_id = ct.times_id and st.school_id = '{$paramArray['school_id']}' and st.course_id = c.course_id and ct.units_id = cu.units_id and st.tasklog_year = '{$this->taskyear}') ORDER BY ts.times_sort DESC");
                    if ($unitSort['units_sort'] == $value['units_sort']) {
                        $value['is_select'] = '1';
                    } else {
                        $value['is_select'] = '0';
                    }
                    $cowhere = "u.course_branch = co.course_branch and u.units_sort = '{$value['units_sort']}'";
                    if($this->semester == '1'){
                        $cowhere .= " and IF(co.course_mold <> 1, co.course_branch IN ('KIRC1', 'KIRC3', 'KIRC5'), (co.course_cnname LIKE '%上学期%' or co.course_cnname LIKE '%上學期%'))";
                    }else{
                        $cowhere .= " and IF(co.course_mold <> 1, co.course_branch IN ('KIRC2', 'KIRC4', 'KIRC6'), (co.course_cnname LIKE '%下学期%' or co.course_cnname LIKE '%下學期%'))";
                    }
                    $value['times_list'] = $this->Show_css->selectClear("SELECT times_name,times_sort FROM eas_course_times WHERE units_id = '{$value['units_id']}' ");
                    $value['course_list'] = $this->Show_css->selectClear("SELECT 
                                                            co.course_cnname,co.course_branch 
                                                        FROM 
                                                            eas_course_units as u,
                                                            eas_course as co 
                                                        WHERE 
                                                            {$cowhere} and exists (select 1 from eas_course_inspectasks as t where t.course_branch = co.course_branch and t.inspectasks_status = '1')
                                                        ORDER BY
                                                            co.course_branch");
                } else {
                    $value['is_complete'] = '0';
                    $inspectype = $this->Show_css->selectClear("SELECT t.inspectype_id,t.inspectype_accordnum
                            FROM 
                                eas_course_inspectasks as a
                                LEFT JOIN eas_course_inspec_lsscore as b ON b.lsscore_id = a.lsscore_id
                                LEFT JOIN eas_course_inspectype as t ON t.inspectype_id = b.inspectype_id
                                LEFT JOIN eas_course_times as ct ON ct.course_branch = a.course_branch
                                LEFT JOIN eas_classes_tasks as ts ON ts.times_id = ct.times_id
                            WHERE 
                                ts.school_id = '{$paramArray['school_id']}' and t.inspectype_passage = '{$this->schoolOne['school_nature']}' and ct.times_sort = '{$value['times_sort']}' 
                                and a.inspectasks_type = '2' and t.inspectype_status = '1' and a.inspectasks_status = '1' and b.lsscore_status = '1'
                                and exists (select 1 from eas_course_inspecitem_splpost as p where p.inspecitem_id = a.inspecitem_id and p.post_id = '{$this->postbeOne['post_id']}')
                            GROUP BY
                                t.inspectype_id");
                    if ($inspectype) {
                        $is_complete = 0;
                        foreach ($inspectype as $item) {
                            $param = array();
                            $param['inspectype_id'] = $item['inspectype_id'];
                            $param['times_sort'] = $value['times_sort'];
                            $recordList = $this->getLectureRecordOne($param);
                            if ($recordList && count($recordList) >= $item['inspectype_accordnum']) {
                                $is_complete++;
                            }
                        }
                        if (count($inspectype) == $is_complete) {
                            $value['is_complete'] = '1';
                        } else {
                            $value['is_complete'] = '0';
                        }
                    }
                }
            }
            if ($paramArray['type'] !== '1' && empty($paramArray['is_recheck'])) {
                $complete = array_column($dataList, 'is_complete');
                $sort = array_column($dataList, 'times_sort');
                array_multisort($complete, SORT_ASC, $sort, SORT_ASC, $dataList);
            }
        } else {
            $dataList = array();
        }

        return $dataList;
    }

    //获取项目巡检
    function getInspecItemApi($paramArray)
    {
        $datawhere = "ct.school_id = '{$paramArray['school_id']}' and a.course_branch = '{$paramArray['course_branch']}' and u.units_sort = '{$paramArray['units_sort']}' and a.inspectasks_type = '1' and a.inspectasks_status = '1' and i.inspecitem_status = '1' and t.inspectype_status = '1'";
        $datawhere .= " and exists (select 1 from eas_course_inspecitem_splpost as p where p.inspecitem_id = a.inspecitem_id and p.post_id = '{$this->postbeOne['post_id']}')";
        if (!empty($paramArray['keyword'])) {
            $datawhere .= " and (i.inspecitem_name like '%{$paramArray['keyword']}%')";
        }
        $having = "1=1";
        if (isset($paramArray['check_status']) && $paramArray['check_status'] == '0') {
            $having .= " and class_nums <> check_allnums";
        } elseif (isset($paramArray['check_status']) && $paramArray['check_status'] == '1') {
            $having .= " and class_nums = check_allnums";
        }
        $day = date("Ymd");
        $daywhere = "c.school_branch = '{$this->schoolOne['school_branch']}' and c.course_branch = a.course_branch and cu.units_id = a.units_id and cu.units_sort = '{$paramArray['units_sort']}'";
        $daywhere .= " and c.class_stdate <= '{$day}' and c.class_enddate >= '{$day}' and c.class_isdel = '0' and exists (select 1 from eas_classes_tasks as ct where ct.class_id = c.class_id)";
        if (in_array($this->schoolOne['school_class'], array("1", "3"))) {
            $daywhere .= " and (
                            c.class_naturejson = '' or c.class_naturejson = '{\"chinese\":\"1\",\"english\":\"1\",\"maths\":\"1\"}'
                            or (t.inspectype_classnature = 0 and (c.class_naturejson  like '%\"chinese\":\"1\"%' or c.class_naturejson like '%\"english\":\"1\"%' or c.class_naturejson like '%\"maths\":\"1\"%'))
                            or (FIND_IN_SET(1, t.inspectype_classnature) and c.class_naturejson like '%\"chinese\":\"1\"%')
                            or (FIND_IN_SET(2, t.inspectype_classnature) and c.class_naturejson like '%\"english\":\"1\"%')
                            or (FIND_IN_SET(3, t.inspectype_classnature) and c.class_naturejson like '%\"maths\":\"1\"%')
                        )";
        }
        $clwhere = $daywhere . " and (select cu.units_sort from eas_classes_tasks as ct,eas_course_times as t,eas_course_units as cu where ct.times_id = t.times_id and t.units_id = cu.units_id and ct.class_id = c.class_id and t.course_branch = a.course_branch order by t.times_sort desc limit 1) = '{$paramArray['units_sort']}'";

        $sql = "SELECT 
                    a.inspectasks_id,a.inspecitem_id,a.course_branch,i.inspecitem_name,i.inspecitem_content,t.inspectype_id,t.inspectype_name,u.units_sort,
                    IF((select count(distinct c.class_id) from eas_classes as c,eas_course_units as cu where {$clwhere}), 0, 1) as is_disables,
                    IFNULL((select count(distinct c.class_id) from eas_classes as c,eas_course_units as cu where {$daywhere}), 0) as class_nums,
                    IFNULL((select count(distinct pc.class_id) from eas_classes_inspec_patrolcheck as p,eas_classes_inspec_patrolcheck_splclass as pc,eas_course_times as tt,eas_course_units as cu,eas_classes as c 
                            where {$daywhere} and p.patrolcheck_id = pc.patrolcheck_id and p.school_id = ct.school_id and pc.class_id = c.class_id and pc.times_id = tt.times_id and tt.units_id = cu.units_id
                            and c.course_branch = a.course_branch and p.inspecitem_id = a.inspecitem_id and cu.units_id = a.units_id and cu.units_sort = u.units_sort and p.patrolcheck_status = '1'), 0) as check_allnums,
                    IFNULL((select count(distinct c.class_id) from eas_classes as c,eas_course_units as cu where {$daywhere}
                            and exists (select 1 from eas_classes_inspec_patrolcheck as p,eas_classes_inspec_patrolcheck_splclass as pc,eas_course_times as tt,eas_course_units as cu where p.patrolcheck_id = pc.patrolcheck_id and pc.class_id = c.class_id
                            and p.inspecitem_id = a.inspecitem_id and p.school_id = ct.school_id and pc.times_id = tt.times_id and tt.units_id = cu.units_id and cu.units_sort = u.units_sort and p.patrolcheck_status = '1'
                            and (p.patrolcheck_level <> '-1' or (p.patrolcheck_level = '-1' and (select y.patrolcheck_level from eas_classes_inspec_patrolcheck as y,eas_classes_inspec_patrolcheck_splclass as z where y.patrolcheck_id = z.patrolcheck_id and y.inspecitem_id = a.inspecitem_id and z.class_id = pc.class_id and y.patrolcheck_status = '1' order by y.patrolcheck_updatetime desc,y.patrolcheck_createtime desc limit 1) <> '-1')))), 0) as check_comnums,
                    IFNULL((select count(distinct c.class_id) from eas_classes as c,eas_course_units as cu where {$daywhere}
                            and exists (select 1 from eas_classes_inspec_patrolcheck as p,eas_classes_inspec_patrolcheck_splclass as pc,eas_course_times as tt,eas_course_units as cu where p.patrolcheck_id = pc.patrolcheck_id and pc.class_id = c.class_id
                            and p.inspecitem_id = a.inspecitem_id and p.school_id = ct.school_id and pc.times_id = tt.times_id and tt.units_id = cu.units_id and cu.units_sort = u.units_sort and p.patrolcheck_status = '1' and p.patrolcheck_level = '-1'
                            and (select y.patrolcheck_level from eas_classes_inspec_patrolcheck as y,eas_classes_inspec_patrolcheck_splclass as z where y.patrolcheck_id = z.patrolcheck_id and y.inspecitem_id = a.inspecitem_id and z.class_id = pc.class_id and y.patrolcheck_status = '1' order by y.patrolcheck_updatetime desc,y.patrolcheck_createtime desc limit 1) = '-1')), 0) as check_errnums
                FROM 
                    eas_course_inspectasks as a
                    LEFT JOIN eas_course_units as u ON u.units_id = a.units_id
                    LEFT JOIN eas_course_inspecitem as i ON i.inspecitem_id = a.inspecitem_id
                    LEFT JOIN eas_course_inspectype as t ON t.inspectype_id = i.inspectype_id
                    LEFT JOIN eas_course_times as ts ON ts.course_branch = a.course_branch and ts.units_id = u.units_id
                    LEFT JOIN eas_classes_tasks as ct ON ct.times_id = ts.times_id
                WHERE
                    {$datawhere}
                GROUP BY
	                a.inspecitem_id
                HAVING
                    {$having}
                ORDER BY
                    i.inspecitem_sort";

        $dataList = $this->Show_css->selectClear($sql);
        $inspectype = array();
        if ($dataList) {
            foreach ($dataList as $key => &$value) {
                $value['check_notnums'] = (string)($value['class_nums'] - $value['check_allnums']);
                if ($value['class_nums'] == $value['check_allnums']) {
                    if ($value['check_errnums']) {
                        $value['check_status'] = '-1';//异常检核
                    } else {
                        $value['check_status'] = '0';//新增检核
                    }
                } else {
                    $value['check_status'] = '1';//立即检核
                }
                if (!in_array($value['inspectype_id'], array_column($inspectype, 'inspectype_id'))) {
                    $inspectype[$key]['inspectype_id'] = $value['inspectype_id'];
                    $inspectype[$key]['inspectype_name'] = $value['inspectype_name'];
                }
            }
            foreach ($inspectype as &$item) {
                foreach ($dataList as $val) {
                    if ($item['inspectype_id'] == $val['inspectype_id']) {
                        $item['inspecitem_list'][] = $val;
                    }
                }
            }
        }

        $abnormalOne = $this->Show_css->selectClear("SELECT a.patrolcheck_id
FROM 
    eas_classes_inspec_patrolcheck as a
    LEFT JOIN eas_classes_inspec_patrolcheck_splclass as b ON b.patrolcheck_id = a.patrolcheck_id
    LEFT JOIN eas_course_times as c ON c.times_id = b.times_id 
    LEFT JOIN eas_course_units as u ON u.units_id = c.units_id 
    LEFT JOIN eas_course_inspectasks as d ON d.course_branch = c.course_branch and d.inspecitem_id = a.inspecitem_id and d.units_id = c.units_id
WHERE 
    a.school_id = '{$paramArray['school_id']}' and a.teacher_id = '{$paramArray['teacher_id']}' and a.patrolcheck_level = '-1' and a.patrolcheck_status = '1' and d.inspectasks_status = '1'
  and (select cu.units_sort from eas_classes_tasks as ct,eas_course_times as t,eas_course_units as cu where ct.times_id = t.times_id and t.units_id = cu.units_id and ct.class_id = b.class_id and t.course_branch = c.course_branch order by t.times_sort desc limit 1) <= u.units_sort
  and (select x.patrolcheck_level from eas_classes_inspec_patrolcheck as x,eas_classes_inspec_patrolcheck_splclass as y where x.patrolcheck_id = y.patrolcheck_id and x.inspecitem_id = a.inspecitem_id and y.class_id = b.class_id and x.patrolcheck_status = '1' order by x.patrolcheck_updatetime desc,x.patrolcheck_createtime desc limit 1) = '-1' 
GROUP BY 
    c.units_id,a.inspecitem_id,b.class_id");
        if ($abnormalOne) {
            $error_one = count($abnormalOne);
        } else {
            $error_one = 0;
        }
        $abnormalTwo = $this->Show_css->selectClear("SELECT a.lslistens_id
FROM 
    eas_classes_inspec_lslistens as a
    LEFT JOIN eas_course_times as c ON c.times_id = a.times_id 
    LEFT JOIN eas_course_units as u ON u.units_id = c.units_id 
    LEFT JOIN eas_course_inspectasks as d ON d.course_branch = c.course_branch and d.inspecitem_id = a.inspecitem_id and d.lsscore_id = a.lsscore_id
WHERE 
    a.school_id = '{$paramArray['school_id']}' and a.teacher_id = '{$paramArray['teacher_id']}' and a.lslistens_score < 70 and a.lslistens_status = '1' and d.inspectasks_status = '1'
  and (select cu.units_sort from eas_classes_tasks as ct,eas_course_times as t,eas_course_units as cu where ct.times_id = t.times_id and t.units_id = cu.units_id and ct.class_id = a.class_id and t.course_branch = c.course_branch order by t.times_sort desc limit 1) <= u.units_sort
  and (select x.lslistens_score from eas_classes_inspec_lslistens as x where x.inspecitem_id = a.inspecitem_id and x.class_id = a.class_id and x.times_id = a.times_id and x.lslistens_status = '1' order by x.lslistens_updatetime desc,x.lslistens_createtime desc limit 1) < 70 
GROUP BY 
    a.inspecitem_id,a.class_id,a.times_id");
        if ($abnormalTwo) {
            $error_two = count($abnormalTwo);
        } else {
            $error_two = 0;
        }
        $draft = $this->Show_css->selectOne("SELECT count(DISTINCT a.patrolcheck_id) as num 
FROM 
    eas_classes_inspec_patrolcheck as a
    LEFT JOIN eas_classes_inspec_patrolcheck_splclass as b ON b.patrolcheck_id = a.patrolcheck_id
    LEFT JOIN eas_course_times as d ON d.times_id = b.times_id 
    LEFT JOIN eas_course_units as u ON u.units_id = d.units_id
    LEFT JOIN eas_course_inspectasks as t ON t.course_branch = d.course_branch and t.inspecitem_id = a.inspecitem_id and t.units_id = d.units_id
WHERE 
    a.school_id = '{$paramArray['school_id']}' and a.teacher_id = '{$paramArray['teacher_id']}' and u.units_sort = '{$paramArray['units_sort']}' and a.patrolcheck_status = '0' and t.inspectasks_status = '1'");
        $data['draft_nums'] = $draft['num'] ?: 0;
        $data['error_nums'] = $error_one + $error_two;
        $data['list'] = $inspectype;
        return $data;
    }

    //获取项目概览
    function getItemOutlineApi($paramArray)
    {
        $datawhere = "ct.school_id = '{$paramArray['school_id']}' and a.course_branch = '{$paramArray['course_branch']}' and u.units_id = '{$paramArray['units_id']}'";
        $datawhere .= " and a.inspectasks_type = '1' and a.inspectasks_status = '1' and i.inspecitem_status = '1' and t.inspectype_status = '1'";
//        $datawhere .= " and exists (select 1 from eas_course_inspecitem_splpost as p where p.inspecitem_id = a.inspecitem_id and p.post_id = '{$this->postbeOne['post_id']}')";

        if (!empty($paramArray['inspecitem_id'])) {
            $datawhere .= " and a.inspecitem_id = '{$paramArray['inspecitem_id']}'";
        }
        if (!empty($paramArray['inspectype_id'])) {
            $datawhere .= " and t.inspectype_id = '{$paramArray['inspectype_id']}'";
        }
        $day = date("Ymd");
        $daywhere = "c.school_branch = '{$this->schoolOne['school_branch']}' and c.course_branch = x.course_branch and x.units_id = u.units_id and x.inspecitem_id = a.inspecitem_id and c.class_stdate <= '{$day}' and c.class_enddate >= '{$day}' and c.class_isdel = '0' and x.inspectasks_type = '1' and x.inspectasks_status = '1'";
//        $daywhere .= " and exists (select 1 from eas_course_inspecitem_splpost as p where p.inspecitem_id = x.inspecitem_id and p.post_id = '{$this->postbeOne['post_id']}')";
        $daywhere .= " and exists (select 1 from eas_classes_tasks as ct where ct.class_id = c.class_id)";
        if (in_array($this->schoolOne['school_class'], array("1", "3"))) {
            $daywhere .= " and (
                            c.class_naturejson = '' or c.class_naturejson = '{\"chinese\":\"1\",\"english\":\"1\",\"maths\":\"1\"}'
                            or (t.inspectype_classnature = 0 and (c.class_naturejson like '%\"chinese\":\"1\"%' or c.class_naturejson like '%\"english\":\"1\"%' or c.class_naturejson like '%\"maths\":\"1\"%'))
                            or (FIND_IN_SET(1, t.inspectype_classnature) and c.class_naturejson like '%\"chinese\":\"1\"%')
                            or (FIND_IN_SET(2, t.inspectype_classnature) and c.class_naturejson like '%\"english\":\"1\"%')
                            or (FIND_IN_SET(3, t.inspectype_classnature) and c.class_naturejson like '%\"maths\":\"1\"%')
                        )";
        }

        if (!empty($paramArray['p'])) {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (!empty($paramArray['num'])) {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $c_sql = "select count(distinct c.class_id) from eas_course_inspectasks as x,eas_classes as c where {$daywhere}";

        $sql = "SELECT 
                    a.inspecitem_id,i.inspecitem_name,t.inspectype_name,
                    IFNULL(({$c_sql}), 0) as class_nums,
                    IFNULL((select count(distinct pc.class_id) from eas_classes_inspec_patrolcheck as p,eas_classes_inspec_patrolcheck_splclass as pc,eas_course_times as tt,eas_classes as c,eas_course_inspectasks as x 
                            where p.patrolcheck_id = pc.patrolcheck_id and p.inspecitem_id = a.inspecitem_id and p.school_id = ct.school_id and pc.class_id = c.class_id and pc.times_id = tt.times_id and tt.units_id = x.units_id
                            and c.course_branch = x.course_branch and p.inspecitem_id = x.inspecitem_id and x.units_id = u.units_id and p.patrolcheck_status = '1' and x.inspectasks_status = '1'), 0) as check_allnums,
                    IFNULL(({$c_sql}
                            and exists (select 1 from eas_classes_inspec_patrolcheck as p,eas_classes_inspec_patrolcheck_splclass as pc,eas_course_times as tt where p.patrolcheck_id = pc.patrolcheck_id and pc.class_id = c.class_id
                            and p.inspecitem_id = x.inspecitem_id and p.school_id = ct.school_id and pc.times_id = tt.times_id and tt.units_id = x.units_id and p.patrolcheck_status = '1' and p.patrolcheck_level = '1'
                            and (select y.patrolcheck_level from eas_classes_inspec_patrolcheck as y,eas_classes_inspec_patrolcheck_splclass as z,eas_course_times as ti where y.patrolcheck_id = z.patrolcheck_id and z.times_id = ti.times_id and y.inspecitem_id = a.inspecitem_id and z.class_id = pc.class_id and ti.units_id = tt.units_id and y.patrolcheck_status = '1' order by y.patrolcheck_updatetime desc,y.patrolcheck_createtime desc limit 1) = '1')), 0) as check_finenums,
                    IFNULL(({$c_sql}
                            and exists (select 1 from eas_classes_inspec_patrolcheck as p,eas_classes_inspec_patrolcheck_splclass as pc,eas_course_times as tt where p.patrolcheck_id = pc.patrolcheck_id and pc.class_id = c.class_id
                            and p.inspecitem_id = x.inspecitem_id and p.school_id = ct.school_id and pc.times_id = tt.times_id and tt.units_id = x.units_id and p.patrolcheck_status = '1' and p.patrolcheck_level = '0'
                            and (select y.patrolcheck_level from eas_classes_inspec_patrolcheck as y,eas_classes_inspec_patrolcheck_splclass as z,eas_course_times as ti where y.patrolcheck_id = z.patrolcheck_id and z.times_id = ti.times_id and y.inspecitem_id = a.inspecitem_id and z.class_id = pc.class_id and ti.units_id = tt.units_id and y.patrolcheck_status = '1' order by y.patrolcheck_updatetime desc,y.patrolcheck_createtime desc limit 1) = '0')), 0) as check_normnums,
                    IFNULL(({$c_sql}
                            and exists (select 1 from eas_classes_inspec_patrolcheck as p,eas_classes_inspec_patrolcheck_splclass as pc,eas_course_times as tt where p.patrolcheck_id = pc.patrolcheck_id and pc.class_id = c.class_id
                            and p.inspecitem_id = x.inspecitem_id and p.school_id = ct.school_id and pc.times_id = tt.times_id and tt.units_id = x.units_id and p.patrolcheck_status = '1' and p.patrolcheck_level = '-1'
                            and (select y.patrolcheck_level from eas_classes_inspec_patrolcheck as y,eas_classes_inspec_patrolcheck_splclass as z,eas_course_times as ti where y.patrolcheck_id = z.patrolcheck_id and z.times_id = ti.times_id and y.inspecitem_id = a.inspecitem_id and z.class_id = pc.class_id and ti.units_id = tt.units_id and y.patrolcheck_status = '1' order by y.patrolcheck_updatetime desc,y.patrolcheck_createtime desc limit 1) = '-1')), 0) as check_errnums
                FROM 
                    eas_course_inspectasks as a
                    LEFT JOIN eas_course_units as u ON u.units_id = a.units_id
                    LEFT JOIN eas_course_inspecitem as i ON i.inspecitem_id = a.inspecitem_id
                    LEFT JOIN eas_course_inspectype as t ON t.inspectype_id = i.inspectype_id
                    LEFT JOIN eas_course_times as ts ON ts.course_branch = a.course_branch and ts.units_id = u.units_id
                    LEFT JOIN eas_classes_tasks as ct ON ct.times_id = ts.times_id
                WHERE
                    {$datawhere}
                GROUP BY
	                a.inspecitem_id
                ORDER BY
                    i.inspecitem_sort";

        if (!empty($paramArray['is_count'])) {
            $db_nums = $this->Show_css->selectClear($sql);
            if ($db_nums) {
                $allnum = count($db_nums);
            } else {
                $allnum = 0;
            }
            $data['allnum'] = $allnum;
        }

        $dataList = $this->Show_css->selectClear($sql . " LIMIT {$pagestart},{$num}");
        if ($dataList) {
            foreach ($dataList as &$value) {
                $value['check_notnums'] = (string)($value['class_nums'] - $value['check_allnums']);
            }
        } else {
            $dataList = array();
        }

        $data['list'] = $dataList;
        return $data;
    }

    //获取巡检明细
    function getInspecDetailApi($paramArray)
    {
        $datawhere = "c.school_branch = '{$this->schoolOne['school_branch']}' and a.course_branch = '{$paramArray['course_branch']}' and a.units_id = '{$paramArray['units_id']}'";
        $datawhere .= " and a.inspectasks_type = '1' and a.inspectasks_status = '1' and i.inspecitem_status = '1' and t.inspectype_status = '1'";
//        $datawhere .= " and exists (select 1 from eas_course_inspecitem_splpost as p where p.inspecitem_id = a.inspecitem_id and p.post_id = '{$this->postbeOne['post_id']}')";
        $datawhere .= " and exists (select 1 from eas_classes_tasks as ct where ct.class_id = c.class_id)";
        if (in_array($this->schoolOne['school_class'], array("1", "3"))) {
            $datawhere .= " and (
                            c.class_naturejson = '' or c.class_naturejson = '{\"chinese\":\"1\",\"english\":\"1\",\"maths\":\"1\"}'
                            or (t.inspectype_classnature = 0 and (c.class_naturejson like '%\"chinese\":\"1\"%' or c.class_naturejson like '%\"english\":\"1\"%' or c.class_naturejson like '%\"maths\":\"1\"%'))
                            or (FIND_IN_SET(1, t.inspectype_classnature) and c.class_naturejson like '%\"chinese\":\"1\"%')
                            or (FIND_IN_SET(2, t.inspectype_classnature) and c.class_naturejson like '%\"english\":\"1\"%')
                            or (FIND_IN_SET(3, t.inspectype_classnature) and c.class_naturejson like '%\"maths\":\"1\"%')
                        )";
        }

        if (!empty($paramArray['inspecitem_id'])) {
            $datawhere .= " and a.inspecitem_id = '{$paramArray['inspecitem_id']}'";
        }
        if (!empty($paramArray['inspectype_id'])) {
            $datawhere .= " and t.inspectype_id = '{$paramArray['inspectype_id']}'";
        }
        if (!empty($paramArray['class_id'])) {
            $datawhere .= " and c.class_id = '{$paramArray['class_id']}'";
        }

        if (!empty($paramArray['p'])) {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (!empty($paramArray['num'])) {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT 
                    a.inspectasks_id,a.inspecitem_id,i.inspecitem_name,i.inspecitem_content,t.inspectype_name,c.class_id,c.class_cnname
                FROM 
                    eas_course_inspectasks as a
                    LEFT JOIN eas_course_inspecitem as i ON i.inspecitem_id = a.inspecitem_id
                    LEFT JOIN eas_course_inspectype as t ON t.inspectype_id = i.inspectype_id
                    LEFT JOIN eas_classes as c ON c.course_branch = a.course_branch
                WHERE
                    {$datawhere}
                ORDER BY
                    a.course_branch";

        if (empty($paramArray['times_id']) && $paramArray['patrolcheck_level'] == '') {
            $data = array();
            if (!empty($paramArray['is_count'])) {
                $db_nums = $this->Show_css->selectClear($sql);
                if ($db_nums) {
                    $allnum = count($db_nums);
                } else {
                    $allnum = 0;
                }
                $data['allnum'] = $allnum;
            }
            $dataList = $this->Show_css->selectClear($sql . " LIMIT {$pagestart},{$num}");
        } else {
            $checkList = array();
            $dataList = $this->Show_css->selectClear($sql);
        }

        if ($dataList) {
            $level = array("0" => "正常", "1" => "优秀", "-1" => "异常", "-2" => "待检核");
            foreach ($dataList as &$value) {
                $patrolcheck = $this->Show_css->selectOne("SELECT p.patrolcheck_id,p.patrolcheck_type,p.patrolcheck_level,p.patrolcheck_content,p.patrolcheck_imgsurl,p.patrolcheck_day,
                              FROM_UNIXTIME(p.patrolcheck_createtime,'%Y-%m-%d %H:%i') as patrolcheck_createtime,t.teacher_cnname,t.teacher_enname,ct.times_id,ct.times_name
                        FROM 
                            eas_classes_inspec_patrolcheck as p
                            LEFT JOIN eas_classes_inspec_patrolcheck_splclass as ps ON ps.patrolcheck_id = p.patrolcheck_id
                            LEFT JOIN app_teacher as t ON t.teacher_id = p.teacher_id
                            LEFT JOIN eas_course_times as ct ON ct.times_id = ps.times_id
                        WHERE 
                            p.patrolcheck_status = '1' and p.school_id = '{$paramArray['school_id']}' and p.inspecitem_id = '{$value['inspecitem_id']}' and ps.class_id = '{$value['class_id']}' and ct.units_id = '{$paramArray['units_id']}'
                        ORDER BY
                            p.patrolcheck_updatetime DESC,p.patrolcheck_createtime DESC");
                if (!empty($paramArray['times_id']) && $patrolcheck['times_id'] !== $paramArray['times_id']) {
                    continue;
                }
                if ($patrolcheck) {
                    $value['times_id'] = $patrolcheck['times_id'];
                    $value['times_name'] = $patrolcheck['times_name'];
                    $value['patrolcheck_level'] = $patrolcheck['patrolcheck_level'];
                    $value['patrolcheck_level_name'] = $level[$patrolcheck['patrolcheck_level']];
                    $value['patrolcheck_content'] = $patrolcheck['patrolcheck_content'] ?: '--';
                    $value['patrolcheck_createtime'] = $patrolcheck['patrolcheck_createtime'];
                    $value['teacher_cnname'] = $patrolcheck['teacher_enname'] ? $patrolcheck['teacher_cnname'] . '/' . $patrolcheck['teacher_enname'] : $patrolcheck['teacher_cnname'];
                    $value['patrolcheck_imgsurl'] = $patrolcheck['patrolcheck_imgsurl'];
                    $imgsurl = json_decode($patrolcheck['patrolcheck_imgsurl'], true);
                    $value['patrolcheck_imagenums'] = count($imgsurl);
                } else {
                    $value['patrolcheck_level'] = '-2';
                    $value['patrolcheck_level_name'] = $level[-2];
                    $value['times_name'] = $value['patrolcheck_imagenums'] = $value['patrolcheck_videonums'] = $value['patrolcheck_content'] = $value['teacher_cnname'] = $value['patrolcheck_createtime'] = '--';
                }
                if ($paramArray['patrolcheck_level'] !== '' && $value['patrolcheck_level_name'] !== $level[$paramArray['patrolcheck_level']]) {
                    continue;
                }
                if (!empty($paramArray['times_id']) || $paramArray['patrolcheck_level'] !== '') {
                    $checkList[] = $value;
                }
            }
            if (!empty($paramArray['times_id']) || $paramArray['patrolcheck_level'] !== '') {
                if (!empty($paramArray['is_count'])) {
                    $data['allnum'] = $checkList ? count($checkList) : 0;
                } else {
                    $data['allnum'] = 0;
                }
                $dataList = array_slice($checkList, $pagestart, $num);
            }
        } else {
            $dataList = array();
        }

        $data['list'] = $dataList;
        return $data;
    }

    //获取项目巡检详情
    function getInspecItemOne($paramArray)
    {
        $dataOne = $this->Show_css->selectOne("SELECT 
                    p.patrolcheck_id,p.inspecitem_id,p.patrolcheck_type,p.patrolcheck_level,p.patrolcheck_day,p.patrolcheck_content,p.patrolcheck_imgsurl,
                    FROM_UNIXTIME(p.patrolcheck_createtime,'%Y-%m-%d %H:%i') as createtime,i.inspecitem_name,i.inspecitem_content,t.teacher_cnname,t.teacher_enname
                FROM 
                    eas_classes_inspec_patrolcheck as p
                    LEFT JOIN eas_course_inspecitem as i ON i.inspecitem_id = p.inspecitem_id
                    LEFT JOIN app_teacher as t ON t.teacher_id = p.teacher_id 
                WHERE   
                    p.patrolcheck_id = '{$paramArray['patrolcheck_id']}' and i.inspecitem_status = '1'");
        $dataOne['class_list'] = $this->Show_css->selectClear("SELECT class_id FROM eas_classes_inspec_patrolcheck_splclass WHERE patrolcheck_id = '{$dataOne['patrolcheck_id']}'");
        if (!empty($paramArray['class_id'])) {
            $timesOne = $this->Show_css->getFieldOne("eas_classes", "class_cnname", "class_id = '{$paramArray['class_id']}'");
            $dataOne['class_cnname'] = $timesOne['class_cnname'];
        }
        if (!empty($paramArray['times_id'])) {
            $timesOne = $this->Show_css->getFieldOne("eas_course_times", "times_name", "times_id = '{$paramArray['times_id']}'");
            $dataOne['times_name'] = $timesOne['times_name'];
        }

        return $dataOne;
    }

    //获取巡班听课草稿箱
    function getDraftBoxApi($paramArray)
    {
        if (!empty($paramArray['p'])) {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (!empty($paramArray['num'])) {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if ($paramArray['type'] == '1') {
            $sql = "SELECT 
                        p.patrolcheck_id,e.inspecitem_id,e.inspecitem_name,p.patrolcheck_level,p.patrolcheck_content,p.patrolcheck_imgsurl,p.patrolcheck_day,u.course_branch,u.units_sort,
                        (select group_concat(distinct c.class_cnname) from eas_classes_inspec_patrolcheck_splclass as pc,eas_classes as c where pc.patrolcheck_id = p.patrolcheck_id and pc.class_id = c.class_id) as class_cnname,
                        tc.teacher_cnname,FROM_UNIXTIME(p.patrolcheck_createtime,'%Y-%m-%d %H:%i') as createtime
                    FROM 
                        eas_classes_inspec_patrolcheck as p
                        LEFT JOIN eas_classes_inspec_patrolcheck_splclass as pc ON pc.patrolcheck_id = p.patrolcheck_id
                        LEFT JOIN eas_course_times as t ON t.times_id = pc.times_id
                        LEFT JOIN eas_course_units as u ON u.units_id = t.units_id
                        LEFT JOIN app_teacher as tc ON tc.teacher_id = p.teacher_id 
                        LEFT JOIN eas_course_inspecitem as e ON e.inspecitem_id = p.inspecitem_id
                        LEFT JOIN eas_course_inspectasks as f ON f.course_branch = t.course_branch and f.inspecitem_id = p.inspecitem_id and f.units_id = u.units_id
                    WHERE 
                        p.school_id = '{$paramArray['school_id']}' and p.teacher_id = '{$paramArray['teacher_id']}' and u.units_sort = '{$paramArray['units_sort']}' and p.patrolcheck_status = '0' and e.inspecitem_status = '1' and f.inspectasks_status = '1'
                    GROUP BY
                        p.patrolcheck_id
                    ORDER BY
                         p.patrolcheck_createtime DESC
                    LIMIT {$pagestart},{$num}";
        } else {
            $sql = "SELECT 
                        l.lslistens_id,s.inspectype_id,l.lslistens_type,l.lslistens_class,l.lslistens_day,c.class_id,c.class_cnname,ct.times_name,ct.times_sort,u.units_sort,t.teacher_cnname,FROM_UNIXTIME(l.lslistens_createtime,'%Y-%m-%d %H:%i') as createtime
                    FROM 
                        eas_classes_inspec_lslistens as l
                        LEFT JOIN eas_classes as c ON c.class_id = l.class_id 
                        LEFT JOIN eas_course_times as ct ON ct.times_id = l.times_id 
                        LEFT JOIN eas_course_units as u ON u.units_id = ct.units_id
                        LEFT JOIN app_teacher as t ON t.teacher_id = l.teacher_id 
                        LEFT JOIN eas_course_inspec_lsscore as s ON s.lsscore_id = l.lsscore_id
                        LEFT JOIN eas_course_inspectasks as f ON f.course_branch = c.course_branch and f.inspecitem_id = l.inspecitem_id and f.lsscore_id = l.lsscore_id
                    WHERE 
                         l.school_id = '{$paramArray['school_id']}' and l.teacher_id = '{$paramArray['teacher_id']}' and ct.times_sort = '{$paramArray['times_sort']}' and l.lslistens_status = '0' and s.lsscore_status = '1' and f.inspectasks_status = '1'
                    ORDER BY
                         l.lslistens_createtime DESC
                    LIMIT {$pagestart},{$num}";
        }
        $datalist = $this->Show_css->selectClear($sql);
        if ($paramArray['type'] == '1') {
            if ($datalist) {
                foreach ($datalist as &$value) {
                    $value['class_list'] = $this->Show_css->selectClear("select distinct pc.class_id from eas_classes_inspec_patrolcheck_splclass as pc where pc.patrolcheck_id = '{$value['patrolcheck_id']}'");
                }
            }
        }

        return $datalist ?: array();
    }

    //获取巡检班级
    function getCheckClassApi($paramArray)
    {
        $day = date("Ymd");
        $datawhere = "c.school_branch = '{$this->schoolOne['school_branch']}' and a.inspecitem_id = '{$paramArray['inspecitem_id']}' and a.inspectasks_type = '1' and a.inspectasks_status = '1' and t.inspectype_status = '1'";
        $datawhere .= " and a.course_branch = '{$paramArray['course_branch']}' and u.units_sort = '{$paramArray['units_sort']}' and c.class_stdate <= '{$day}' and c.class_enddate >= '{$day}' and c.class_isdel = '0'";
        $datawhere .= " and exists (select 1 from eas_course_inspecitem_splpost as p where p.inspecitem_id = a.inspecitem_id and p.post_id = '{$this->postbeOne['post_id']}')";
        if (in_array($this->schoolOne['school_class'], array("1", "3"))) {
            $datawhere .= " and (
                            c.class_naturejson = '' or c.class_naturejson = '{\"chinese\":\"1\",\"english\":\"1\",\"maths\":\"1\"}'
                            or (t.inspectype_classnature = 0 and (c.class_naturejson like '%\"chinese\":\"1\"%' or c.class_naturejson like '%\"english\":\"1\"%' or c.class_naturejson like '%\"maths\":\"1\"%'))
                            or (FIND_IN_SET(1, t.inspectype_classnature) and c.class_naturejson like '%\"chinese\":\"1\"%')
                            or (FIND_IN_SET(2, t.inspectype_classnature) and c.class_naturejson like '%\"english\":\"1\"%')
                            or (FIND_IN_SET(3, t.inspectype_classnature) and c.class_naturejson like '%\"maths\":\"1\"%')
                        )";
        }

        if (!empty($paramArray['keyword'])) {
            $datawhere .= " and c.class_cnname like '%{$paramArray['keyword']}%'";
        }

        $sql = "SELECT 
                    c.class_id,c.class_cnname,u.units_sort,
                    (select t.times_id from eas_classes_tasks as ct,eas_course_times as t where ct.times_id = t.times_id and ct.class_id = c.class_id order by t.times_sort desc limit 1) as times_id,
                    (select t.times_name from eas_classes_tasks as ct,eas_course_times as t where ct.times_id = t.times_id and ct.class_id = c.class_id order by t.times_sort desc limit 1) as times_name,
                    IF((SELECT count(1) FROM eas_classes_inspec_patrolcheck AS p,eas_classes_inspec_patrolcheck_splclass as pc,eas_course_times as t,eas_course_units as cu
                        WHERE p.patrolcheck_id = pc.patrolcheck_id and p.inspecitem_id = a.inspecitem_id and pc.times_id = t.times_id and t.units_id = cu.units_id and cu.units_sort = u.units_sort and pc.class_id = c.class_id and p.patrolcheck_status = '1'), '已检核', '未检核') AS is_check
                FROM 
                    eas_course_inspectasks as a
                    LEFT JOIN eas_course_inspecitem as i ON i.inspecitem_id = a.inspecitem_id
                    LEFT JOIN eas_course_inspectype as t ON t.inspectype_id = i.inspectype_id
                    LEFT JOIN eas_classes as c ON c.course_branch = a.course_branch
                    LEFT JOIN eas_course_units as u ON u.units_id = a.units_id
                WHERE 
                    {$datawhere}
                GROUP BY
                    c.class_id
                HAVING
                    times_id > 0
                ORDER BY
                    is_check DESC,c.course_branch";

        $dataList = $this->Show_css->selectClear($sql);
        if ($dataList) {
            foreach ($dataList as &$value) {
                $unitSort = $this->Show_css->selectOne("SELECT cu.units_sort FROM eas_classes_tasks as ct,eas_course_times as ts,eas_course_units as cu WHERE ct.times_id = ts.times_id and ts.units_id = cu.units_id and ct.class_id = '{$value['class_id']}' ORDER BY ts.times_sort DESC");
                if ($unitSort['units_sort'] == $paramArray['units_sort']) {
                    $value['is_select'] = '1';
                } else {
                    $value['is_select'] = '0';
                }
            }
        }

        return $dataList ?: array();
    }

    //立即检核
    function addPatrolCheckApi($paramArray)
    {
        $class_list = json_decode(stripslashes($paramArray['class_list']), true);
        if (empty($class_list)) {
            $this->error = 1;
            $this->errortip = '请选择班级';
            return false;
        }
        if ($paramArray['patrolcheck_level'] == '-1' && $paramArray['patrolcheck_status']) {
            $classArr = array();
            foreach ($class_list as $value) {
                if ($this->Show_css->selectOne("SELECT 1 FROM eas_classes_inspec_patrolcheck as p,eas_classes_inspec_patrolcheck_splclass as pc WHERE p.patrolcheck_id = pc.patrolcheck_id and p.inspecitem_id = '{$paramArray['inspecitem_id']}' and pc.class_id = '{$value['class_id']}' and pc.times_id = '{$value['times_id']}' and p.patrolcheck_level = '-1' and p.patrolcheck_status = '1' and (select x.patrolcheck_level from eas_classes_inspec_patrolcheck as x,eas_classes_inspec_patrolcheck_splclass as y where x.patrolcheck_id = y.patrolcheck_id and x.inspecitem_id = p.inspecitem_id and y.class_id = pc.class_id and y.times_id = pc.times_id and x.patrolcheck_status = '1' order by x.patrolcheck_updatetime desc,x.patrolcheck_createtime desc limit 1) = '-1'")) {
                    $classOne = $this->Show_css->getFieldOne("eas_classes", "class_cnname", "class_id = '{$value['class_id']}'");
                    $classArr[] = $classOne['class_cnname'];
                }
            }
            if ($classArr) {
                $classStr = implode("、", $classArr);
                $this->error = 1;
                $this->errortip = "{$classStr}当前存在异常记录，不可再次提交异常";
                return false;
            }
        }
        if ($paramArray['patrolcheck_status']) {
            $tip = "提交";
        } else {
            $tip = "保存";
        }
        $data = array();
        $data['school_id'] = $paramArray['school_id'];
        $data['teacher_id'] = $paramArray['teacher_id'];
        $data['inspecitem_id'] = $paramArray['inspecitem_id'];
        $data['patrolcheck_type'] = $paramArray['patrolcheck_type'];
        if ($paramArray['patrolcheck_type']) {
            $data['from_patrolcheck_id'] = $paramArray['patrolcheck_id'];
        }
        $data['patrolcheck_level'] = $paramArray['patrolcheck_level'];
        if ($paramArray['patrolcheck_level'] == '-1') {
            $data['patrolcheck_day'] = $paramArray['patrolcheck_day'];
        }
        $data['patrolcheck_content'] = $paramArray['patrolcheck_content'];
        $data['patrolcheck_imgsurl'] = $paramArray['patrolcheck_imgsurl'];
        $data['patrolcheck_status'] = $paramArray['patrolcheck_status'];
        $data['patrolcheck_updatetime'] = time();
        if ($paramArray['patrolcheck_id'] && !$paramArray['patrolcheck_type'] && $paramArray['patrolcheck_status']) {
            $this->Show_css->updateData("eas_classes_inspec_patrolcheck","patrolcheck_id = '{$paramArray['patrolcheck_id']}'", $data);
            $dataid = $paramArray['patrolcheck_id'];
        } else {
            $data['patrolcheck_createtime'] = time();
            $dataid = $this->Show_css->insertData("eas_classes_inspec_patrolcheck", $data);
        }
        if ($dataid) {
            $this->Show_css->delData("eas_classes_inspec_patrolcheck_splclass", "patrolcheck_id = '{$dataid}'");
            foreach ($class_list as $value) {
                $class = array();
                $class['patrolcheck_id'] = $dataid;
                $class['class_id'] = $value['class_id'];
                $class['times_id'] = $value['times_id'];
                $this->Show_css->insertData("eas_classes_inspec_patrolcheck_splclass", $class);
            }
            $this->error = 0;
            $this->errortip = "{$tip}成功";
            return true;
        } else {
            $this->error = 1;
            $this->errortip = "{$tip}失败";
            return false;
        }
    }

    //添加听课记录
    function addInspecLslistensApi($paramArray)
    {
        $value_list = json_decode(stripslashes($paramArray['value_list']), true);
        if (!$paramArray['lslistens_class'] && empty($value_list)) {
            $this->error = 1;
            $this->errortip = '请选择班级';
            return false;
        }
        if ($paramArray['lslistens_status']) {
            $tip = "提交";
        } else {
            $tip = "保存";
        }
        $timesOne = $this->Show_css->selectOne("SELECT t.times_id FROM eas_classes_tasks as ct,eas_course_times as t WHERE ct.times_id = t.times_id and ct.class_id = '{$paramArray['class_id']}' and t.times_sort = '{$paramArray['times_sort']}'");
        if ($timesOne) {
            if ($paramArray['lslistens_score'] < 70 && $paramArray['lslistens_status']) {
                if ($this->Show_css->selectOne("SELECT 1 FROM eas_classes_inspec_lslistens as l WHERE l.inspecitem_id = '{$paramArray['inspecitem_id']}' and l.class_id = '{$paramArray['class_id']}' and l.times_id = '{$timesOne['times_id']}' and l.lslistens_score < 70 and l.lslistens_status = '1' and (select x.lslistens_score from eas_classes_inspec_lslistens as x where x.inspecitem_id = l.inspecitem_id and x.class_id = l.class_id and x.times_id = l.times_id and x.lslistens_status = '1' order by x.lslistens_updatetime desc,x.lslistens_createtime desc limit 1) < 70")) {
                    $classOne = $this->Show_css->getFieldOne("eas_classes", "class_cnname", "class_id = '{$paramArray['class_id']}'");
                    $this->error = 1;
                    $this->errortip = "{$classOne['class_cnname']}当前存在异常记录，不可再次提交异常";
                    return false;
                }
            }
            $data = array();
            $data['school_id'] = $paramArray['school_id'];
            $data['class_id'] = $paramArray['class_id'];
            $data['times_id'] = $timesOne['times_id'];
            $data['teacher_id'] = $paramArray['teacher_id'];
            $data['lsscore_id'] = $paramArray['lsscore_id'];
            $data['inspecitem_id'] = $paramArray['inspecitem_id'];
            $data['lslistens_type'] = $paramArray['lslistens_type'];
            if ($paramArray['lslistens_type']) {
                $data['from_lslistens_id'] = $paramArray['lslistens_id'];
            }
            $data['lslistens_class'] = $paramArray['lslistens_class'];
            $data['lslistens_day'] = $paramArray['lslistens_day'];
            if ($paramArray['lslistens_class']) {
                $data['lslistens_imgsurl'] = $paramArray['lslistens_imgsurl'];
                $data['lslistens_content'] = $paramArray['lslistens_content'];
            } else {
                $data['lslistens_name'] = $paramArray['lslistens_name'];
                $data['lslistens_place'] = $paramArray['lslistens_place'];
                $data['lslistens_prepare'] = $paramArray['lslistens_prepare'];
                $data['lslistens_target'] = $paramArray['lslistens_target'];
                $data['lslistens_process'] = $paramArray['lslistens_process'];
                $data['lslistens_appraise'] = $paramArray['lslistens_appraise'];
                $data['lslistens_evaluate'] = $paramArray['lslistens_evaluate'];
            }
            $data['lslistens_photo'] = $paramArray['lslistens_photo'];
            $data['lslistens_score'] = $paramArray['lslistens_score'];
            $data['lslistens_status'] = $paramArray['lslistens_status'];
            $data['lslistens_updatetime'] = time();
            if ($paramArray['lslistens_id'] && !$paramArray['lslistens_type'] && $paramArray['lslistens_status']) {
                $this->Show_css->updateData("eas_classes_inspec_lslistens", "lslistens_id = '{$paramArray['lslistens_id']}'", $data);
                $dataid = $paramArray['lslistens_id'];
            } else {
                $data['lslistens_createtime'] = time();
                $dataid = $this->Show_css->insertData("eas_classes_inspec_lslistens", $data);
            }
            if ($dataid) {
                $this->Show_css->delData("eas_classes_inspec_lslistens_normvalue", "lslistens_id = '{$dataid}'");
                if (!$paramArray['lslistens_class']) {
                    foreach ($value_list as $value) {
                        foreach ($value['normvalue_list'] as $item) {
                            $class = array();
                            $class['lslistens_id'] = $dataid;
                            $class['itemdetail_id'] = $value['itemdetail_id'];
                            $class['normvalue_sort'] = $item['normvalue_sort'];
                            $class['normvalue_score'] = $item['normvalue_score'];
                            $class['normvalue_content'] = $item['normvalue_content'];
                            $this->Show_css->insertData("eas_classes_inspec_lslistens_normvalue", $class);
                        }
                    }
                }
                $this->error = 0;
                $this->errortip = "{$tip}成功";
                return true;
            } else {
                $this->error = 1;
                $this->errortip = "{$tip}失败";
                return false;
            }
        } else {
            $this->error = 1;
            $this->errortip = "{$tip}失败";
            return false;
        }
    }

    //删除巡班/听课草稿箱数据
    function delDraftBoxDataApi($paramArray)
    {
        if ($paramArray['type'] == '1') {
            if (!empty($paramArray['patrolcheck_id'])) {
                $this->Show_css->delData("eas_classes_inspec_patrolcheck", "patrolcheck_id = '{$paramArray['patrolcheck_id']}'");
            } else {
                $patrolcheckList = $this->Show_css->selectClear("SELECT DISTINCT a.patrolcheck_id FROM eas_classes_inspec_patrolcheck a,eas_classes_inspec_patrolcheck_splclass b,eas_course_times c,eas_course_units d WHERE a.patrolcheck_id = b.patrolcheck_id and b.times_id = c.times_id and c.units_id = d.units_id and a.school_id = '{$paramArray['school_id']}' and a.teacher_id = '{$paramArray['teacher_id']}' and d.units_sort = '{$paramArray['units_sort']}' and a.patrolcheck_status = '0'");
                if ($patrolcheckList) {
                    foreach ($patrolcheckList as $value) {
                        $this->Show_css->delData("eas_classes_inspec_patrolcheck", "patrolcheck_id = '{$value['patrolcheck_id']}'");
                    }
                }
            }
        } else {
            if (!empty($paramArray['lslistens_id'])) {
                $this->Show_css->delData("eas_classes_inspec_lslistens", "lslistens_id = '{$paramArray['lslistens_id']}'");
            } else {
                $lslistensList = $this->Show_css->selectClear("SELECT DISTINCT a.lslistens_id FROM eas_classes_inspec_lslistens a,eas_course_times b WHERE a.times_id = b.times_id and a.school_id = '{$paramArray['school_id']}' and a.teacher_id = '{$paramArray['teacher_id']}' and b.times_sort = '{$paramArray['times_sort']}' and a.lslistens_status = '0'");
                if ($lslistensList) {
                    foreach ($lslistensList as $value) {
                        $this->Show_css->delData("eas_classes_inspec_lslistens", "lslistens_id = '{$value['lslistens_id']}'");
                    }
                }
            }
        }
        $this->error = 0;
        $this->errortip = "删除成功";
        return true;
    }

    //获取周次统计
    function getTimesStatApi($paramArray)
    {
        $day = date("Ymd");
        $datawhere = "c.school_branch = '{$this->schoolOne['school_branch']}' and tt.times_sort = '{$paramArray['times_sort']}' and a.inspectasks_type = '1' and a.inspectasks_status = '1' and t.inspectype_status = '1' and c.class_stdate <= '{$day}' and c.class_enddate >= '{$day}' and c.class_isdel = '0'";
//        $datawhere .= " and exists (select 1 from eas_course_inspecitem_splpost as p where p.inspecitem_id = a.inspecitem_id and p.post_id = '{$this->postbeOne['post_id']}')";
        $datawhere .= " and exists (select 1 from eas_classes_tasks as ct where ct.class_id = c.class_id and ct.times_id = tt.times_id)";
        if (in_array($this->schoolOne['school_class'], array("1", "3"))) {
            $datawhere .= " and (
                            c.class_naturejson = '' or c.class_naturejson = '{\"chinese\":\"1\",\"english\":\"1\",\"maths\":\"1\"}'
                            or (t.inspectype_classnature = 0 and (c.class_naturejson like '%\"chinese\":\"1\"%' or c.class_naturejson like '%\"english\":\"1\"%' or c.class_naturejson like '%\"maths\":\"1\"%'))
                            or (FIND_IN_SET(1, t.inspectype_classnature) and c.class_naturejson like '%\"chinese\":\"1\"%')
                            or (FIND_IN_SET(2, t.inspectype_classnature) and c.class_naturejson like '%\"english\":\"1\"%')
                            or (FIND_IN_SET(3, t.inspectype_classnature) and c.class_naturejson like '%\"maths\":\"1\"%')
                        )";
        }

        if (!empty($paramArray['keyword'])) {
            $datawhere .= " and c.class_cnname like '%{$paramArray['keyword']}%'";
        }
        $having = "1=1";
        if (isset($paramArray['is_check']) && $paramArray['is_check'] !== '') {
            $having .= " and is_check = '{$paramArray['is_check']}'";
        }

        if (!empty($paramArray['p'])) {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (!empty($paramArray['num'])) {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT 
                    a.inspectasks_id,c.class_id,c.class_cnname,c.course_branch,tt.times_id,tt.times_name,tt.times_sort,u.units_id,
                    IF((SELECT count(1) FROM eas_classes_inspec_patrolcheck as p,eas_classes_inspec_patrolcheck_splclass as pc,eas_course_times as ct
                        WHERE p.patrolcheck_id = pc.patrolcheck_id and pc.times_id = ct.times_id and ct.times_sort = tt.times_sort and pc.class_id = c.class_id and p.patrolcheck_status = '1'), '1', '0') AS is_check
                FROM 
                    eas_course_inspectasks as a
                    LEFT JOIN eas_course_inspecitem as i ON i.inspecitem_id = a.inspecitem_id
                    LEFT JOIN eas_course_inspectype as t ON t.inspectype_id = i.inspectype_id
                    LEFT JOIN eas_classes as c ON c.course_branch = a.course_branch
                    LEFT JOIN eas_course_units as u ON u.units_id = a.units_id
                    LEFT JOIN eas_course_times as tt ON tt.course_branch = a.course_branch and tt.units_id = u.units_id
                WHERE 
                    {$datawhere}
                GROUP BY 
                    c.class_id
                HAVING
                    {$having}
                ORDER BY
                    c.course_branch
                LIMIT
                    {$pagestart},{$num}";
        $dataList = $this->Show_css->selectClear($sql);
        if ($dataList) {
            foreach ($dataList as $key => &$value) {
                $itemList = $this->Show_css->selectClear("SELECT i.inspecitem_id,i.inspecitem_name,i.inspecitem_content,t.inspectype_name FROM eas_course_inspectasks as a,eas_course_inspecitem as i,eas_course_inspectype as t WHERE a.inspecitem_id = i.inspecitem_id and i.inspectype_id = t.inspectype_id and a.course_branch = '{$value['course_branch']}' and a.units_id = '{$value['units_id']}' and a.inspectasks_type = '1' and a.inspectasks_status = '1' and i.inspecitem_status = '1' and t.inspectype_status = '1'");
                if ($itemList) {
                    if ($value['is_check']) {
                        foreach ($itemList as $k => &$item) {
                            $patrolcheck = $this->Show_css->selectOne("SELECT p.patrolcheck_level FROM eas_classes_inspec_patrolcheck_splclass as pc,eas_classes_inspec_patrolcheck as p,eas_course_times as ts WHERE pc.patrolcheck_id = p.patrolcheck_id and pc.times_id = ts.times_id and pc.class_id = '{$value['class_id']}' and ts.times_sort = '{$value['times_sort']}' and p.inspecitem_id = '{$item['inspecitem_id']}' and p.patrolcheck_status = '1' ORDER BY p.patrolcheck_updatetime DESC,p.patrolcheck_createtime DESC");
                            if ($patrolcheck) {
                                $item['check_status'] = $patrolcheck['patrolcheck_level'];
                            } else {
                                unset($itemList[$k]);
                            }
                        }
                    }
                    $value['item_list'] = $itemList;
                } else {
                    unset($dataList[$key]);
                }
            }
        } else {
            $dataList = array();
        }

        return $dataList;
    }

    //主题统计
    function getThemeStatApi($paramArray)
    {
        $day = date("Ymd");
        if (!empty($paramArray['units_sort'])) {
            $units = "u.units_sort = '{$paramArray['units_sort']}'";
        } else {
            $units = "u.units_id = '{$paramArray['units_id']}'";
        }
        $datawhere = "ct.school_id = '{$paramArray['school_id']}' and c.class_stdate <= '{$day}' and c.class_enddate >= '{$day}' and c.class_isdel = '0' and {$units}";
        if (in_array($this->schoolOne['school_class'], array("1", "3"))) {
            $datawhere .= " and exists (select 
                                        group_concat(it.inspectype_classnature) as classnature
                                    from 
                                        eas_course_inspectasks as a,
                                        eas_course_inspecitem as i,
                                        eas_course_inspectype as it,
                                        eas_course_units as u
                                    where 
                                        a.inspecitem_id = i.inspecitem_id and i.inspectype_id = it.inspectype_id and a.units_id = u.units_id and {$units}
                                    having
                                        c.class_naturejson = '' or c.class_naturejson = '{\"chinese\":\"1\",\"english\":\"1\",\"maths\":\"1\"}'
                                        or (FIND_IN_SET(0, classnature) and (c.class_naturejson like '%\"chinese\":\"1\"%' or c.class_naturejson like '%\"english\":\"1\"%' or c.class_naturejson like '%\"maths\":\"1\"%'))
                                        or (FIND_IN_SET(1, classnature) and c.class_naturejson like '%\"chinese\":\"1\"%')
                                        or (FIND_IN_SET(2, classnature) and c.class_naturejson like '%\"english\":\"1\"%')
                                        or (FIND_IN_SET(3, classnature) and c.class_naturejson like '%\"maths\":\"1\"%')
                                    )";
        }
        if (!empty($paramArray['class_id'])) {
            $datawhere .= " and c.class_id = '{$paramArray['class_id']}'";
        }
        if (!empty($paramArray['keyword'])) {
            $datawhere .= " and c.class_cnname like '%{$paramArray['keyword']}%'";
        }
        $having = "1=1";
        if (isset($paramArray['is_check']) && $paramArray['is_check'] !== '') {
            if ($paramArray['is_check']) {
                $having .= " and item_allnum = item_comnum";
            } else {
                $having .= " and item_allnum <> item_comnum";
            }
        }

        if (!empty($paramArray['p'])) {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (!empty($paramArray['num'])) {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $classwhere = " and (
                        cs.class_naturejson = '' or cs.class_naturejson = '{\"chinese\":\"1\",\"english\":\"1\",\"maths\":\"1\"}'
                        or (t.inspectype_classnature = 0 and (cs.class_naturejson like '%\"chinese\":\"1\"%' or cs.class_naturejson like '%\"english\":\"1\"%' or cs.class_naturejson like '%\"maths\":\"1\"%'))
                        or (FIND_IN_SET(1, t.inspectype_classnature) and cs.class_naturejson like '%\"chinese\":\"1\"%')
                        or (FIND_IN_SET(2, t.inspectype_classnature) and cs.class_naturejson like '%\"english\":\"1\"%')
                        or (FIND_IN_SET(3, t.inspectype_classnature) and cs.class_naturejson like '%\"maths\":\"1\"%')
                    )";

        $where = "a.inspecitem_id = i.inspecitem_id and i.inspectype_id = t.inspectype_id and a.course_branch = c.course_branch and a.course_branch = cs.course_branch";
        $where .= " and a.units_id = b.units_id and b.units_sort = u.units_sort and cs.class_id = c.class_id and a.inspectasks_type = '1' and a.inspectasks_status = '1'";
//        $where .= " and exists (select 1 from eas_course_inspecitem_splpost as p where p.inspecitem_id = a.inspecitem_id and p.post_id = '{$this->postbeOne['post_id']}')";
        if (in_array($this->schoolOne['school_class'], array("1", "3"))) {
            $where .= $classwhere;
        }

        $c_sql = "select count(1) from eas_course_inspectasks as a,eas_course_inspecitem as i,eas_course_inspectype as t,eas_course_units as b,eas_classes as cs where {$where}";

        $sql = "SELECT 
                    ct.class_id,ct.times_id,c.class_cnname,c.class_branch,c.course_branch,c.class_naturejson,u.units_id,u.units_sort,
                    IFNULL(({$c_sql}), '0') as item_allnum,
                    
                    IFNULL(({$c_sql}
                            and exists (select 1 from eas_classes_inspec_patrolcheck as p,eas_classes_inspec_patrolcheck_splclass as pc,eas_course_times as tt,eas_course_units as cu where p.patrolcheck_id = pc.patrolcheck_id and p.inspecitem_id = a.inspecitem_id 
                            and pc.times_id = tt.times_id and tt.units_id = cu.units_id and cu.units_sort = u.units_sort and pc.class_id = ct.class_id and p.patrolcheck_status = '1')), '0') as item_comnum,
    
                    IFNULL(({$c_sql}
                            and not exists (select 1 from eas_classes_inspec_patrolcheck as p,eas_classes_inspec_patrolcheck_splclass as pc,eas_course_times as tt,eas_course_units as cu where p.patrolcheck_id = pc.patrolcheck_id and p.inspecitem_id = a.inspecitem_id 
                            and pc.times_id = tt.times_id and tt.units_id = cu.units_id and cu.units_sort = b.units_sort and pc.class_id = ct.class_id and p.patrolcheck_status = '1')), 0) as notcheck_nums,
    
                    IFNULL(({$c_sql}
                            and '0' = (select p.patrolcheck_level from eas_classes_inspec_patrolcheck as p,eas_classes_inspec_patrolcheck_splclass as pc,eas_course_times as tt,eas_course_units as cu where p.patrolcheck_id = pc.patrolcheck_id and p.inspecitem_id = a.inspecitem_id 
                            and pc.times_id = tt.times_id and tt.units_id = cu.units_id and cu.units_sort = u.units_sort and pc.class_id = ct.class_id and p.patrolcheck_status = '1' order by p.patrolcheck_updatetime desc,p.patrolcheck_createtime desc limit 1)), 0) as normal_nums,
    
                    IFNULL(({$c_sql}
                            and '1' = (select p.patrolcheck_level from eas_classes_inspec_patrolcheck as p,eas_classes_inspec_patrolcheck_splclass as pc,eas_course_times as tt,eas_course_units as cu where p.patrolcheck_id = pc.patrolcheck_id and p.inspecitem_id = a.inspecitem_id 
                            and pc.times_id = tt.times_id and tt.units_id = cu.units_id and cu.units_sort = u.units_sort and pc.class_id = ct.class_id and p.patrolcheck_status = '1' order by p.patrolcheck_updatetime desc,p.patrolcheck_createtime desc limit 1)), 0) as excellent_nums,
    
                    IFNULL(({$c_sql}
                            and '-1' = (select p.patrolcheck_level from eas_classes_inspec_patrolcheck as p,eas_classes_inspec_patrolcheck_splclass as pc,eas_course_times as tt,eas_course_units as cu where p.patrolcheck_id = pc.patrolcheck_id and p.inspecitem_id = a.inspecitem_id 
                            and pc.times_id = tt.times_id and tt.units_id = cu.units_id and cu.units_sort = u.units_sort and pc.class_id = ct.class_id and p.patrolcheck_status = '1' order by p.patrolcheck_updatetime desc,p.patrolcheck_createtime desc limit 1)), 0) as aberrant_nums
                FROM 
                    eas_classes_tasks as ct
                    LEFT JOIN eas_classes as c ON c.class_id = ct.class_id 
                    LEFT JOIN eas_course_times AS t ON t.times_id = ct.times_id 
                    LEFT JOIN eas_course_units as u ON u.units_id = t.units_id
                WHERE
                    {$datawhere}
                GROUP BY 
                    ct.class_id
                HAVING
                    {$having}
                ORDER BY
                    c.course_branch";

        $data = array();
        if (!empty($paramArray['is_count'])) {
            $db_nums = $this->Show_css->selectClear($sql);
            if ($db_nums) {
                $allnum = count($db_nums);
            } else {
                $allnum = 0;
            }
            $data['allnum'] = $allnum;
        }

        $dataList = $this->Show_css->selectClear($sql . " LIMIT {$pagestart},{$num}");
        if ($dataList) {
            if (!empty($paramArray['units_sort'])) {
                foreach ($dataList as &$value) {
                    $timewhere = "a.inspecitem_id = i.inspecitem_id and i.inspectype_id = t.inspectype_id and a.units_id = u.units_id and a.course_branch = cs.course_branch and a.course_branch = '{$value['course_branch']}' and u.units_sort = '{$paramArray['units_sort']}' and cs.class_id = '{$value['class_id']}' and a.inspectasks_status = '1' and i.inspecitem_status = '1'";
                    if (in_array($this->schoolOne['school_class'], array("1", "3"))) {
                        $timewhere .= $classwhere;
                    }
                    $itemList = $this->Show_css->selectClear("SELECT i.inspecitem_id,i.inspecitem_name FROM eas_course_inspectasks as a,eas_course_inspecitem as i,eas_course_inspectype as t,eas_course_units as u,eas_classes as cs WHERE {$timewhere}");
                    if ($itemList) {
                        foreach ($itemList as &$item) {
                            $patrolcheck = $this->Show_css->selectOne("SELECT p.patrolcheck_level FROM eas_classes_inspec_patrolcheck_splclass as pc,eas_classes_inspec_patrolcheck as p,eas_course_times as t,eas_course_units as u WHERE pc.patrolcheck_id = p.patrolcheck_id and pc.times_id = t.times_id and t.units_id = u.units_id and pc.class_id = '{$value['class_id']}' and u.units_sort = '{$value['units_sort']}' and p.inspecitem_id = '{$item['inspecitem_id']}' and p.patrolcheck_status = '1' ORDER BY p.patrolcheck_updatetime DESC,p.patrolcheck_createtime DESC");
                            if ($patrolcheck) {
                                $item['check_status'] = $patrolcheck['patrolcheck_level'];
                            } else {
                                $item['check_status'] = '-2';
                            }
                        }
                        $value['item_list'] = $itemList;
                    }
                }
            }
        } else {
            $dataList = array();
        }

        $data['list'] = $dataList;
        return $data;
    }

    //班级主题详情
    function getClassThemeDetailApi($paramArray)
    {
        $datawhere = "c.class_id = '{$paramArray['class_id']}' and u.units_sort = '{$paramArray['units_sort']}' and a.inspectasks_type = '1' and a.inspectasks_status = '1' and i.inspecitem_status = '1' and t.inspectype_status = '1'";
//        $datawhere .= "and exists (select 1 from eas_course_inspecitem_splpost as p where p.inspecitem_id = a.inspecitem_id and p.post_id = '{$this->postbeOne['post_id']}')";
        if (in_array($this->schoolOne['school_class'], array("1", "3"))) {
            $datawhere .= " and (
                            c.class_naturejson = '' or c.class_naturejson = '{\"chinese\":\"1\",\"english\":\"1\",\"maths\":\"1\"}'
                            or (t.inspectype_classnature = 0 and (c.class_naturejson like '%\"chinese\":\"1\"%' or c.class_naturejson like '%\"english\":\"1\"%' or c.class_naturejson like '%\"maths\":\"1\"%'))
                            or (FIND_IN_SET(1, t.inspectype_classnature) and c.class_naturejson like '%\"chinese\":\"1\"%')
                            or (FIND_IN_SET(2, t.inspectype_classnature) and c.class_naturejson like '%\"english\":\"1\"%')
                            or (FIND_IN_SET(3, t.inspectype_classnature) and c.class_naturejson like '%\"maths\":\"1\"%')
                        )";
        }

        if (!empty($paramArray['p'])) {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (!empty($paramArray['num'])) {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT 
                    a.inspectasks_id,i.inspecitem_id,i.inspecitem_name,i.inspecitem_content,t.inspectype_name,c.class_id,c.course_branch,u.units_id
                FROM 
                    eas_course_inspectasks as a
                    LEFT JOIN eas_course_inspecitem as i ON i.inspecitem_id = a.inspecitem_id
                    LEFT JOIN eas_course_inspectype as t ON t.inspectype_id = i.inspectype_id
                    LEFT JOIN eas_classes as c ON c.course_branch = a.course_branch
                    LEFT JOIN eas_course_units as u ON u.units_id = a.units_id
                WHERE 
                    {$datawhere}
                LIMIT 
                    {$pagestart},{$num}";
        $dataList = $this->Show_css->selectClear($sql);
        if ($dataList) {
            foreach ($dataList as &$value) {
                $unitSort = $this->Show_css->selectOne("SELECT cu.units_sort FROM eas_classes_tasks as ct,eas_course_times as ts,eas_course_units as cu WHERE ct.times_id = ts.times_id and ts.units_id = cu.units_id and ct.class_id = '{$value['class_id']}' ORDER BY cu.units_sort DESC");
                if ($unitSort['units_sort'] !== $paramArray['units_sort']) {
                    $value['is_disables'] = '1';
                } else {
                    $value['is_disables'] = '0';
                }
                $checkList = $this->Show_css->selectClear("SELECT 
                                                                    p.patrolcheck_id,p.patrolcheck_type,p.patrolcheck_level,p.patrolcheck_content,p.patrolcheck_imgsurl,p.patrolcheck_day,
                                                                    IF(p.patrolcheck_updatetime, FROM_UNIXTIME(p.patrolcheck_updatetime, '%Y-%m-%d %H:%i'), FROM_UNIXTIME(p.patrolcheck_createtime, '%Y-%m-%d %H:%i')) as createtime,
                                                                    t.teacher_cnname,t.teacher_enname,ct.times_id,ct.times_name
                                                                FROM 
                                                                    eas_classes_inspec_patrolcheck_splclass as pc
                                                                    LEFT JOIN eas_classes_inspec_patrolcheck as p ON p.patrolcheck_id = pc.patrolcheck_id
                                                                    LEFT JOIN app_teacher as t ON t.teacher_id = p.teacher_id 
                                                                    LEFT JOIN eas_course_times as ct ON ct.times_id = pc.times_id 
                                                                WHERE 
                                                                    pc.class_id = '{$paramArray['class_id']}' and ct.units_id = '{$value['units_id']}' and p.inspecitem_id = '{$value['inspecitem_id']}' and p.patrolcheck_status = '1'
                                                                ORDER BY 
                                                                    p.patrolcheck_updatetime DESC,p.patrolcheck_createtime DESC");
                if ($checkList) {
                    $value['patrolcheck'] = $checkList;
                    if ($checkList[0]['patrolcheck_level'] == '-1') {
                        $value['check_status'] = '-1';//异常检核
                    } else {
                        $value['check_status'] = '0';//新增检核
                    }
                } else {
                    $value['check_status'] = '1';//立即检核
                    $value['patrolcheck'] = array();
                }
            }
        } else {
            $dataList = array();
        }

        $data = array();
        $timesOne = $this->Show_css->selectOne("SELECT c.class_cnname,t.times_name,u.units_sort FROM eas_classes_tasks as ct,eas_classes as c,eas_course_times as t,eas_course_units as u WHERE ct.class_id = c.class_id and ct.times_id = t.times_id and t.units_id = u.units_id and ct.class_id = '{$paramArray['class_id']}' ORDER BY t.times_sort DESC");
        $data['times'] = $timesOne;
        $data['list'] = $dataList;
        return $data;
    }

    //获取听课记录
    function getLectureRecordApi($paramArray)
    {
        $datawhere = "ct.school_id = '{$paramArray['school_id']}' and t.inspectype_passage = '{$this->schoolOne['school_nature']}' and ts.times_sort = '{$paramArray['times_sort']}' and t.inspectype_status = '1' and a.inspectasks_status = '1' and b.lsscore_status = '1' and a.inspectasks_type = '2'";

        if (!empty($paramArray['p'])) {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (!empty($paramArray['num'])) {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT 
                    t.inspectype_id,t.inspectype_name,t.inspectype_accordnum,a.course_branch,u.units_sort
                FROM 
                    eas_course_inspectasks as a
                    LEFT JOIN eas_course_inspec_lsscore as b ON b.lsscore_id = a.lsscore_id
                    LEFT JOIN eas_course_inspectype as t ON t.inspectype_id = b.inspectype_id
                    LEFT JOIN eas_course_times as ts ON ts.course_branch = a.course_branch
                    LEFT JOIN eas_course_units as u ON u.units_id = ts.units_id
                    LEFT JOIN eas_classes_tasks as ct ON ct.times_id = ts.times_id
                WHERE
                    {$datawhere}
                GROUP BY
                    t.inspectype_id";

        $data = array();
        if (!empty($paramArray['is_count'])) {
            $db_nums = $this->Show_css->selectClear($sql);
            if ($db_nums) {
                $allnum = count($db_nums);
            } else {
                $allnum = 0;
            }
            $data['allnum'] = $allnum;
        }

        $dataList = $this->Show_css->selectClear($sql . " LIMIT {$pagestart},{$num}");
        if ($dataList) {
            foreach ($dataList as &$value) {
                $paramArr = array();
                $paramArr['inspectype_id'] = $value['inspectype_id'];
                $paramArr['units_sort'] = $paramArray['units_sort'];
                $paramArr['times_sort'] = $paramArray['times_sort'];
                if ($this->getLslistensClassApi($paramArr)) {
                    $value['is_increase'] = '1';
                } else {
                    $value['is_increase'] = '0';
                }
                $param = array();
                $param['inspectype_id'] = $value['inspectype_id'];
                $param['times_sort'] = $paramArray['times_sort'];
                $value['record'] = $this->getLectureRecordOne($param);
                if ($value['record']) {
                    $value['com_nums'] = count($value['record']);
                } else {
                    $value['com_nums'] = 0;
                }
            }
        } else {
            $dataList = array();
        }

        $abnormalOne = $this->Show_css->selectClear("SELECT a.patrolcheck_id
FROM 
    eas_classes_inspec_patrolcheck as a
    LEFT JOIN eas_classes_inspec_patrolcheck_splclass as b ON b.patrolcheck_id = a.patrolcheck_id
    LEFT JOIN eas_course_times as c ON c.times_id = b.times_id 
    LEFT JOIN eas_course_units as u ON u.units_id = c.units_id 
    LEFT JOIN eas_course_inspectasks as d ON d.course_branch = c.course_branch and d.inspecitem_id = a.inspecitem_id and d.units_id = c.units_id
WHERE 
    a.school_id = '{$paramArray['school_id']}' and a.teacher_id = '{$paramArray['teacher_id']}' and a.patrolcheck_level = '-1' and a.patrolcheck_status = '1' and d.inspectasks_status = '1'
  and (select cu.units_sort from eas_classes_tasks as ct,eas_course_times as t,eas_course_units as cu where ct.times_id = t.times_id and t.units_id = cu.units_id and ct.class_id = b.class_id and t.course_branch = c.course_branch order by t.times_sort desc limit 1) <= u.units_sort
  and (select x.patrolcheck_level from eas_classes_inspec_patrolcheck as x,eas_classes_inspec_patrolcheck_splclass as y where x.patrolcheck_id = y.patrolcheck_id and x.inspecitem_id = a.inspecitem_id and y.class_id = b.class_id and x.patrolcheck_status = '1' order by x.patrolcheck_updatetime desc,x.patrolcheck_createtime desc limit 1) = '-1' 
GROUP BY 
    d.units_id,a.inspecitem_id,b.class_id");
        if ($abnormalOne) {
            $error_one = count($abnormalOne);
        } else {
            $error_one = 0;
        }
        $abnormalTwo = $this->Show_css->selectClear("SELECT a.lslistens_id
FROM 
    eas_classes_inspec_lslistens as a
    LEFT JOIN eas_course_times as c ON c.times_id = a.times_id 
    LEFT JOIN eas_course_units as u ON u.units_id = c.units_id 
    LEFT JOIN eas_course_inspectasks as d ON d.course_branch = c.course_branch and d.inspecitem_id = a.inspecitem_id and d.lsscore_id = a.lsscore_id
WHERE 
    a.school_id = '{$paramArray['school_id']}' and a.teacher_id = '{$paramArray['teacher_id']}' and a.lslistens_score < 70 and a.lslistens_status = '1' and d.inspectasks_status = '1'
  and (select cu.units_sort from eas_classes_tasks as ct,eas_course_times as t,eas_course_units as cu where ct.times_id = t.times_id and t.units_id = cu.units_id and ct.class_id = a.class_id and t.course_branch = c.course_branch order by t.times_sort desc limit 1) <= u.units_sort
  and (select x.lslistens_score from eas_classes_inspec_lslistens as x where x.inspecitem_id = a.inspecitem_id and x.class_id = a.class_id and x.times_id = a.times_id and x.lslistens_status = '1' order by x.lslistens_updatetime desc,x.lslistens_createtime desc limit 1) < 70 
GROUP BY 
    a.inspecitem_id,a.class_id,a.times_id");
        if ($abnormalTwo) {
            $error_two = count($abnormalTwo);
        } else {
            $error_two = 0;
        }
        $draft = $this->Show_css->selectOne("SELECT count(1) as num 
FROM 
    eas_classes_inspec_lslistens as a
    LEFT JOIN eas_course_times as c ON c.times_id = a.times_id
    LEFT JOIN eas_course_inspectasks as d ON d.course_branch = c.course_branch and d.inspecitem_id = a.inspecitem_id and d.lsscore_id = a.lsscore_id
WHERE 
    a.school_id = '{$paramArray['school_id']}' and a.teacher_id = '{$paramArray['teacher_id']}' and c.times_sort = '{$paramArray['times_sort']}' and a.lslistens_status = '0' and d.inspectasks_status = '1'");
        $data['draft_nums'] = $draft['num'] ?: 0;
        $data['error_nums'] = $error_one + $error_two;
        $data['list'] = $dataList;
        return $data;
    }

    //获取听课记录详情
    function getLectureRecordOne($paramArray)
    {
        $datawhere = "l.school_id = '{$this->schoolOne['school_id']}' and s.lsscore_status = '1' and it.inspecitem_status = '1' and x.inspectasks_status = '1'";
        if($this->semester == '1'){
            $datawhere .= " and IF(co.course_mold <> 1, co.course_branch IN ('KIRC1', 'KIRC3', 'KIRC5'), (co.course_cnname LIKE '%上学期%' or co.course_cnname LIKE '%上學期%'))";
        }else{
            $datawhere .= " and IF(co.course_mold <> 1, co.course_branch IN ('KIRC2', 'KIRC4', 'KIRC6'), (co.course_cnname LIKE '%下学期%' or co.course_cnname LIKE '%下學期%'))";
        }
        if ($paramArray['inspectype_id']) {
            $datawhere .= " and s.inspectype_id = '{$paramArray['inspectype_id']}'";
        }
        if ($paramArray['course_branch']) {
            $datawhere .= " and c.course_branch = '{$paramArray['course_branch']}'";
        }
        if ($paramArray['times_sort']) {
            $datawhere .= " and ct.times_sort = '{$paramArray['times_sort']}'";
        }
        if ($paramArray['lslistens_id']) {
            $datawhere .= " and l.lslistens_id = '{$paramArray['lslistens_id']}'";
        } else {
            $datawhere .= " and l.lslistens_status = '1'";
        }
        $sql = "SELECT 
                    l.lslistens_id,l.class_id,l.times_id,l.lsscore_id,l.lslistens_type,l.lslistens_class,l.lslistens_day,l.lslistens_score,l.lslistens_content,l.lslistens_evaluate,
                    l.lslistens_name,l.lslistens_place,l.lslistens_photo,l.lslistens_prepare,l.lslistens_target,l.lslistens_process,l.lslistens_appraise,l.lslistens_imgsurl,                    
                    IF(l.lslistens_updatetime, FROM_UNIXTIME(l.lslistens_updatetime, '%Y-%m-%d %H:%i'), FROM_UNIXTIME(l.lslistens_createtime, '%Y-%m-%d %H:%i')) as createtime,
                    s.inspectype_id,s.lsscore_name,it.inspecitem_id,it.inspecitem_name,c.course_branch,c.class_cnname,t.teacher_cnname,
                    (select tc.teacher_cnname from eas_classes_teach as ct,app_teacher as tc where ct.teacher_id = tc.teacher_id and ct.class_id = l.class_id and ct.teach_type = '0' and ct.teach_status = '0' limit 1) as main_teacher_cnname,
                    (select count(1) from eas_classes_studytimes as cs where cs.class_id = l.class_id and cs.times_id = l.times_id and cs.studytimes_status > 0) as stu_nums
                FROM 
                    eas_classes_inspec_lslistens as l
                    LEFT JOIN eas_classes as c ON c.class_id = l.class_id 
                    LEFT JOIN eas_course as co ON co.course_branch = c.course_branch 
                    LEFT JOIN eas_course_times as ct ON ct.times_id = l.times_id 
                    LEFT JOIN app_teacher as t ON t.teacher_id = l.teacher_id 
                    LEFT JOIN eas_course_inspec_lsscore as s ON s.lsscore_id = l.lsscore_id
                    LEFT JOIN eas_course_inspecitem as it ON it.inspecitem_id = l.inspecitem_id
                    LEFT JOIN eas_course_inspectasks as x ON x.course_branch = c.course_branch and x.inspecitem_id = l.inspecitem_id and x.lsscore_id = l.lsscore_id
                WHERE   
                    {$datawhere}
                GROUP BY
	                l.lslistens_id
                HAVING
                    YEAR(createtime) = '{$this->taskyear}'";
        $dataList = $this->Show_css->selectClear($sql);
        if ($dataList) {
            foreach ($dataList as &$value) {
                $timesSort = $this->Show_css->selectOne("SELECT u.units_sort FROM eas_classes_tasks as ct,eas_course_times as t,eas_course_units as u WHERE ct.times_id = t.times_id and t.units_id = u.units_id and ct.class_id = '{$value['class_id']}' ORDER BY t.times_sort DESC");
                if ($timesSort['units_sort'] !== $paramArray['units_sort']) {
                    $value['is_disables'] = '1';
                } else {
                    $value['is_disables'] = '0';
                }
                $itemList = $this->Show_css->selectClear("SELECT itemdetail_id,itemdetail_name FROM eas_course_inspec_lsscore_itemdetail WHERE lsscore_id = '{$value['lsscore_id']}' ORDER BY itemdetail_sort");
                if ($itemList) {
                    foreach ($itemList as &$item) {
                        $normList = $this->Show_css->selectClear("SELECT normdetail_name,normdetail_score,normdetail_sort FROM eas_course_inspec_lsscore_normdetail WHERE itemdetail_id = '{$item['itemdetail_id']}' ORDER BY normdetail_sort");
                        if ($normList) {
                            $item['itemdetail_score'] = array_sum(array_column($normList, 'normdetail_score'));
                            foreach ($normList as &$val) {
                                $normvalue = $this->Show_css->getFieldOne("eas_classes_inspec_lslistens_normvalue", "normvalue_score,normvalue_content", "lslistens_id = '{$value['lslistens_id']}' and itemdetail_id = '{$item['itemdetail_id']}' and normvalue_sort = '{$val['normdetail_sort']}'");
                                $val['normvalue_score'] = $normvalue['normvalue_score'] + 0;
                                $val['normvalue_content'] = $normvalue['normvalue_content'];
                            }
                            $item['normtotal_sroce'] = array_sum(array_column($normList, 'normvalue_score'));
                            $item['normdetail_list'] = $normList;
                        }
                    }
                    $value['itemdetail_list'] = $itemList;
                }
            }
        } else {
            $dataList = array();
        }

        return $dataList;
    }

    //获取听课记录信息
    function getLectureRecordInfo($paramArray)
    {
        $dataList = array();
        $scoreList = $this->Show_css->selectClear("SELECT lsscore_id,lsscore_name FROM eas_course_inspec_lsscore WHERE lsscore_id = '{$paramArray['lsscore_id']}' and lsscore_status = '1'");
        if ($scoreList) {
            foreach ($scoreList as &$value) {
                $itemList = $this->Show_css->selectClear("SELECT itemdetail_id,itemdetail_name FROM eas_course_inspec_lsscore_itemdetail WHERE lsscore_id = '{$value['lsscore_id']}' ORDER BY itemdetail_sort");
                if ($itemList) {
                    foreach ($itemList as &$item) {
                        $normList = $this->Show_css->selectClear("SELECT normdetail_name,normdetail_score,normdetail_sort FROM eas_course_inspec_lsscore_normdetail WHERE itemdetail_id = '{$item['itemdetail_id']}' ORDER BY normdetail_sort");
                        if ($normList) {
                            $item['itemdetail_score'] = array_sum(array_column($normList, 'normdetail_score'));
                            $item['normdetail_list'] = $normList;
                        }
                        $dataList[] = $item;
                    }
                }
            }
        } else {
            $dataList = array();
        }

        $inspectype = $this->Show_css->getFieldOne("eas_course_inspectype", "inspectype_name", "inspectype_id = '{$paramArray['inspectype_id']}'");
        $data['inspectype'] = "Week{$paramArray['times_sort']} {$inspectype['inspectype_name']}听课";
        $data['list'] = $dataList;
        return $data;
    }

    //获取听课班级
    function getLslistensClassApi($paramArray)
    {
        $day = date("Ymd");
        $datawhere = "c.school_branch = '{$this->schoolOne['school_branch']}' and l.inspectype_id = '{$paramArray['inspectype_id']}' and a.inspectasks_status = '1' and l.lsscore_status = '1' and c.class_stdate <= '{$day}' and c.class_enddate >= '{$day}' and c.class_isdel = '0'";
        $datawhere .= " and exists (select 1 from eas_classes_tasks as ct where ct.class_id = c.class_id and ct.times_id = tt.times_id)";
        $datawhere .= " and (select ts.times_sort from eas_classes_tasks as ct,eas_course_times as ts where ct.times_id = ts.times_id and ct.class_id = c.class_id order by ts.times_sort desc limit 1) >= '{$paramArray['times_sort']}'";
        $datawhere .= " and (select cu.units_sort from eas_classes_tasks as ct,eas_course_times as ts,eas_course_units as cu where ct.times_id = ts.times_id and ts.units_id = cu.units_id and ct.class_id = c.class_id order by ts.times_sort desc limit 1) = '{$paramArray['units_sort']}'";
        if (in_array($this->schoolOne['school_class'], array("1", "3"))) {
            $datawhere .= " and (
                            c.class_naturejson = '' or c.class_naturejson = '{\"chinese\":\"1\",\"english\":\"1\",\"maths\":\"1\"}'
                            or (t.inspectype_classnature = 0 and (c.class_naturejson like '%\"chinese\":\"1\"%' or c.class_naturejson like '%\"english\":\"1\"%' or c.class_naturejson like '%\"maths\":\"1\"%'))
                            or (FIND_IN_SET(1, t.inspectype_classnature) and c.class_naturejson like '%\"chinese\":\"1\"%')
                            or (FIND_IN_SET(2, t.inspectype_classnature) and c.class_naturejson like '%\"english\":\"1\"%')
                            or (FIND_IN_SET(3, t.inspectype_classnature) and c.class_naturejson like '%\"maths\":\"1\"%')
                        )";
        }

        $sql = "SELECT 
                    c.class_id,c.class_cnname,c.course_branch,
                    (select t.teacher_cnname from eas_classes_teach as ct,app_teacher as t where ct.teacher_id = t.teacher_id and ct.class_id = c.class_id and ct.teach_type = '0' and ct.teach_status = '0' limit 1) as teacher_cnname,
                    (select count(1) from app_student_study as ss where ss.class_id = c.class_id and ss.study_beginday <= '{$day}' and ss.study_endday >= '{$day}') as stu_nums
                FROM 
                    eas_course_inspec_lsscore as l
                    LEFT JOIN eas_course_inspectasks as a ON a.lsscore_id = l.lsscore_id 
                    LEFT JOIN eas_course_inspectype as t ON t.inspectype_id = l.inspectype_id
                    LEFT JOIN eas_classes as c ON c.course_branch = a.course_branch 
                    LEFT JOIN eas_course_times as tt ON tt.course_branch = a.course_branch
                WHERE 
                    {$datawhere}
                GROUP BY
                    c.class_id
                ORDER BY
                    c.course_branch";

        $dataList = $this->Show_css->selectClear($sql);

        return $dataList ?: array();
    }

    //获取巡班类型
    function getPatrolInSpecTypeApi($paramArray)
    {
        $dataList = $this->Show_css->selectClear("SELECT inspectype_id,inspectype_name FROM eas_course_inspectype WHERE inspectype_passage = '{$this->schoolOne['school_nature']}' and inspectype_status = '1' and inspectype_ispatrol = '1'");

        return $dataList ?: array();
    }

    //获取巡班项目
    function getPatrolInSpecItemApi($paramArray)
    {
        $datawhere = "i.inspecitem_status = '1' and a.inspectasks_type = '1' and a.inspectasks_status = '1'";
        $datawhere .= " and exists (select 1 from eas_course_inspecitem_splpost as p where p.inspecitem_id = a.inspecitem_id and p.post_id = '{$this->postbeOne['post_id']}')";
        if (!empty($paramArray['inspectype_id'])) {
            $datawhere .= " and i.inspectype_id = '{$paramArray['inspectype_id']}'";
        }

        $dataList = $this->Show_css->selectClear("SELECT 
                    i.inspecitem_id,i.inspecitem_name
                FROM 
                    eas_course_inspectasks as a
                    LEFT JOIN eas_course_inspecitem as i ON i.inspecitem_id = a.inspecitem_id 
                WHERE 
                    {$datawhere}
                GROUP BY
                    i.inspecitem_id");

        return $dataList ?: array();
    }

    //获取听课项目
    function getLslistensItemApi($paramArray)
    {
        $datawhere = "a.inspectasks_type = '2' and l.lsscore_status = '1' and a.inspectasks_status = '1' and i.inspecitem_status = '1'";
        $datawhere .= " and exists (select 1 from eas_course_inspecitem_splpost as p where p.inspecitem_id = a.inspecitem_id and p.post_id = '{$this->postbeOne['post_id']}')";
        if (!empty($paramArray['inspectype_id'])) {
            $datawhere .= " and i.inspectype_id = '{$paramArray['inspectype_id']}'";
        }
        if (!empty($paramArray['course_branch'])) {
            $datawhere .= " and a.course_branch = '{$paramArray['course_branch']}'";
        }

        $dataList = $this->Show_css->selectClear("SELECT 
                    l.lsscore_id,l.lsscore_name,i.inspecitem_id,i.inspecitem_name
                FROM 
                    eas_course_inspec_lsscore as l
                    LEFT JOIN eas_course_inspectasks as a ON a.lsscore_id = l.lsscore_id 
                    LEFT JOIN eas_course_inspecitem as i ON i.inspecitem_id = a.inspecitem_id 
                WHERE 
                    {$datawhere}
                GROUP BY
                    i.inspecitem_id");

        return $dataList ?: array();
    }

    //获取异常复检
    function getAberrantRecheckApi($paramArray)
    {
        $datawhere = "a.school_id = '{$paramArray['school_id']}' and a.teacher_id = '{$paramArray['teacher_id']}'";
        $datawheres = "a.school_id = '{$paramArray['school_id']}' and a.teacher_id = '{$paramArray['teacher_id']}'";
        if (!empty($paramArray['class_id'])) {
            $datawhere .= " and b.class_id = '{$paramArray['class_id']}'";
            $datawheres .= " and a.class_id = '{$paramArray['class_id']}'";
        }
        //项目
        if (!empty($paramArray['inspecitem_id'])) {
            $datawhere .= " and a.inspecitem_id = '{$paramArray['inspecitem_id']}'";
        }
        //进度
        if (!empty($paramArray['times_sort'])) {
            $datawheres .= " and c.times_sort = '{$paramArray['times_sort']}'";
        }

        if (!empty($paramArray['p'])) {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (!empty($paramArray['num'])) {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        if ($paramArray['type'] == '1') {
            $sql = "SELECT q.* FROM
                   (SELECT 
                        a.patrolcheck_id,a.inspecitem_id,a.patrolcheck_day,a.patrolcheck_content,a.patrolcheck_imgsurl,e.course_branch,e.units_id,e.units_sort,f.inspecitem_name,h.inspectype_name,
                        c.class_id,c.class_cnname,d.times_id,d.times_name,t.teacher_cnname,t.teacher_enname,FROM_UNIXTIME(a.patrolcheck_createtime,'%Y-%m-%d %H:%i') as createtime,
                        IF((select z.units_sort from eas_classes_tasks as x,eas_course_times as y,eas_course_units as z where x.times_id = y.times_id and y.units_id = z.units_id and x.class_id = c.class_id order by y.times_sort desc limit 1) = e.units_sort, '0', '-1') as is_recheck
                   FROM 
                        eas_classes_inspec_patrolcheck as a
                        LEFT JOIN eas_classes_inspec_patrolcheck_splclass as b ON b.patrolcheck_id = a.patrolcheck_id
                        LEFT JOIN eas_classes as c ON c.class_id = b.class_id 
                        LEFT JOIN eas_course_times as d ON d.times_id = b.times_id 
                        LEFT JOIN eas_course_units as e ON e.units_id = d.units_id
                        LEFT JOIN app_teacher as t ON t.teacher_id = a.teacher_id 
                        LEFT JOIN eas_course_inspecitem as f ON f.inspecitem_id = a.inspecitem_id
                        LEFT JOIN eas_course_inspectasks as g ON g.course_branch = c.course_branch and g.inspecitem_id = a.inspecitem_id and g.units_id = e.units_id
                        LEFT JOIN eas_course_inspectype as h ON h.inspectype_id = f.inspectype_id
                   WHERE
                        {$datawhere} and a.patrolcheck_level = '-1' and a.patrolcheck_status = '1' and f.inspecitem_status = '1' and g.inspectasks_status = '1' and h.inspectype_status = '1' and (select x.patrolcheck_level from eas_classes_inspec_patrolcheck as x,eas_classes_inspec_patrolcheck_splclass as y where x.patrolcheck_id = y.patrolcheck_id and x.inspecitem_id = a.inspecitem_id and y.class_id = b.class_id and x.patrolcheck_status = '1' order by x.patrolcheck_updatetime desc,x.patrolcheck_createtime desc limit 1) = '-1'
                   ORDER BY
                        a.patrolcheck_createtime DESC) as q
                    GROUP BY
                        q.units_id,q.inspecitem_id,q.class_id";

            $classList = $this->Show_css->selectClear("SELECT 
                    c.class_id,c.class_cnname 
                FROM 
                    eas_classes_inspec_patrolcheck as a
                    LEFT JOIN eas_classes_inspec_patrolcheck_splclass as b ON b.patrolcheck_id = a.patrolcheck_id
                    LEFT JOIN eas_classes as c ON c.class_id = b.class_id
                    LEFT JOIN eas_course_times as t ON t.times_id = b.times_id 
                    LEFT JOIN eas_course_inspectasks as d ON d.course_branch = c.course_branch and d.inspecitem_id = a.inspecitem_id and d.units_id = t.units_id
                WHERE
                    a.school_id = '{$paramArray['school_id']}' and a.patrolcheck_level = '-1' and a.patrolcheck_status = '1' and d.inspectasks_status = '1'
                    and (select x.patrolcheck_level from eas_classes_inspec_patrolcheck as x,eas_classes_inspec_patrolcheck_splclass as y where x.patrolcheck_id = y.patrolcheck_id and x.inspecitem_id = a.inspecitem_id and y.class_id = b.class_id and x.patrolcheck_status = '1' order by x.patrolcheck_updatetime desc,x.patrolcheck_createtime desc limit 1) = '-1'
                GROUP BY
                    c.class_id");
        } else {
            $sql = "SELECT q.* FROM
                    (SELECT 
                        a.lslistens_id,a.lslistens_type,a.lslistens_class,a.lslistens_score,a.lslistens_day,a.lslistens_photo,a.lslistens_imgsurl,a.lslistens_content,a.lslistens_evaluate,FROM_UNIXTIME(a.lslistens_createtime,'%Y-%m-%d %H:%i') as createtime,
                        b.class_id,b.class_cnname,c.times_id,c.times_name,c.times_sort,t.teacher_cnname,t.teacher_enname,d.units_sort,e.inspecitem_id,e.inspecitem_name,g.inspectype_id,g.inspectype_name,
                        (select tc.teacher_cnname from eas_classes_teach as te,app_teacher as tc where te.teacher_id = tc.teacher_id and te.class_id = a.class_id and te.teach_type = '0' and te.teach_status = '0' limit 1) as main_teacher_cnname,
                        (select count(1) from eas_classes_studytimes as cs where cs.class_id = a.class_id and cs.times_id = a.times_id and cs.studytimes_status > 0) as stu_nums,
                        IF((select z.units_sort from eas_classes_tasks as x,eas_course_times as y,eas_course_units as z where x.times_id = y.times_id and y.units_id = z.units_id and x.class_id = a.class_id order by y.times_sort desc limit 1) = d.units_sort, '0', '-1') as is_recheck
                    FROM 
                        eas_classes_inspec_lslistens as a
                        LEFT JOIN eas_classes as b ON b.class_id = a.class_id 
                        LEFT JOIN eas_course_times as c ON c.times_id = a.times_id 
                        LEFT JOIN eas_course_units as d ON d.units_id = c.units_id
                        LEFT JOIN app_teacher as t ON t.teacher_id = a.teacher_id 
                        LEFT JOIN eas_course_inspecitem as e ON e.inspecitem_id = a.inspecitem_id
                        LEFT JOIN eas_course_inspectasks as f ON f.course_branch = b.course_branch and f.inspecitem_id = a.inspecitem_id and f.lsscore_id = a.lsscore_id
                        LEFT JOIN eas_course_inspectype as g ON g.inspectype_id = e.inspectype_id
                    WHERE 
                         {$datawheres} and a.lslistens_score < 70 and a.lslistens_status = '1' and e.inspecitem_status = '1' and f.inspectasks_status = '1' 
                         and g.inspectype_status = '1' and (select x.lslistens_score from eas_classes_inspec_lslistens as x where x.inspecitem_id = a.inspecitem_id and x.class_id = a.class_id and x.times_id = a.times_id and x.lslistens_status = '1' order by x.lslistens_updatetime desc,x.lslistens_createtime desc limit 1) < 70
                    ORDER BY
                        a.lslistens_createtime DESC) as q
                    GROUP BY
                         q.inspecitem_id,q.class_id,q.times_id";

            $classList = $this->Show_css->selectClear("SELECT 
                    b.class_id,b.class_cnname 
                FROM 
                    eas_classes_inspec_lslistens as a
                    LEFT JOIN eas_classes as b ON b.class_id = a.class_id 
                    LEFT JOIN eas_course_inspectasks as d ON d.course_branch = b.course_branch and d.inspecitem_id = a.inspecitem_id and d.lsscore_id = a.lsscore_id
                WHERE
                    a.school_id = '{$paramArray['school_id']}' and a.lslistens_score < 70 and a.lslistens_status = '1' and d.inspectasks_status = '1' 
                    and (select x.lslistens_score from eas_classes_inspec_lslistens as x where x.inspecitem_id = a.inspecitem_id and x.class_id = a.class_id and x.times_id = a.times_id and x.lslistens_status = '1' order by x.lslistens_updatetime desc,x.lslistens_createtime desc limit 1) < 70
                GROUP BY
                    b.class_id");
        }

        $dataList = $this->Show_css->selectClear($sql. " LIMIT {$pagestart},{$num}");

        $dataOne = $this->Show_css->selectClear("SELECT a.patrolcheck_id
FROM 
    eas_classes_inspec_patrolcheck as a
    LEFT JOIN eas_classes_inspec_patrolcheck_splclass as b ON b.patrolcheck_id = a.patrolcheck_id
    LEFT JOIN eas_course_times as c ON c.times_id = b.times_id 
    LEFT JOIN eas_course_inspectasks as d ON d.course_branch = c.course_branch and d.inspecitem_id = a.inspecitem_id and d.units_id = c.units_id
WHERE 
    a.school_id = '{$paramArray['school_id']}' and a.teacher_id = '{$paramArray['teacher_id']}' and a.patrolcheck_level = '-1' and a.patrolcheck_status = '1' and d.inspectasks_status = '1'
  and (select x.patrolcheck_level from eas_classes_inspec_patrolcheck as x,eas_classes_inspec_patrolcheck_splclass as y where x.patrolcheck_id = y.patrolcheck_id and x.inspecitem_id = a.inspecitem_id and y.class_id = b.class_id and x.patrolcheck_status = '1' order by x.patrolcheck_updatetime desc,x.patrolcheck_createtime desc limit 1) = '-1' 
GROUP BY 
    c.units_id,a.inspecitem_id,b.class_id");
        $dataTwo = $this->Show_css->selectClear("SELECT a.lslistens_id
FROM 
    eas_classes_inspec_lslistens as a
    LEFT JOIN eas_course_times as c ON c.times_id = a.times_id 
    LEFT JOIN eas_course_inspectasks as d ON d.course_branch = c.course_branch and d.inspecitem_id = a.inspecitem_id and d.lsscore_id = a.lsscore_id
WHERE 
    a.school_id = '{$paramArray['school_id']}' and a.teacher_id = '{$paramArray['teacher_id']}' and a.lslistens_score < 70 and a.lslistens_status = '1' and d.inspectasks_status = '1'
  and (select x.lslistens_score from eas_classes_inspec_lslistens as x where x.inspecitem_id = a.inspecitem_id and x.class_id = a.class_id and x.times_id = a.times_id and x.lslistens_status = '1' order by x.lslistens_updatetime desc,x.lslistens_createtime desc limit 1) < 70 
GROUP BY 
    a.inspecitem_id,a.class_id,a.times_id");
        $data['patrol_num'] = $dataOne ? count($dataOne) : 0;
        $data['listen_num'] = $dataTwo ? count($dataTwo) : 0;
        $data['list'] = $dataList ?: array();
        $data['classList'] = $classList ?: array();
        return $data;
    }


    //获取班级项目巡检
    function getClassInspecItemApi($paramArray)
    {
        $datawhere = "a.school_id = '{$paramArray['school_id']}' and b.class_id = '{$paramArray['class_id']}' and d.units_id = '{$paramArray['units_id']}' and a.patrolcheck_status = '1' and f.inspecitem_status = '1' and g.inspectasks_status = '1' and h.inspectype_status = '1'";
        if (!empty($paramArray['inspecitem_id'])) {
            $datawhere .= " and a.inspecitem_id = '{$paramArray['inspecitem_id']}'";
        }
        if (!empty($paramArray['inspectype_id'])) {
            $datawhere .= " and h.inspectype_id = '{$paramArray['inspectype_id']}'";
        }
        if (!empty($paramArray['patrolcheck_level']) || $paramArray['patrolcheck_level'] == '0') {
            $datawhere .= " and a.patrolcheck_level = '{$paramArray['patrolcheck_level']}'";
        }

        if (!empty($paramArray['p'])) {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (!empty($paramArray['num'])) {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT a.patrolcheck_id,a.inspecitem_id,a.patrolcheck_type,a.patrolcheck_level,a.patrolcheck_content,a.patrolcheck_imgsurl,a.patrolcheck_day,f.inspecitem_name,h.inspectype_name,
                      FROM_UNIXTIME(a.patrolcheck_createtime,'%Y-%m-%d %H:%i') as patrolcheck_createtime,c.class_cnname,d.times_name,t.teacher_cnname,t.teacher_enname
                FROM 
                    eas_classes_inspec_patrolcheck as a
                    LEFT JOIN eas_classes_inspec_patrolcheck_splclass as b ON b.patrolcheck_id = a.patrolcheck_id
                    LEFT JOIN eas_classes as c ON c.class_id = b.class_id 
                    LEFT JOIN eas_course_times as d ON d.times_id = b.times_id 
                    LEFT JOIN app_teacher as t ON t.teacher_id = a.teacher_id 
                    LEFT JOIN eas_course_inspecitem as f ON f.inspecitem_id = a.inspecitem_id
                    LEFT JOIN eas_course_inspectasks as g ON g.course_branch = d.course_branch and g.inspecitem_id = a.inspecitem_id and g.units_id = d.units_id
                    LEFT JOIN eas_course_inspectype as h ON h.inspectype_id = f.inspectype_id
                WHERE 
                    {$datawhere}
                ORDER BY
                    a.patrolcheck_createtime DESC";

        $data = array();
        if (!empty($paramArray['is_count'])) {
            $db_nums = $this->Show_css->selectClear($sql);
            if ($db_nums) {
                $allnum = count($db_nums);
            } else {
                $allnum = 0;
            }
            $data['allnum'] = $allnum;
        }

        $dataList = $this->Show_css->selectClear($sql . " LIMIT {$pagestart},{$num}");
        if ($dataList) {
            $level = array("0" => "正常", "1" => "优秀", "-1" => "异常");
            foreach ($dataList as &$value) {
                $value['teacher_cnname'] = $value['teacher_enname'] ? $value['teacher_cnname'] . '/' . $value['teacher_enname'] : $value['teacher_cnname'];
                $value['patrolcheck_level_name'] = $level[$value['patrolcheck_level']];
                $value['patrolcheck_content'] = $value['patrolcheck_content'] ?: '--';
                $imgsurl = json_decode($value['patrolcheck_imgsurl'], true);
                $value['patrolcheck_imagenums'] = count($imgsurl);
            }
        } else {
            $dataList = array();
        }

        $themeStat = $this->getThemeStatApi($paramArray);
        $total_date = $themeStat['list'];
        $data['checkStat'] = ["excellent_nums" => $total_date[0]['excellent_nums'], "normal_nums" => $total_date[0]['normal_nums'], "aberrant_nums" => $total_date[0]['aberrant_nums']];
        $data['list'] = $dataList;
        return $data;
    }

    //获取项目检核记录
    function getInspecItemRecordApi($paramArray)
    {
        $sql = "SELECT p.patrolcheck_id,p.patrolcheck_type,p.patrolcheck_level,p.patrolcheck_content,p.patrolcheck_imgsurl,p.patrolcheck_day,
                      FROM_UNIXTIME(p.patrolcheck_createtime,'%Y-%m-%d %H:%i') as patrolcheck_createtime,t.teacher_cnname,t.teacher_enname,
                      (select ct.times_name from eas_course_times as ct where ct.times_id = ps.times_id) as times_name
                FROM 
                    eas_classes_inspec_patrolcheck as p,
                    eas_classes_inspec_patrolcheck_splclass as ps,
                    app_teacher as t
                WHERE 
                    p.patrolcheck_id = ps.patrolcheck_id and p.teacher_id = t.teacher_id and p.patrolcheck_status = '1'
                    and p.school_id = '{$paramArray['school_id']}' and p.inspecitem_id = '{$paramArray['inspecitem_id']}' and ps.class_id = '{$paramArray['class_id']}'
                ORDER BY
                    p.patrolcheck_createtime DESC";
        $dataList = $this->Show_css->selectClear($sql);

        return $dataList ?: array();
    }

    //获取班级听课记录
    function getClassListensRecordApi($paramArray)
    {
        $day = date("Ymd");
        $datawhere = "a.school_id = '{$paramArray['school_id']}' and a.class_id = '{$paramArray['class_id']}' and a.lslistens_status = '1' and d.inspectasks_status = '1' and i.inspecitem_status = '1'";
        if (!empty($paramArray['keyword'])) {
            $datawhere .= " and i.inspecitem_name like '%{$paramArray['keyword']}%'";
        }

        if (!empty($paramArray['p'])) {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (!empty($paramArray['num'])) {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT 
                    a.lslistens_id,a.lsscore_id,a.lslistens_class,a.lslistens_score,a.lslistens_day,a.lslistens_content,a.lslistens_evaluate,
                    a.lslistens_name,a.lslistens_place,a.lslistens_photo,a.lslistens_prepare,a.lslistens_target,a.lslistens_process,a.lslistens_appraise,a.lslistens_imgsurl,
                    IF(a.lslistens_updatetime, FROM_UNIXTIME(a.lslistens_updatetime, '%Y-%m-%d %H:%i'), FROM_UNIXTIME(a.lslistens_createtime, '%Y-%m-%d %H:%i')) as createtime,
                    i.inspecitem_name,b.class_id,b.class_cnname,c.times_name,t.teacher_cnname,t.teacher_enname,
                    (select count(1) from app_student_study as ss where ss.class_id = b.class_id and ss.study_beginday <= '{$day}' and ss.study_endday >= '{$day}') as stu_nums
                FROM 
                    eas_classes_inspec_lslistens as a 
                    LEFT JOIN eas_classes as b ON b.class_id = a.class_id 
                    LEFT JOIN eas_course_times as c ON c.times_id = a.times_id 
                    LEFT JOIN app_teacher as t ON t.teacher_id = a.teacher_id 
                    LEFT JOIN eas_course_inspecitem as i ON i.inspecitem_id = a.inspecitem_id
                    LEFT JOIN eas_course_inspectasks as d ON d.course_branch = b.course_branch and d.inspecitem_id = a.inspecitem_id and d.lsscore_id = a.lsscore_id
                WHERE 
                     {$datawhere}
                ORDER BY
                    a.lslistens_createtime DESC";

        $data = array();
        if (!empty($paramArray['is_count'])) {
            $db_nums = $this->Show_css->selectClear($sql);
            if ($db_nums) {
                $allnum = count($db_nums);
            } else {
                $allnum = 0;
            }
            $data['allnum'] = $allnum;
        }

        $dataList = $this->Show_css->selectClear($sql . " LIMIT {$pagestart},{$num}");
        if ($dataList) {
            foreach ($dataList as &$value) {
                $value['teacher_cnname'] = $value['teacher_enname'] ? $value['teacher_cnname'] . '/' . $value['teacher_enname'] : $value['teacher_cnname'];
                $teacher = $this->Show_css->selectOne("select t.teacher_cnname from eas_classes_teach as ct,app_teacher as t where ct.teacher_id = t.teacher_id and ct.class_id = '{$value['class_id']}' and ct.teach_type = '0' and ct.teach_status = '0'");
                $value['main_teacher_cnname'] = $teacher['teacher_cnname'];
                $itemList = $this->Show_css->selectClear("SELECT itemdetail_id,itemdetail_name FROM eas_course_inspec_lsscore_itemdetail WHERE lsscore_id = '{$value['lsscore_id']}' ORDER BY itemdetail_sort");
                if ($itemList) {
                    foreach ($itemList as &$item) {
                        $normList = $this->Show_css->selectClear("SELECT normdetail_name,normdetail_score,normdetail_sort FROM eas_course_inspec_lsscore_normdetail WHERE itemdetail_id = '{$item['itemdetail_id']}' ORDER BY normdetail_sort");
                        if ($normList) {
                            $item['itemdetail_score'] = array_sum(array_column($normList, 'normdetail_score'));
                            foreach ($normList as &$val) {
                                $normvalue = $this->Show_css->getFieldOne("eas_classes_inspec_lslistens_normvalue", "normvalue_score,normvalue_content", "lslistens_id = '{$value['lslistens_id']}' and itemdetail_id = '{$item['itemdetail_id']}' and normvalue_sort = '{$val['normdetail_sort']}'");
                                $val['normvalue_score'] = $normvalue['normvalue_score'] + 0;
                                $val['normvalue_content'] = $normvalue['normvalue_content'];
                            }
                            $item['normtotal_sroce'] = array_sum(array_column($normList, 'normvalue_score'));
                            $item['normdetail_list'] = $normList;
                        }
                    }
                    $value['itemdetail_list'] = $itemList;
                }
            }
        } else {
            $dataList = array();
        }

        $data['list'] = $dataList;
        return $data;
    }
}