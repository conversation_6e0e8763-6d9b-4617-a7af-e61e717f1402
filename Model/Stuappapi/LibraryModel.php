<?php
/**
 * ============================================================================
 * 版权所有 : https://www.mohism.cn
 * 网站地址 : https://www.mohism.cn
 * <AUTHOR> Zhugong <PERSON>
 * Date: 2017/4/14
 * Time: 0:58
 */

namespace Model\Stuappapi;
use Model\modelTpl;

class LibraryModel extends modelTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();

    function __construct()
    {
        parent::__construct();
    }

    function getRecommendReadBooks($request){

        $classOne=$this->Show_css->getFieldOne("eas_classes","coursecat_branch","class_id='{$request['class_id']}'");

        $sql = "select b.readbooks_id,b.readbooks_title,b.readbooks_coverimg,b.readbooks_remark,b.readbooks_stoptime
                ,ifnull((select 1 from app_student_booklist as x where x.readbooks_id=a.readbooks_id and x.student_id='{$request['student_id']}' limit 0,1),0) as isCollection
                ,ifnull((select 1 from app_readbooks_log as x where x.readbooks_id=a.readbooks_id and x.student_id='{$request['student_id']}' and x.log_iscomplete=1 limit 0,1),0) as isComplete
                ,ifnull((select 1 from app_readbooks_log as x where x.readbooks_id=a.readbooks_id and x.student_id='{$request['student_id']}' limit 0,1),0) as isRead
                ,if((b.readbooks_stoptime > curdate()),1,0) as isInvalid 
                from app_readbooks_recommend as a,app_readbooks as b 
                where a.readbooks_id=b.readbooks_id and a.recommend_month=MONTH(CURDATE()) and a.coursecat_branch='{$classOne['coursecat_branch']}' and b.readbooks_stoptime>=CURDATE()
                order by a.recommend_sort asc
                ";

        $booksList=$this->Show_css->selectClear($sql);

        if(!$booksList){
            $this->error = true;
            $this->errortip = "小奇正在维护图书馆，敬请期待哦~";
            return false;
        }

        return $booksList;
    }

    function getReadBooksTask($request){
        $Behaviorlog = new \Model\BehaviorlogModel('App-Library');
        $userecord['member_id'] = $request['member_id'];
        $userecord['school_id'] = $request['school_id'];
        $userecord['class_id'] = $request['class_id'];
        $userecord['student_id'] = $request['student_id'];
        $userecord['userecord_playport'] = $request['program_type'];
        $userecord['userecord_other'] = "V{$request['app_version']}";
        $Behaviorlog->studentUserecord($userecord);

        $classOne=$this->Show_css->getFieldOne("eas_classes","coursecat_branch","class_id='{$request['class_id']}'");

        $sql = "select a.readplan_booksnum 
                from app_readbooks_readplan as a 
                where a.readplan_startmonth<=DATE_FORMAT(NOW(),'%Y-%m') and a.readplan_endmonth>=DATE_FORMAT(NOW(),'%Y-%m') and a.coursecat_branch='{$classOne['coursecat_branch']}'
                order by a.readplan_booksnum desc limit 0,1
                ";

        $planOne=$this->Show_css->selectOne($sql);//每月读书任务总数

        $sql = "select a.readplan_title,a.readplan_booksnum 
                from app_readbooks_readplan as a 
                where a.readplan_startmonth<=DATE_FORMAT(NOW(),'%Y-%m') and a.readplan_endmonth>=DATE_FORMAT(NOW(),'%Y-%m') and a.coursecat_branch='{$classOne['coursecat_branch']}'
                order by a.readplan_booksnum asc
                ";

        $planList=$this->Show_css->selectClear($sql);//每月读书计划

        $sql = "select a.log_id 
                from app_readbooks_log as a 
                where a.student_id='{$request['student_id']}' and from_unixtime(a.log_createtime, '%Y-%m')=DATE_FORMAT(NOW(), '%Y-%m') and a.log_iscomplete=1
                group by a.readbooks_id
                ";
        $logList=$this->Show_css->selectClear($sql);//每月已读书数量


        $sql = "select a.recommend_id 
                from app_readbooks_recommend as a,app_readbooks as b 
                where a.readbooks_id=b.readbooks_id and a.recommend_month=MONTH(CURDATE()) and a.coursecat_branch='{$classOne['coursecat_branch']}'
                ";

        $recommendPlanList=$this->Show_css->selectClear($sql);//每月推荐总数

        $sql = "select a.log_id 
                from app_readbooks_log as a 
                where a.student_id='{$request['student_id']}' 
                -- and from_unixtime(a.log_createtime, '%Y-%m')=DATE_FORMAT(NOW(), '%Y-%m') 
                and a.log_iscomplete=1 
                and exists(select 1 from app_readbooks_recommend as x where x.recommend_month=MONTH(CURDATE()) and x.coursecat_branch='{$classOne['coursecat_branch']}' and x.readbooks_id=a.readbooks_id)
                group by a.readbooks_id
                ";
        $recommendList=$this->Show_css->selectClear($sql);//每月已读推荐书数量

        $data=array();
        $data['planNum']=$planOne?$planOne['readplan_booksnum']:0;//每月总需读书
        $data['hasreadNum']=$logList?count($logList):0;//当月已完成读书
        $data['recommendNum']=$recommendPlanList?count($recommendPlanList):0;//当月总推荐数
        $data['hasrecommendNum']=$recommendList?count($recommendList):0;//当月已读推荐数
        $data['planList']=$planList?$planList:array();//任务计划列表

        return $data;


    }

    function getCateReadBooksList($request){

        $nowtime = date("Y-m-d",time());
        $datawhere = " 1 and a.readbooks_starttime <= '{$nowtime}' and a.readbooks_stoptime >= '{$nowtime}' ";

        if (isset($request['readbooks_language']) && $request['readbooks_language'] !== '') {
            $datawhere .= " and a.readbooks_language = '{$request['readbooks_language']}' ";
        } else {
            $datawhere .= " and a.readbooks_language = '1' ";
        }

        $sql = "select y.* 
                from (select xx.*
                ,@rownum:=@rownum+1 
                ,if(@pdept=xx.readbooks_class,@rank:=@rank+1,@rank:=1) as num
                ,@pdept:=xx.readbooks_class
                from(select a.readbooks_id,a.readbooks_class,a.readbooks_title,a.readbooks_remark,a.readbooks_coverimg,a.readbooks_stoptime,b.list_name
                ,ifnull((select 1 from app_student_booklist as x where x.readbooks_id=a.readbooks_id and x.student_id='{$request['student_id']}' limit 0,1),0) as isCollection
                ,ifnull((select 1 from app_readbooks_log as x where x.readbooks_id=a.readbooks_id and x.student_id='{$request['student_id']}' and x.log_iscomplete=1 limit 0,1),0) as isComplete
                ,ifnull((select 1 from app_readbooks_log as x where x.readbooks_id=a.readbooks_id and x.student_id='{$request['student_id']}' limit 0,1),0) as isRead 
                from app_readbooks as a,cms_variablelist as b
                where {$datawhere} and b.variable_id=1 and a.readbooks_class=b.list_parameter and a.readbooks_stoptime>=CURDATE()
                order by b.list_weight asc,a.readbooks_class asc) as xx,(select @rownum :=0 , @pdept := null ,@rank:=0) R ) as y  
                having num<=4
                ";

        $booksList=$this->Show_css->selectClear($sql);

        if(!$booksList){
            $this->error = true;
            $this->errortip = "小奇正在维护图书馆，敬请期待哦~";
            return false;
        }

        $tem_data=array();
        foreach($booksList as $booksOne){

            $tem_data[$booksOne['readbooks_class']]['readbooks_class']=$booksOne['readbooks_class'];
            $tem_data[$booksOne['readbooks_class']]['list_name']=$booksOne['list_name'];
            $tem_data[$booksOne['readbooks_class']]['list_url']='';
            $tem_data[$booksOne['readbooks_class']]['list_title_url']='';
            $tem_data[$booksOne['readbooks_class']]['booksList'][]=$booksOne;
        }

        return $tem_data;

    }

    function getReadBooksList($request)
    {

        $sql = "select b.school_language 
                from eas_classes as a 
                inner join app_school as b on b.school_branch=a.school_branch
                where a.class_id='{$request['class_id']}'
                ";

        $classOne=$this->Show_css->selectOne($sql);

        if(!$classOne){
            $this->error = true;
            $this->errortip = "班级不存在";
            return false;
        }


        $sql = "select a.readbooks_id 
                from app_readbooks_log as a 
                where a.student_id='{$request['student_id']}' 
                order by a.log_id desc 
                limit 0,1";
        $logOne=$this->Show_css->selectOne($sql);

        $datawhere = "b.readbooks_starttime<>'' and b.readbooks_isRecommend=0 and b.readbooks_starttime <= curdate() and b.readbooks_stoptime >= curdate() ";

        if (isset($request['readbooks_class']) && $request['readbooks_class'] !== '') {
            $datawhere .= " and b.readbooks_class = '{$request['readbooks_class']}' ";
        }

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and b.readbooks_title like '%{$request['keyword']}%' ";
        }

        if (isset($request['readbooks_language']) && $request['readbooks_language'] !== '') {
            $datawhere .= " and b.readbooks_language = '{$request['readbooks_language']}' ";
        }

        if (isset($request['readbooks_agetype']) && $request['readbooks_agetype'] !== '') {
            $datawhere .= " and b.readbooks_agetype = '{$request['readbooks_agetype']}' ";
        }

        // if($classOne['school_language']=='zh_tw'){
        //     $datawhere .= " and b.readbooks_iscomplex = '1' ";
        // }else{
        //     $datawhere .= " and b.readbooks_iscomplex = '0' ";
        // }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '5';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT b.readbooks_id,b.readbooks_title,b.readbooks_remark,b.readbooks_coverimg,b.readbooks_stoptime,b.readbooks_goldcorrect,b.readbooks_audioUrl
                ,ifnull((select 1 from app_student_booklist as x where x.readbooks_id=b.readbooks_id and x.student_id='{$request['student_id']}' limit 0,1),0) as isCollection
                ,ifnull((select 1 from app_readbooks_log as x where x.readbooks_id=b.readbooks_id and x.student_id='{$request['student_id']}' and x.log_iscomplete=1 limit 0,1),0) as isComplete
                ,ifnull((select 1 from app_readbooks_log as x where x.readbooks_id=b.readbooks_id and x.student_id='{$request['student_id']}' limit 0,1),0) as isRead 
                ,if('{$logOne['readbooks_id']}'=b.readbooks_id,1,0) as is_last_read
                ,if(DATE_SUB(b.readbooks_stoptime, INTERVAL 15 DAY)>=CURDATE(),0,1) as is_will_become_due
                FROM app_readbooks as b
                WHERE {$datawhere} and b.readbooks_stoptime>=CURDATE()
                ";

        $sql .= " limit {$pagestart},{$num}";

        $booksList = $this->Show_css->selectClear($sql);

        if (!$booksList) {
            $this->error = true;
            $this->errortip = "亲爱的小朋友，没有找到你想要的图书哦~";
            return false;
        }

        return $booksList;
    }


    //将UNICODE编码后的内容进行解码 --- 字符串中间不带空格
//    function unicode_decode($name)
//    {
//        //转换编码，将Unicode编码转换成可以浏览的utf-8编码
//        $pattern = '/([\w]+)|(\\\u([\w]{4}))/i';
//        preg_match_all($pattern, $name, $matches);
//        if (!empty($matches))
//        {
//            $name = '';
//            for ($j = 0; $j <count($matches[0]); $j++)
//            {
//                $str = $matches[0][$j];
//                if (strpos($str, '\\u') === 0)
//                {
//                    $code = base_convert(substr($str, 2, 2), 16, 10);
//                    $code2 = base_convert(substr($str, 4), 16, 10);
//                    $c = chr($code).chr($code2);
//                    $c = iconv('UCS-2', 'UTF-8', $c);
//                    $name .= ($j+1)<count($matches[0])?$c.' ':$c;
//                }
//                else
//                {
//                    $name .= ($j+1)<count($matches[0])?$str.' ':$str;
//                }
//            }
//        }
//        return $name;
//    }

    //将UNICODE编码后的内容进行解码
    function unicode_decode($name)
    {
        $name = str_replace(',','\u002c',$name);
        $name = str_replace('.','\u002e',$name);
        $namearray = array();
        $namearray = explode(' ',$name);
        if($namearray){
            foreach ($namearray as &$namevar){
                $pattern = '/([\w]+)|(\\\u([\w]{4}))/i';
                preg_match_all($pattern, $namevar, $matches);
                if (!empty($matches)) {
                    $namevar = '';
                    for ($j = 0; $j < count($matches[0]); $j++) {
                        $str = $matches[0][$j];
                        if (strpos($str, '\\u') === 0) {
                            $code = base_convert(substr($str, 2, 2), 16, 10);
                            $code2 = base_convert(substr($str, 4), 16, 10);
                            $c = chr($code) . chr($code2);
                            $c = iconv('UCS-2', 'UTF-8', $c);
                            $namevar .= $c;
                        } else {
                            $namevar .= $str;
                        }
                    }
                }
            }
            $namestr = implode(' ',$namearray);
            return $namestr;
        }else {
            //转换编码，将Unicode编码转换成可以浏览的utf-8编码
            $pattern = '/([\w]+)|(\\\u([\w]{4}))/i';
            preg_match_all($pattern, $name, $matches);
            if (!empty($matches)) {
                $name = '';
                for ($j = 0; $j < count($matches[0]); $j++) {
                    $str = $matches[0][$j];
                    if (strpos($str, '\\u') === 0) {
                        $code = base_convert(substr($str, 2, 2), 16, 10);
                        $code2 = base_convert(substr($str, 4), 16, 10);
                        $c = chr($code) . chr($code2);
                        $c = iconv('UCS-2', 'UTF-8', $c);
                        $name .= $c;
                    } else {
                        $name .= $str;
                    }
                }
            }
            return $name;
        }
    }

    function getBooksUrl($bookurl){
        //$bookurl = str_replace("https://apish.bookteller.com.cn/", "https://mokebook.kidcastle.com.cn/", $bookurl);
        $userpwd = 'kidcastle:3BkwQRnJ5kPsc8gv';
        $ch = curl_init($bookurl);
        // 基于http的验证
        curl_setopt($ch, CURLOPT_USERPWD, $userpwd);
        // 不要直接输出信息
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        $output = curl_exec($ch);
        curl_close($ch);
        $result = json_decode($output, true);
        if(isset($result['url'])){
            return str_replace("http://", "https://", $result['url']);;
        }else{
            return "";
        }
    }

    function getReadBooksOne($request){
        if (!isset($request['readbooks_id']) || $request['readbooks_id'] == '') {
            $this->error = true;
            $this->errortip = "图书id必须传";
            return false;
        }

        $sql = "select b.readbooks_id,b.readbooks_title,b.readbooks_remark,b.readbooks_coverimg,b.readbooks_url,b.readbooks_stoptime,b.readbooks_goldcorrect,b.readbooks_addgoldtime,b.readbooks_audioUrl,b.readbooks_plateids,b.readbooks_isRecommend,d.list_name as readbooks_class_name,e.list_name as readbooks_agetype_name
                ,c.*
                ,ifnull((select 1 from app_student_booklist as x where x.readbooks_id=b.readbooks_id and x.student_id='{$request['student_id']}' limit 0,1),0) as isCollection
                ,ifnull((select 1 from app_readbooks_log as x where x.readbooks_id=b.readbooks_id and x.student_id='{$request['student_id']}' and x.log_iscomplete=1 limit 0,1),0) as isComplete
                ,ifnull((select 1 from app_readbooks_log as x where x.readbooks_id=b.readbooks_id and x.student_id='{$request['student_id']}' limit 0,1),0) as isRead 
                from app_readbooks as b 
                left join app_readbooks_extend as c on c.readbooks_id=b.readbooks_id
                left join cms_variablelist as d on d.variable_id=1 and d.list_parameter=b.readbooks_class
                left join cms_variablelist as e on e.variable_id=3 and e.list_parameter=b.readbooks_agetype
                where b.readbooks_id = '{$request['readbooks_id']}' limit 0,1";
        $booksOne = $this->Show_css->selectOne($sql);

        if(!$booksOne){
            $this->error = true;
            $this->errortip = "图书不存在";
            return false;
        }

        if(APPVER=='TW'){
            $Model = new \Model\TraditionalModel();

            $booksOne['extend_categoryTitle']=$Model->gb2312_big5($booksOne['extend_categoryTitle']);
            $booksOne['extend_gradeTitle']=$Model->gb2312_big5($booksOne['extend_gradeTitle']);
        }

        $booksOne['plateArray']=array();

        if($booksOne){
            $bookUrlArr = explode("id=",$booksOne['readbooks_url']) ;
            $bookID = trim($bookUrlArr[1]);

            $booksUrl = $this->getBooksUrl($booksOne['readbooks_url']);
            $arrContextOptions=array(
                "ssl"=>array(
                    "verify_peer"=>false,
                    "verify_peer_name"=>false,
                    "allow_self_signed"=>true,
                ),
            );

            $bookString = file_get_contents($booksUrl, false, stream_context_create($arrContextOptions));

            //pageWord
            $pageWord = \Websting::getContent($bookString,'var data = ["','"];');
            $pageWordJson = '["'.$pageWord.'"]';
            $pageWordArr = array();
            if($pageWordArray = json_decode($pageWordJson)){
                foreach ($pageWordArray as $pageWordstr){
                    if($pageWordstr) {//加这个是因为会出现空数组的情况
                        if (strpos($pageWordstr, ' class="pinyin"') !== false) {
                            $pageWordstr = str_replace(' class="pinyin"', '', $pageWordstr);
                            $pageLine = \Websting::getContentarray($pageWordstr, '<div>', '</div>');
                            if ($pageLine) {
                                $pageLineArr = array();
                                $pageLineno = array();
                                foreach ($pageLine as $pageLineStr) {
                                    $pageLineno[] = $pageLineStr;
                                }
                                $pageLineArr[] = $pageLineno;
                            }
                        } elseif (strpos($pageWordstr, '</div>') !== false) {
                            $pageLine = \Websting::getContentarray($pageWordstr, '<div>', '</div>');
                            if ($pageLine) {
                                $pageLineArr = array();
                                $pageLineno = array();
                                foreach ($pageLine as $pageLineStr) {
                                    //$pageLineStr = $this->unicode_decode($pageLineStr);
                                    $pageLineno[] = $pageLineStr;
                                }
                                $pageLineArr[] = $pageLineno;
                            }
                        } else {
                            $pageLine = \Websting::getContentarray($pageWordstr, '<div>', '<div>');
                            if ($pageLine) {
                                $pageLineArr = array();
                                $pageLineno = array();
                                foreach ($pageLine as $pageLineStr) {
                                    //$pageLineStr = $this->unicode_decode($pageLineStr);
                                    $pageLineno[] = $pageLineStr;
                                }
                                $pageLineArr[] = $pageLineno;
                            }
                        }
                        $pageWordArr[] = $pageLineArr;
                    }
                }
            }

            //end_page
            $end_page = \Websting::getContent($bookString,'end_page = ',';');
            $pageNums = $end_page+1;
            for($i = 0;$i <= $end_page; $i++) {
                //图片
                $pageImgArr[] = "https://moker-bookresource.oss-cn-shanghai.aliyuncs.com/pages/{$bookID}/book/{$i}.jpg";//图片
                //音频
                $pageAudioArr[] = "https://moker-bookresource.oss-cn-shanghai.aliyuncs.com/pages/{$bookID}/voice/{$i}.mp3";//音频
            }

            /*$begin ='var data = ["';
            $end ='"];';
            $txt=$this->cut($begin,$end,$bookString);
            $pageWordArr = explode('","',$txt);
            if($pageWordArr){
                foreach ($pageWordArr as &$txtarrvar){
                    $txtarrvar = explode('<\/div><div class=\"pinyin\">',$txtarrvar);
                    if($txtarrvar){
                        foreach ($txtarrvar as &$txtarrvar2){
                            $txtarrvar2 = explode('<\/div><div>',$txtarrvar2);
                            $txtarrvar2 = str_replace('<div class=\"pinyin\">','',$txtarrvar2);
                            $txtarrvar2 = str_replace('<\/div>','',$txtarrvar2);
                            $txtarrvar2 = str_replace('<div>','',$txtarrvar2);
                            if($txtarrvar2){
                                foreach ($txtarrvar2 as &$txtarrvar2var){
                                    //将UNICODE编码后的内容进行解码
                                    $txtarrvar2var = $this->unicode_decode($txtarrvar2var);
                                    //echo $txtarrvar2var;die;
                                }
                            }
                        }
                    }
                }
            }*/

            //标签json加密有会添加 双斜杠，app人员不好处理，可以考虑把字符串 提前进行 url 加密，后返回，最后让 app 人员进行解密   （php函数 加密urlencode($a) 解密urldecode($a)）
            //$txtarray = array();
            //foreach ($txtarr as $txtarrvar){
            //$txtarray[] = urlencode($txtarrvar);
            //}

            /*$begin ='var end_page = ';
            $end ='for (let';
            $pagejson = trim($this->cut($begin,$end,$bookString));
            $pagearray = explode(';',$pagejson);
            $pageNums = trim($pagearray[0])+1;
            $pageNums = ($pageNums == 1 && count($pageWordArr)>1)? count($pageWordArr) : $pageNums;
            for($i = 0;$i < $pageNums; $i++) {
                //图片
                $pageImgArr[$i] = "https://moker-bookresource.oss-cn-shanghai.aliyuncs.com/pages/{$bookID}/book/{$i}.jpg";//图片
                //音频
                $pageAudioArr[$i] = "https://moker-bookresource.oss-cn-shanghai.aliyuncs.com/pages/{$bookID}/voice/{$i}.mp3";//音频
            }*/

            $result = array();
            $result['allpage'] = $pageNums;
            $result['txtarr'] = $pageWordArr;
            $result['imgurl'] = $pageImgArr;
            $result['mp3url'] = $pageAudioArr;
        }

        if($booksOne['readbooks_plateids'] && $booksOne['readbooks_plateids']!=''){

            $plateArray=array("1"=>"核心词汇","2"=>"伴读动画","3"=>"听故事","4"=>"亲子互动");

            $idsArray=explode(",",$booksOne['readbooks_plateids']);

            if($idsArray){
                foreach($idsArray as $plate_id){

                    if($plate_id==1){
                        if(!$this->Show_css->getFieldOne("app_readbooks_summarizations","summarizations_id","readbooks_id='{$request['readbooks_id']}'")){
                            continue;
                        }

                    }elseif($plate_id==2){

                        if(!$booksOne['readbooks_audioUrl'] || $booksOne['readbooks_audioUrl']==''){
                            continue;
                        }

                    }elseif($plate_id==3){

                        if(!$this->Show_css->getFieldOne("app_readbooks_audio","audio_id","readbooks_id='{$request['readbooks_id']}'")){
                            continue;
                        }
                    }elseif($plate_id==4){
                        if((!$booksOne['extend_textReference'] || $booksOne['extend_textReference']=='') && !$this->Show_css->getFieldOne("app_readbooks_challenge","challenge_id","readbooks_id='{$request['readbooks_id']}'") && !$this->Show_css->getFieldOne("app_readbooks_question","question_id","readbooks_id='{$request['readbooks_id']}'")){
                            continue;
                        }
                    }else{
                        continue;
                    }

                    $data=array();
                    $data['plate_id']=$plate_id;
                    $data['plate_name']=$plateArray[$plate_id];

                    $booksOne['plateArray'][]=$data;
                }
            }
        }



        $result['booksOne'] = $booksOne;
        return $result;
    }

    function getReadBooksOnePlateInfo($request){

        if(!isset($request['readbooks_id']) || $request['readbooks_id']==''){
            $this->error = true;
            $this->errortip = "图书id必须传";
            return false;
        }

        if(!isset($request['plate_id']) || $request['plate_id']==''){
            $this->error = true;
            $this->errortip = "请选择图书板块";
            return false;
        }

        $sql = "select a.readbooks_audioUrl,ifnull(b.extend_textReference,'') as extend_textReference 
                from app_readbooks as a 
                left join app_readbooks_extend as b on b.readbooks_id=a.readbooks_id
                where a.readbooks_id='{$request['readbooks_id']}'
                ";
        $raadbooksOne=$this->Show_css->selectOne($sql);

        if(!$raadbooksOne){
            $this->error = true;
            $this->errortip = "图书不存在";
            return false;
        }

        $lastArray=array();

        if($request['plate_id']==1){

            $summarizationsList=$this->Show_css->getList("app_readbooks_summarizations","readbooks_id='{$request['readbooks_id']}'");

            $lastArray['summarizationsList']=$summarizationsList?$summarizationsList:array();

        }elseif($request['plate_id']==2){

            $lastArray['readbooks_audioUrl']=$raadbooksOne['readbooks_audioUrl'];

        }elseif($request['plate_id']==3){

            $audioList=$this->Show_css->getList("app_readbooks_audio","readbooks_id='{$request['readbooks_id']}'");
            if($audioList){
                foreach ($audioList as &$audioVar){
                    $audioVar['bookTextPlain'] = $audioVar['bookTextPlain'];
                }
            }
            $lastArray['audioList']=$audioList?$audioList:array();
        }elseif($request['plate_id']==4){

            $questionList=$this->Show_css->getList("app_readbooks_question","readbooks_id='{$request['readbooks_id']}'");

            $lastArray['questionList']=$questionList?$questionList:array();

            $challengeList=$this->Show_css->getList("app_readbooks_challenge","readbooks_id='{$request['readbooks_id']}'");

            $lastArray['challengeList']=$challengeList?$challengeList:array();

            $lastArray['extend_textReference']=$raadbooksOne['extend_textReference'];

        }

        return $lastArray;
    }

    //给菀直临时用 --- 测试用
    function getWzBooksOne($request){
        if (!isset($request['bookid']) || $request['bookid'] == '') {
            return false;
        }

        $url = "https://apish.bookteller.com.cn/kidcastle?id=".$request['bookid'];
        $booksUrl = $this->getBooksUrl($url);
        return $booksUrl;
    }


    function getStuReadBooksLogList($request){

        $sql = "select a.readbooks_id 
                from app_readbooks_log as a 
                where a.student_id='{$request['student_id']}' 
                order by a.log_id desc 
                limit 0,1";
        $logOne=$this->Show_css->selectOne($sql);

        $datawhere = " b.readbooks_stoptime>=curdate() and b.readbooks_isRecommend=0 ";

        if (isset($request['readbooks_class']) && $request['readbooks_class'] !== '') {
            $datawhere .= " and b.readbooks_class = '{$request['readbooks_class']}' ";
        }

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and b.readbooks_title like '%{$request['keyword']}%' ";
        }

        if (isset($request['readbooks_language']) && $request['readbooks_language'] !== '') {
            $datawhere .= " and b.readbooks_language = '{$request['readbooks_language']}' ";
        }

        if (isset($request['readbooks_agetype']) && $request['readbooks_agetype'] !== '') {
            $datawhere .= " and b.readbooks_agetype = '{$request['readbooks_agetype']}' ";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '5';
        }

        $pagestart = ($page - 1) * $num;

        $sql = "select b.readbooks_id,b.readbooks_class,b.readbooks_title,b.readbooks_remark,b.readbooks_coverimg,b.readbooks_stoptime 
                ,ifnull((select 1 from app_student_booklist as x where x.readbooks_id=b.readbooks_id and x.student_id=a.student_id limit 0,1),0) as isCollection
                ,ifnull((select 1 from app_readbooks_log as x where x.readbooks_id=b.readbooks_id and x.student_id=a.student_id and x.log_iscomplete=1 limit 0,1),0) as isComplete
                ,if('{$logOne['readbooks_id']}'=b.readbooks_id,1,0) as is_last_read
                ,if(DATE_SUB(b.readbooks_stoptime, INTERVAL 15 DAY)>=CURDATE(),0,1) as is_will_become_due
                from app_readbooks_log as a,app_readbooks as b 
                where {$datawhere} and a.readbooks_id=b.readbooks_id and a.student_id='{$request['student_id']}'
                group by a.student_id,a.readbooks_id
                order by max(a.log_createtime) desc
                ";

        $sql .= " limit {$pagestart},{$num}";

        $booksList=$this->Show_css->selectClear($sql);

        if(!$booksList){
            $this->error = true;
            $this->errortip = "有好多有趣的故事书等你来看哦~";
            return false;
        }

        return $booksList;
    }

    function getStuLikeReadBooksList($request){

        $sql = "select a.readbooks_id 
                from app_readbooks_log as a 
                where a.student_id='{$request['student_id']}' 
                order by a.log_id desc 
                limit 0,1";
        $logOne=$this->Show_css->selectOne($sql);

        $datawhere = " b.readbooks_stoptime>=curdate() and b.readbooks_isRecommend=0";

        if (isset($request['readbooks_class']) && $request['readbooks_class'] !== '') {
            $datawhere .= " and b.readbooks_class = '{$request['readbooks_class']}' ";
        }

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and b.readbooks_title like '%{$request['keyword']}%' ";
        }

        if (isset($request['readbooks_language']) && $request['readbooks_language'] !== '') {
            $datawhere .= " and b.readbooks_language = '{$request['readbooks_language']}' ";
        }

        if (isset($request['readbooks_agetype']) && $request['readbooks_agetype'] !== '') {
            $datawhere .= " and b.readbooks_agetype = '{$request['readbooks_agetype']}' ";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '5';
        }

        $pagestart = ($page - 1) * $num;

        $sql = "select b.readbooks_id,b.readbooks_class,b.readbooks_title,b.readbooks_remark,b.readbooks_coverimg,b.readbooks_stoptime 
                ,ifnull((select 1 from app_student_booklist as x where x.readbooks_id=b.readbooks_id and x.student_id=a.student_id limit 0,1),0) as isCollection
                ,ifnull((select 1 from app_readbooks_log as x where x.readbooks_id=b.readbooks_id and x.student_id=a.student_id and x.log_iscomplete=1 limit 0,1),0) as isComplete
                ,ifnull((select 1 from app_readbooks_log as x where x.readbooks_id=b.readbooks_id and x.student_id='{$request['student_id']}' limit 0,1),0) as isRead 
                ,if((b.readbooks_stoptime > curdate()),1,0) as isInvalid 
                ,if('{$logOne['readbooks_id']}'=b.readbooks_id,1,0) as is_last_read
                ,if(DATE_SUB(b.readbooks_stoptime, INTERVAL 15 DAY)>=CURDATE(),0,1) as is_will_become_due
                from app_student_booklist as a
                inner join app_readbooks as b on b.readbooks_id=a.readbooks_id
                left join app_readbooks_extend as c on c.readbooks_id=b.readbooks_id
                where {$datawhere} and a.student_id='{$request['student_id']}'
                order by a.booklist_createtime desc
                ";

        $sql .= " limit {$pagestart},{$num}";

        $booksList=$this->Show_css->selectClear($sql);

        if(!$booksList){
            $this->error = true;
            $this->errortip = "点击x收藏喜欢的图书吧~";
            return false;
        }

        return $booksList;
    }

    function changeReadBooksLikeStatus($request){

        if (strpos($request['member_id'], 'T') !== false) {
            $this->error = true;
            $this->errortip = "教师账号不会提交记录";
            return false;
        }

        $booksOne=$this->Show_css->getFieldOne("app_student_booklist","booklist_id","readbooks_id='{$request['readbooks_id']}' and student_id='{$request['student_id']}'");

        if($booksOne){
            if ($this->Show_css->delData('app_student_booklist', "student_id={$request['student_id']} and readbooks_id='{$request['readbooks_id']}'")) {
                $data=array();
                $data['status']=0;
                return $data;
            } else {
                $this->error = true;
                $this->errortip = "取消收藏失败";
                return false;
            }

        }else{
            $data = array();
            $data['student_id'] = $request['student_id'];
            $data['readbooks_id'] = $request['readbooks_id'];
            $data['booklist_createtime'] = time();

            if($this->Show_css->insertData("app_student_booklist",$data)){
                $data=array();
                $data['status']=1;
                return $data;
            }else{
                $this->error = true;
                $this->errortip = "收藏失败";
                return false;
            }
        }
    }

    function getSearchCondition($request){

        $data=array();

        $k=0;

        $data[$k]['key']='readbooks_agetype';
        $data[$k]['value']=1;
        $data[$k]['name']='适合1~6岁';
        $k++;

        $data[$k]['key']='readbooks_agetype';
        $data[$k]['value']=2;
        $data[$k]['name']='适合6~12岁';
        $k++;

        $data[$k]['key']='readbooks_agetype';
        $data[$k]['value']=3;
        $data[$k]['name']='适合12~18岁';
        $k++;

        $data[$k]['key']='readbooks_language';
        $data[$k]['value']=0;
        $data[$k]['name']='中文';
        $k++;

        $data[$k]['key']='readbooks_language';
        $data[$k]['value']=1;
        $data[$k]['name']='英文';
        $k++;

        $data[$k]['key']='readbooks_class';
        $data[$k]['value']=1;
        $data[$k]['name']='童话系列';
        $k++;

        $data[$k]['key']='readbooks_class';
        $data[$k]['value']=2;
        $data[$k]['name']='亲子阅读';
        $k++;

        if(APPVER == 'TW'){
            $data[$k]['key']='readbooks_class';
            $data[$k]['value']=3;
            $data[$k]['name']='繪本系列';
            $k++;
        }else{
            $data[$k]['key']='readbooks_class';
            $data[$k]['value']=3;
            $data[$k]['name']='公主系列';
            $k++;
        }

        return $data;

    }

    function recordActionLog($request){
        if(strpos($request['member_id'],'T') !== false){
            $this->error = 1;
            $this->errortip = "教师账号不会提交记录！";
            return false;
        }
        if (!isset($request['readbooks_id']) || $request['readbooks_id'] == '') {
            $this->error = true;
            $this->errortip = "图书id必须传";
            return false;
        }

        if (!isset($request['log_operatetime']) || $request['log_operatetime'] == '') {
            $this->error = true;
            $this->errortip = "阅读时长必须传";
            return false;
        }

        if (!isset($request['log_class']) || $request['log_class'] == '') {
            $this->error = true;
            $this->errortip = "获取类型必须传";
            return false;
        }

        $booksOne=$this->Show_css->getFieldOne("app_readbooks","readbooks_addgoldtime,readbooks_goldcorrect","readbooks_id='{$request['readbooks_id']}'");

        if(!$booksOne){
            $this->error = true;
            $this->errortip = "图书不存在";
            return false;
        }

//        if(isset($request['log_playprice']) && $request['log_playprice'] >0){
//            $log_playprice=$request['log_playprice'];
//        }else{
//            if($request['log_operatetime']>$booksOne['readbooks_addgoldtime']){
//                $log_playprice=$booksOne['readbooks_goldcorrect'];
//            }else{
//                $log_playprice=0;
//            }
//        }

        if($request['log_operatetime']>$booksOne['readbooks_addgoldtime']){
            $log_playprice=$booksOne['readbooks_goldcorrect'];
        }else{
            $log_playprice=0;
        }

        $data=array();
        $data['readbooks_id']=$request['readbooks_id'];
        $data['student_id']=$request['student_id'];
        $data['class_id']=$request['class_id'];
        $data['plate_id']=$request['plate_id'];
        $data['log_operatetime']=$request['log_operatetime'];
        $data['log_class']=$request['log_class']?$request['log_class']:0;
        $data['log_iscomplete']=$request['log_iscomplete']?$request['log_iscomplete']:0;
        $data['log_playprice']=$log_playprice;
        $data['log_audiourl']=$request['log_audiourl'];
        $data['log_createtime']=time();

        if(isset($request['log_class']) && $request['log_class']==0 && $log_playprice>0){
            $Model = new \Model\Stuappapi\StudentModel($request['student_id']);

            $Model->AddGold("阅读图书",$log_playprice,"学员完成一次阅读操作，获得+{$log_playprice}金币。",$request['class_id']);

        }

        $classOne=$this->Show_css->getFieldOne("eas_classes","coursecat_branch","class_id='{$request['class_id']}'");

        if(isset($request['log_iscomplete']) && $request['log_iscomplete']==1){

            $this->finishPlanTask($request['student_id'],$request['readbooks_id'],$classOne['coursecat_branch'],$request['class_id']);

            $this->checkReadBooksTask($request['member_id'],$request['student_id'],$request['class_id'],$request['readbooks_id'],$request['plate_id']);

        }

        $studentOne=$this->Show_css->getFieldOne("app_student","student_goldprice,student_diamondprice","student_id='{$request['student_id']}'");

        $this->Show_css->insertData("app_readbooks_log",$data);

        if($request['log_class']==0){
            $data = array();
            $data['playprice'] = $log_playprice;
            $data['playclass'] = 0;
            $data['totalPrice'] = $studentOne['student_goldprice'];
        }else{
            $data = array();
            $data['playprice'] = $log_playprice;
            $data['playclass'] = 1;
            $data['totalPrice'] = $studentOne['student_diamondprice'];
        }

        return $data;

    }


    function finishPlanTask($student_id,$readbooks_id,$coursecat_branch,$class_id=0){

        if($this->Show_css->getFieldOne("app_readbooks_log","log_id","student_id='{$student_id}' and from_unixtime(log_createtime, '%Y-%m-%d')=DATE_FORMAT(NOW(), '%Y-%m') and log_iscomplete=1 and readbooks_id='{$readbooks_id}'")){
            return false;
        }

        $sql = "select a.log_id 
                from app_readbooks_log as a 
                where a.student_id='{$student_id}' and from_unixtime(a.log_createtime, '%Y-%m-%d')=DATE_FORMAT(NOW(), '%Y-%m') and a.log_iscomplete=1
                group by a.readbooks_id
                ";
        $logList=$this->Show_css->selectClear($sql);

        $num=($logList?count($logList):0)+1;

        $sql = "select a.readplan_id,a.readplan_goldcoin,a.readplan_booksnum
                from app_readbooks_readplan as a 
                where a.readplan_startmonth<=DATE_FORMAT(NOW(),'%Y-%m') and a.readplan_endmonth>=DATE_FORMAT(NOW(),'%Y-%m') and a.coursecat_branch='{$coursecat_branch}' and a.readplan_booksnum='{$num}'
                order by a.readplan_booksnum desc
                limit 0,1
                ";

        $planOne=$this->Show_css->selectOne($sql);
        if($planOne){
            if($planOne['readplan_goldcoin']>0){
                $Model = new \Model\Stuappapi\StudentModel($student_id);
                $Model->AddGold("阅读图书",$planOne['readplan_goldcoin'],"学员完成一次阅读任务，获得+{$planOne['readplan_goldcoin']}金币。",$class_id);
            }
        }
    }

    function checkReadBooksTask($member_id,$student_id,$class_id,$readbooks_id,$plate_id){


        $moduleStr=$this->getStuModuleStr($class_id,$student_id);

        $datawhere = " 1 ";

        $studentOne=$this->Show_css->getFieldOne("app_student","is_devstudent","student_id='{$student_id}'");

        if($studentOne['is_devstudent']==0 && strpos($member_id, 'T') === false){
            $datawhere.=" AND (d.taskitems_week='' or (from_unixtime(b.classtasks_tasksviewtimes, '%Y-%m-%d')<(DATE_SUB( CURDATE(), INTERVAL WEEKDAY( CURDATE()) + 0 DAY )) or d.taskitems_week<=(WEEKDAY( CURDATE())+1)))";
        }

        $sql = "SELECT
                    a.taskitems_id,a.learnitems_id,ifnull(h.readbooks_id,0) as readbooks_id,h.plate_ids,ifnull(g.learnitemslog_id,0) as learnitemslog_id
                FROM
                    eas_classes_tasks_learnitems AS a
                    INNER JOIN eas_classes_tasks AS b ON a.classtasks_id = b.classtasks_id
                    INNER JOIN eas_classes_studytimes AS c ON c.times_id = b.times_id AND c.class_id = b.class_id AND c.studytimes_status > 0
                    INNER JOIN app_taskitems AS d ON d.taskitems_id = a.taskitems_id
                    INNER JOIN app_taskitems_readbooks AS h on h.taskitems_id=d.taskitems_id
                    INNER JOIN eas_course_times AS f ON f.times_id = d.times_id
                    LEFT JOIN eas_classes_study_notextbook AS e ON e.class_id = c.class_id AND e.student_id = c.student_id AND e.textbook_id = d.textbook_id 
                    LEFT JOIN app_student_learnitemslog AS g ON g.learnitems_id = a.learnitems_id AND g.student_id=c.student_id
                WHERE {$datawhere}
                    AND c.student_id = '{$student_id}' and d.taskitems_tasktype=4 and h.readbooks_id='{$readbooks_id}' and (h.plate_ids='{$plate_id}' or h.plate_ids='0')
                    AND b.class_id = '{$class_id}' 
                    AND b.classtasks_tasksviewtimes<=unix_timestamp(now())
                    AND ( d.textbook_id = 0 OR e.notextbook_id IS NULL ) and g.learnitemslog_id is null 
                    AND EXISTS (
                            SELECT
                                dd.fucmodule_id 
                            FROM
                                pro_carditem AS aa
                                INNER JOIN pro_sales_channel AS bb ON aa.channel_id = bb.channel_id
                                INNER JOIN pro_sales_fucmodule AS dd ON dd.plan_id = bb.plan_id
                                INNER JOIN pro_product_fucmodule AS ee ON ee.fucmodule_id = dd.fucmodule_id
                                INNER JOIN app_student AS ff ON ff.student_branch = aa.student_branch 
                            WHERE
                                aa.course_branch = f.course_branch 
                                AND ff.student_id = c.student_id 
                                AND ee.fucmodule_branch = 'Taskstu' 
                            ) 
                            and (d.fucmodule_branch='' or d.fucmodule_branch in ($moduleStr))
                group by a.taskitems_id
                ORDER BY
                    f.times_sort ASC,a.learnitems_sort ASC";


        $taskList=$this->Show_css->selectClear($sql);   

        if(!$taskList){
            return false;
        }

        $readbooksOne=$this->Show_css->getFieldOne("app_readbooks","readbooks_plateids","readbooks_id='{$readbooks_id}'");

        foreach($taskList as $taskOne){

            $isComTask=0;

            if($taskOne['plate_ids']==0){

                //判断模块是否都完成了

                $readbooksPlateids=explode(',',$readbooksOne['readbooks_plateids']);

                foreach($readbooksPlateids as $readbooksPlateid){
                    $sql = "select * 
                    from app_readbooks_log as a 
                    where a.student_id='{$student_id}' and a.readbooks_id='{$readbooks_id}' and a.log_iscomplete=1 and a.plate_id='{$readbooksPlateid}'
                    order by a.log_createtime desc
                    limit 0,1
                    ";

                    if(!$this->Show_css->selectOne($sql)){
                        return false;
                    }
                }

                $isComTask=1;
            }elseif($taskOne['plate_ids']==$plate_id){
                $sql = "select * 
                    from app_readbooks_log as a 
                    where a.student_id='{$student_id}' and a.readbooks_id='{$readbooks_id}' and a.log_iscomplete=1 and a.plate_id='{$plate_id}'
                    order by a.log_createtime desc
                    limit 0,1
                    ";

                if(!$this->Show_css->selectOne($sql)){
                    return false;
                }

                $isComTask=1;
            }

            if($isComTask==1){
                $StutaskModel = new \Model\Stuappapi\TaskStuModel($class_id);
                $data=array();
                $data['student_id']=$student_id;
                $data['class_id']=$class_id;
                $data['learnitems_id']=$taskOne['learnitems_id'];
                $StutaskModel->SubmitTask($data);
            }

        }
    }


    //截取指定两个字符之间的字符串
    function cut($begin,$end,$str){
        $b = mb_strpos($str,$begin) + mb_strlen($begin);
        $e = mb_strpos($str,$end) - $b;
        return mb_substr($str,$b,$e);
    }
}