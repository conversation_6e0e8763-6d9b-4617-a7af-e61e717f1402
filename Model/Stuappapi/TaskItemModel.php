<?php

namespace Model\Stuappapi;

use Model\modelTpl;

class TaskItemModel extends modelTpl{
    public $taskitemsOne = false;//当前任务
    public $error = false;
    public $errortip = false;
    public $request = array();
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();

    function __construct($learnitems_id,$request) {
        parent::__construct ();
        $this->verdicttaskitems($learnitems_id);
        $this->request=$request;
    }

    function verdicttaskitems($learnitems_id){

        $sql = "select b.*,a.learnitems_id,a.learnitems_content,a.learnitems_diamond,a.learnitems_goldcoin,c.times_name,c.times_sort,e.class_nowtimessort
                ,(select p.playtype_name from app_taskitems_playtype as p where p.playtype_id = b.taskitems_playtype and p.playtype_tasktype = b.taskitems_tasktype) as playtype_name
                from eas_classes_tasks_learnitems as a,app_taskitems as b,eas_course_times as c,eas_classes_tasks as d,eas_classes as e
                where a.taskitems_id=b.taskitems_id and b.times_id=c.times_id and a.classtasks_id=d.classtasks_id and d.class_id=e.class_id
                  and a.learnitems_id='{$learnitems_id}'";

        $this->taskitemsOne = $this->Show_css->selectOne($sql);

        if(!$this->taskitemsOne){
            $this->error = true;
            $this->errortip = "任务不存在";
        }
    }


    //获取关卡返回数组
    function getTaskItemJsonArray($student_id=0){
        $className = "TaskItem_".$this->taskitemsOne['taskitems_tasktype'];
        $data = $this->$className($student_id);
        if($this->error){
            return false;
        }
        return $data;
    }


    //获取关卡基础信息
    function getTaskTitle(){
        $data = array();
        $data['learnitems_id'] = $this->taskitemsOne['learnitems_id'];
        $data['times_id'] = $this->taskitemsOne['times_id'];
        $data['times_name'] = $this->taskitemsOne['times_name'];
        $data['taskitems_title'] = $this->taskitemsOne['taskitems_title'];
        $data['taskitems_tasktype'] = $this->taskitemsOne['taskitems_tasktype'];
        $data['taskitems_taskclass'] = $this->taskitemsOne['taskitems_taskclass'];
        $data['taskitems_modetype'] = $this->taskitemsOne['taskitems_modetype'];
        $data['learnitems_content'] = $this->taskitemsOne['learnitems_content'];
        $data['taskitems_diamondprice'] = $this->taskitemsOne['taskitems_diamondprice'];
        $data['taskitems_id'] = $this->taskitemsOne['taskitems_id'];
        $data['taskitems_isbook'] = $this->taskitemsOne['taskitems_isbook'];

        $data['taskitems_playtype'] = $this->taskitemsOne['taskitems_playtype'];
        $data['playtype_name'] = $this->taskitemsOne['playtype_name']?$this->taskitemsOne['playtype_name']:'';
        return $data;
    }

    //KCTV视频相关
    function TaskItem_0(){
        $taskArray = $this->getTaskTitle();
        $list=$this->Show_css->selectOne("SELECT h.media_id,h.media_name,h.media_url,h.media_outurl,m.can_drag FROM eas_course_media as h,app_taskitems_media as m
                                                          WHERE h.media_id = m.media_id AND m.taskitems_id='{$taskArray['taskitems_id']}'");
        $taskArray['medias'] = $list?$list:(object)array();

        return $taskArray;
    }

    //复习地图相关
    function TaskItem_1(){

        $taskArray = $this->getTaskTitle();

        $sql = "select x.steps_id,y.steps_category,y.games_id,y.playtype_branch
                from app_taskitems_steps as x,app_maps_steps as y  
                where x.steps_id=y.steps_id and x.taskitems_id='{$taskArray['taskitems_id']}'";

        $list=$this->Show_css->selectOne($sql);

        if($list['games_id']>0 && $list['steps_category']==8){
            $GamesModel = new \Model\Stuappapi\GamesModel();
            $list['games_url'] = $GamesModel->gameStepUrl($list['games_id']);
            if($list['games_id'] == '28'){
                $list['is_need_return'] = 0;
            }else{
                $list['is_need_return'] = 1;
            }
        }
        $list['playtype_branch']=$list['playtype_branch']?$list['playtype_branch']:'A01';

        $taskArray['medias'] = $list?$list:(object)array();

        return $taskArray;
    }

    //真题试卷相关
    function TaskItem_2(){

        $taskArray = $this->getTaskTitle();

        $sql = "select x.testpaper_id
                from app_taskitems_testpaper as x 
                where x.taskitems_id='{$taskArray['taskitems_id']}'";

        $list=$this->Show_css->selectOne($sql);

        $taskArray['medias'] = $list?$list:(object)array();

        return $taskArray;
    }

    //线下任务
    function TaskItem_3(){

        $taskArray = $this->getTaskTitle();
        $taskArray['taskitems_url'] = $this->taskitemsOne['taskitems_url'];

        return $taskArray;
    }

    //阅读相关
    function TaskItem_4(){

        $taskArray = $this->getTaskTitle();

        $sql = "select x.readbooks_id,x.plate_ids
                from app_taskitems_readbooks as x 
                where x.taskitems_id='{$taskArray['taskitems_id']}'";

        $list=$this->Show_css->selectOne($sql);

        $taskArray['medias'] = $list?$list:(object)array();

        return $taskArray;
    }


    //亲子任务
    function TaskItem_5(){

        $taskArray = $this->getTaskTitle();

        return $taskArray;
    }


    //趣配音相关
    function TaskItem_6(){

        $taskArray = $this->getTaskTitle();

        $sql = "select x.audiorecords_id,audiorecords_type
                from app_taskitems_audiorecords as x,
	                 app_audiorecords as a
                where x.audiorecords_id = a.audiorecords_id and x.taskitems_id='{$taskArray['taskitems_id']}'";

        $list=$this->Show_css->selectOne($sql);

        $taskArray['medias'] = $list?$list:(object)array();

        return $taskArray;
    }

    //绘本阅读
    function TaskItem_8(){

        $taskArray = $this->getTaskTitle();

        $sql = "select x.picbooks_id,x.taskitems_title
                from irc_taskitems_picbooks as x 
                where x.taskitems_id='{$taskArray['taskitems_id']}' and x.now_times_sort='{$this->taskitemsOne['class_nowtimessort']}' and x.times_sort='{$this->taskitemsOne['times_sort']}'";
        $picOne=$this->Show_css->selectOne($sql);

        if($picOne){
            $list=$picOne;
            $taskArray['taskitems_title']=$picOne['taskitems_title'];

        }else{
            $sql = "select x.picbooks_id
                from app_taskitems_picbooks as x 
                where x.taskitems_id='{$taskArray['taskitems_id']}'";
            $list=$this->Show_css->selectOne($sql);
        }


        $taskArray['medias'] = $list?$list:(object)array();

        return $taskArray;
    }

    //堡贝乐
    function TaskItem_9(){

        $taskArray = $this->getTaskTitle();

        $sql = "select x.textbookcate_id,y.textbookcate_name,x.steps_id
                from app_taskitems_ekidbook as x
                left join app_ekidbook_textbookcate as y on x.textbookcate_id=y.textbookcate_id
                where x.taskitems_id='{$taskArray['taskitems_id']}'";

        $list=$this->Show_css->selectOne($sql);

        if($list){
            $list['is_all']=0;
        }else{
            $list['is_all']=1;
        }

        if($list['steps_id']>0){
            $stepsOne=$this->Show_css->getFieldOne("app_ekidbook_steps","function_id","steps_id='{$list['steps_id']}'");

            $list['is_steps']=1;
            $list['function_id']=$stepsOne['function_id'];
        }else{
            $list['is_steps']=0;
            $list['function_id']='0';
        }

        $taskArray['medias'] = $list?$list:(object)array();

        return $taskArray;
    }

    function TaskItem_10($student_id=0){

        $taskArray = $this->getTaskTitle();

        if($student_id>0){
            $sql = "select a.item_id,b.item_pid,b.item_title,b.item_note,b.item_remark,if(b.item_coverimg<>'',b.item_coverimg,c.class_coverimg) as item_coverimg,b.item_url,b.item_thumbs
                    ,ifnull((select 1 from app_member_mediacollect as x where x.item_id=a.item_id and x.student_id='{$student_id}'),0) as is_collect
                from app_taskitems_blhitem as a
                inner join app_media_item as b on b.item_id=a.item_id
                inner join app_media_class as c on c.class_id=b.class_id
                where a.taskitems_id='{$taskArray['taskitems_id']}'
                order by a.taskitmes_sort asc
                ";
        }else{
            $sql = "select a.item_id,b.item_pid,b.item_title,b.item_note,b.item_remark,if(b.item_coverimg<>'',b.item_coverimg,c.class_coverimg) as item_coverimg,b.item_url,b.item_thumbs
                from app_taskitems_blhitem as a
                inner join app_media_item as b on b.item_id=a.item_id
                inner join app_media_class as c on c.class_id=b.class_id
                where a.taskitems_id='{$taskArray['taskitems_id']}'
                order by a.taskitmes_sort asc
                ";
        }


        $list=$this->Show_css->selectClear($sql);

        $sql = "select a.can_drag,b.class_mode 
                from app_taskitems_blhclass as a 
                inner join app_media_class as b on b.class_id=a.class_id
                where a.taskitems_id='{$taskArray['taskitems_id']}'";
        $classOne=$this->Show_css->selectOne($sql);
        $taskArray['can_drag']=$classOne?$classOne['can_drag']:0;
        $taskArray['class_mode']=$classOne?$classOne['class_mode']:'0';

        $taskArray['medias'] = (object)array();
        $taskArray['mediasArray'] = $list?$list:(object)array();

        return $taskArray;
    }
}