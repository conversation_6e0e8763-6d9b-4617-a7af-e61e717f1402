<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2018/10/6
 * Time: 17:29
 */

namespace Model\Stuappapi;


use Model\modelTpl;

class TaskStuModel extends modelTpl
{
    public $classesOne = false;//当前班级信息
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();

    function __construct($class_id = 0)
    {
        parent::__construct();

        if ($class_id != 0) {
            $this->verdictclass($class_id);
        }
    }

    //验证班级状态
    function verdictclass($class_id)
    {
        $sql = "select a.class_id,a.class_branch,b.course_mold,b.coursecat_branch,b.course_branch,a.school_branch,a.class_nowtimessort,b.course_isexamassess from eas_classes as a,eas_course as b where a.course_branch=b.course_branch and a.class_id = '{$class_id}' limit 0,1";

        $this->classesOne = $this->Show_css->selectOne($sql);
        if (!$this->classesOne) {
            $this->error = true;
            $this->errortip = "班级信息不存在";
            return false;
        }
    }

    function getTimesList($request){
        $studentOne=$this->Show_css->getFieldOne("app_student","student_branch","student_id='{$request['student_id']}'");

        if($this->classesOne['class_branch'] && $studentOne['student_branch']){
            $Model = new \Model\SchoolManageModel();
            $Model->UpdataClassStudytimes($this->classesOne['class_branch'],$studentOne['student_branch']);
        }
        if($this->classesOne['course_mold']==1 || $this->classesOne['course_mold']==2){
            $sql = "select a.times_id,a.times_name,a.times_sort
                ,ifnull((select 1 from eas_classes_studytimes as x,eas_classes_tasks as y where x.class_id=y.class_id and x.times_id=y.times_id and x.student_id='{$request['student_id']}' and x.times_id=a.times_id and x.studytimes_status>=1 and x.class_id='{$this->classesOne['class_id']}'),0) as status,ifnull((SELECT a.taskitems_id
                            FROM
                                eas_classes_tasks_learnitems AS a
                                INNER JOIN eas_classes_tasks AS b ON a.classtasks_id = b.classtasks_id
                                INNER JOIN eas_classes_studytimes AS c ON c.times_id = b.times_id AND c.class_id = b.class_id AND c.studytimes_status > 0
                                INNER JOIN app_taskitems AS d ON d.taskitems_id = a.taskitems_id
                                INNER JOIN eas_course_times AS f ON f.times_id = d.times_id
                                LEFT JOIN eas_classes_study_notextbook AS e ON e.class_id = c.class_id AND e.student_id = c.student_id AND e.textbook_id = d.textbook_id 
                            WHERE b.times_id=a.times_id
                                AND c.student_id = bb.student_id
                                AND b.class_id = bb.class_id
                                AND b.classtasks_tasksviewtimes<=unix_timestamp(now())
                                AND ( d.textbook_id = 0 OR e.notextbook_id IS NULL )
                            ORDER BY a.learnitems_sort asc limit 0,1),'0') as release_status
                ,ifnull((select 1 from eas_classes_studytimes as x,eas_classes_studytimes_effectlog as y where x.student_id='{$request['student_id']}' and x.times_id=a.times_id and x.studytimes_status>=1 and x.class_id='{$this->classesOne['class_id']}' and x.studytimes_id = y.studytimes_id and y.effectlog_status = 1 ),0) as commentstatus
                from eas_course_times as a 
                    inner join eas_classes_studytimes as bb on bb.times_id=a.times_id and bb.student_id='{$request['student_id']}' and bb.studytimes_status>=1 and bb.class_id='{$this->classesOne['class_id']}'

                where a.course_branch='{$this->classesOne['course_branch']}'
                order by a.times_sort asc";
        }else{
            $sql = "select a.times_id,a.times_name,a.times_sort
                ,ifnull((select 1 from eas_classes_studytimes as x,eas_classes_tasks as y where x.class_id=y.class_id and x.times_id=y.times_id and x.student_id='{$request['student_id']}' and x.times_id=a.times_id and x.studytimes_status>=1 and x.class_id='{$this->classesOne['class_id']}'),0) as status,ifnull((select x.classtasks_id from eas_classes_tasks as x where x.times_id=a.times_id and x.class_id='{$request['class_id']}'),'0') as release_status
                ,ifnull((select 1 from eas_classes_studytimes as x,eas_classes_studytimes_effectlog as y where x.student_id='{$request['student_id']}' and x.times_id=a.times_id and x.studytimes_status>=1 and x.class_id='{$this->classesOne['class_id']}' and x.studytimes_id = y.studytimes_id and y.effectlog_status = 1 ),0) as commentstatus,a.course_branch,bb.class_id
                from eas_course_times as a 
                    inner join eas_classes_studytimes as bb on bb.times_id=a.times_id and bb.student_id='{$request['student_id']}' and bb.studytimes_status>=1 and bb.class_id='{$this->classesOne['class_id']}'

                where a.course_branch='{$this->classesOne['course_branch']}'
                order by a.times_sort asc";

        }


        $timesList=$this->Show_css->selectClear($sql);

        $maxtimes = $this->findMaxValue($timesList,'times_sort');

        if($timesList){
            foreach($timesList as &$val){
                $val['taskUrl'] = 'http://ptcapp.'.SITE_URL.'/FunTasks/dist/index';
                $val['reportUrl'] = 'http://ptcapp.'.SITE_URL.'/StudyReport/dist/index';
                if($val['release_status'] > '0'){
                    $val['isRelease'] = '1';
                }else{
                    $val['isRelease'] = '0';
                }

                if($val['commentstatus'] != 1){//没有点评的可以不显示
                    $val['isreport'] = '0';
                }else{
                    $val['isreport'] = '1';
                }

                $issetweek = $this->Show_css->getFieldOne("ptc_dynamic_readreport","readreport_id,dynamic_id","times_id = '{$val['times_id']}' and student_id = '{$request['student_id']}'");
                if($issetweek){
                    $val['isweekreport'] = '1';
                    $val['wdynamic_id'] = $issetweek['dynamic_id'];
                }else{
                    $val['isweekreport'] = '0';
                }

                $mid = $this->Show_css->getFieldOne("eas_course_times_minibookpage_unittheme","unittheme_id","endtimes_id = '{$val['times_id']}' and unittheme_isdel = '0'");
                if($mid['unittheme_id'] > '0'){
                    $issetmonth = $this->Show_css->getFieldOne("ptc_dynamic_readreport","readreport_id,dynamic_id","unittheme_id = '{$mid['unittheme_id']}' and student_id = '{$request['student_id']}'");
                }else{
                    $issetmonth = '';
                }
//                var_dump($issetmonth);
//                var_dump($issetmonth['readreport_id']);
                if($issetmonth){
                    $val['ismonthreport'] = '1';
                    $val['unittheme_id'] = $mid['unittheme_id'];
                    $val['mdynamic_id'] = $issetmonth['dynamic_id'];
                }else{
                    $val['ismonthreport'] = '0';
                }
            }
        }


        if($request['status'] == '1' && $timesList){
            foreach($timesList as $key => $val){
                $isset = $this->Show_css->selectClear("SELECT t.item_id FROM eas_course_times_mediaitem AS t,eas_classes as c WHERE t.course_branch = c.course_branch AND class_id = '{$this->classesOne['class_id']}' and t.times_id = '{$val['times_id']}' limit 0,1");
                if(!$isset){
                    unset($timesList[$key]);
                }
            }
        }

        if(!$timesList){
            $this->error = true;
            $this->errortip = "无课次";
            return false;
        }

        $last_times_id=0;


        if($this->classesOne['course_mold']==1 || $this->classesOne['course_mold']==2) {
            $taskswhere = " (from_unixtime(d.classtasks_tasksviewtimes, '%Y-%m-%d')<(DATE_SUB( CURDATE(), INTERVAL WEEKDAY( CURDATE()) + 0 DAY )) or b.taskitems_week<=(WEEKDAY( CURDATE())+1))";
            $sql = "select b.times_id
                    from eas_classes_tasks_learnitems as a
                    inner join app_taskitems as b on a.taskitems_id=b.taskitems_id
                    inner join eas_classes_tasks as d on d.classtasks_id=a.classtasks_id    
                    where {$taskswhere} and d.class_id='{$this->classesOne['class_id']}'
                    group by b.times_id";
            $taskList = $this->Show_css->selectClear($sql);

            if($taskList){
                $taskList=array_column($taskList,null,'times_id');
            }

            foreach($timesList as &$timesOne){
                if($timesOne['status']==1 && !$taskList[$timesOne['times_id']]){
                    $timesOne['status']=0;
                }

                if($timesOne['status']==1){
                    $last_times_id=$timesOne['times_id'];
                }
                if($timesOne['times_sort'] == '1'){
                    $timesOne['status']=1;
                }
            }

        }else{
            foreach($timesList as &$timesOne){
                if($timesOne['status']==1){
                    $last_times_id=$timesOne['times_id'];
                }

            }
        }

        if($request['type'] == '1'){
            foreach($timesList as $k=>$v){
                if($v['commentstatus'] != 1){//没有点评的可以不显示
                    unset($timesList[$k]);
                }
            }
            foreach($timesList as &$timesOne){
                if($timesOne['status']==1){
                    $last_times_id=$timesOne['times_id'];
                }
            }
        }

        foreach($timesList as &$value){
            if($value['times_id'] <= $last_times_id){
                $value['taskstatus'] = '1';
            }else{
                $value['taskstatus'] = '0';
            }
        }

        $result = array();
        $result['list'] = $timesList;
        $result['classtimes'] = $last_times_id;

        return $result;

    }

    function findMaxValue($array, $column){
        $maxValue = null;
        foreach ($array as $row) {
            if (isset($row[$column]) && ($row[$column] > $maxValue)) {
                $maxValue = $row[$column];
            }
        }
        return $maxValue;
    }


    function getStuTaskCate($request){
        $datawhere=" class_id='{$this->classesOne['class_id']}' ";
        if(isset($request['times_id']) && $request['times_id']!=''){
            $datawhere.="and times_id='{$request['times_id']}'";
        }

        $studentOne = $this->Show_css->getFieldOne("app_student","student_branch,is_devstudent","student_id='{$request['student_id']}'");

        $sql = "select d.fucmodule_id
                from pro_carditem as a
                inner join pro_sales_channel as b on a.channel_id = b.channel_id
                inner join pro_sales_fucmodule as d on d.plan_id = b.plan_id
                inner join pro_product_fucmodule as e on e.fucmodule_id=d.fucmodule_id
                where a.course_branch='{$this->classesOne['course_branch']}' and a.student_branch='{$studentOne['student_branch']}'
                and from_unixtime(a.carditem_invalidtime, '%Y-%m-%d')>=curdate() and (b.channel_finaltime=0 or from_unixtime(b.channel_finaltime, '%Y-%m-%d')>=curdate()) and e.fucmodule_branch='Taskstu'
                group by d.fucmodule_id";

        if(!$this->Show_css->selectOne($sql) || $this->classesOne['coursecat_branch'] == 'TE'){
            $data=array();
            $data['is_right']=0;
            $data['list']=array();
            return $data;
        }else{
            //获取需要展示任务的课次
            $sql = "select a.classtasks_id,a.times_id,a.classtasks_tasksviewtimes
                from eas_classes_tasks as a
                where {$datawhere} and a.class_id='{$request['class_id']}' and a.classtasks_tasksviewtimes<=unix_timestamp(now())
                and exists(select 1 from eas_classes_studytimes as x where x.student_id='{$request['student_id']}' and x.times_id=a.times_id and x.studytimes_status>0)
                order by classtasks_tasksviewtimes desc,times_id desc 
                limit 0,1";

            $classTasksOne=$this->Show_css->selectOne($sql);

            if(!$classTasksOne){
                $this->error = true;
                $this->errortip = "无可用任务";
                return false;
            }

            $tem_taskArray=array();

            $moduleStr=$this->getStuModuleStr($request['class_id'],$request['student_id']);

            //学前
            if($this->classesOne['course_mold']==1 || $this->classesOne['course_mold']==2){

                $taskswhere=" a.classtasks_id='{$classTasksOne['classtasks_id']}' ";

                if(substr($request['member_id'], 0, 1 ) == 'T' || $studentOne['is_devstudent']==1){
                    $taskswhere.=" and from_unixtime(b.classtasks_tasksviewtimes, '%Y-%m-%d')<=CURDATE() ";
                }else{
                    $taskswhere.=" and (from_unixtime(b.classtasks_tasksviewtimes, '%Y-%m-%d')<(DATE_SUB( CURDATE(), INTERVAL WEEKDAY( CURDATE()) + 0 DAY )) or d.taskitems_week<=(WEEKDAY( CURDATE())+1))
                    ";
                }


                $sql = "SELECT a.taskitems_id,a.learnitems_id,c.times_id,d.taskitems_week,d.taskitems_modetype,d.taskitems_tasktype,g.weektype_imgurl,h.colormatch_color,h.colormatch_imgurl,g.weektype_value,d.taskitems_title,d.taskitems_isbook,g.weektype_name
                    ,ifnull((select x.learnitemslog_id from app_student_learnitemslog as x where x.learnitems_id=a.learnitems_id and x.student_id=c.student_id limit 0,1),0) as isComplete
                        FROM
                            eas_classes_tasks_learnitems AS a
                            INNER JOIN eas_classes_tasks AS b ON a.classtasks_id = b.classtasks_id
                            INNER JOIN eas_classes_studytimes AS c ON c.times_id = b.times_id AND c.class_id = b.class_id AND c.studytimes_status > 0
                            INNER JOIN app_taskitems AS d ON d.taskitems_id = a.taskitems_id
                            INNER JOIN eas_course_times AS f ON f.times_id = d.times_id
                            LEFT JOIN eas_classes_study_notextbook AS e ON e.class_id = c.class_id AND e.student_id = c.student_id AND e.textbook_id = d.textbook_id 
                            left join app_taskitems_weektype as g on g.weektype_value=d.taskitems_week
                            left join app_taskitems_colormatch as h on h.colormatch_id=g.colormatch_id
                        WHERE {$taskswhere}
                            AND c.student_id = '{$request['student_id']}' 
                            AND b.class_id = '{$request['class_id']}' 
                            AND b.classtasks_tasksviewtimes<=unix_timestamp(now())
                            AND ( d.textbook_id = 0 OR e.notextbook_id IS NULL )
                            and (d.fucmodule_branch='' or d.fucmodule_branch in ($moduleStr))
                            ORDER BY d.taskitems_sort asc,a.learnitems_sort asc";

                $taskList=$this->Show_css->selectClear($sql);
                if(!$taskList){
                    $this->error = true;
                    $this->errortip = "老师还未发布精彩内容，耐心等待哦！";
                    return false;
                }

                // $weekNameArray=array("0"=>"未知","1"=>"周一","2"=>"周二","3"=>"周三","4"=>"周四","5"=>"周五","6"=>"周六","7"=>"周日");

                foreach($taskList as $taskOne){

                    $taskOne['taskitems_title']=$taskOne['taskitems_title'].'_'.$taskOne['taskitems_id'];

                    $taskOne['taskitems_week']=$taskOne['taskitems_week']?$taskOne['taskitems_week']:0;
                    $tem_taskArray[$taskOne['taskitems_week']]['taskitems_title']=$taskOne['weektype_name'];
                    $tem_taskArray[$taskOne['taskitems_week']]['taskitems_week']=$taskOne['taskitems_week'];
                    $tem_taskArray[$taskOne['taskitems_week']]['weektype_imgurl']=$taskOne['weektype_imgurl'];
                    $tem_taskArray[$taskOne['taskitems_week']]['colormatch_imgurl']=$taskOne['colormatch_imgurl'];
                    $tem_taskArray[$taskOne['taskitems_week']]['colormatch_color']=$taskOne['colormatch_color'];

                    $tem_taskArray[$taskOne['taskitems_week']]['isbaobeile']=0;//是不是堡贝乐任务 0 不是 1是

                    if($tem_taskArray[$taskOne['taskitems_week']]){
                        $tem_taskArray[$taskOne['taskitems_week']]['allNum']+=1;
                    }else{
                        $tem_taskArray[$taskOne['taskitems_week']]['allNum']=1;
                    }
                    if($taskOne['isComplete']>0){
                        $tem_taskArray[$taskOne['taskitems_week']]['comNum']=$tem_taskArray[$taskOne['taskitems_week']]['comNum']?$tem_taskArray[$taskOne['taskitems_week']]['comNum']+1:1;
                    }

                    if(!$tem_taskArray[$taskOne['taskitems_week']]['comNum']){
                        $tem_taskArray[$taskOne['taskitems_week']]['comNum']=0;
                    }

                    $tem_taskArray[$taskOne['taskitems_week']]['taskList'][]=$taskOne;
                }

                $week=date("w")==0?7:date("w");
                $Monday=date('Y-m-d', (time() - ((date('w') == 0 ? 7 : date('w')) - 1) * 24 * 3600));

                if(substr($request['member_id'], 0, 1 ) == 'T'){
                    $week=7;
                }

                $sql = "select a.learnitemslog_id 
                        from app_student_learnitemslog as a 
                        left join eas_classes_tasks_learnitems as b on b.learnitems_id=a.learnitems_id
                        left join app_taskitems as c on c.taskitems_id=b.taskitems_id
                        where b.classtasks_id='{$classTasksOne['classtasks_id']}' and a.student_id='{$request['student_id']}' and c.taskitems_tasktype=9 and c.taskitems_week=5
                        limit 0,1
                        ";

                if(($week>=5 || date("Y-m-d",$classTasksOne['classtasks_tasksviewtimes'])<$Monday) && !$this->Show_css->selectOne($sql) ){

                    if(!$tem_taskArray[5] && strpos($moduleStr, 'Ebookkid')){

                        $sql = "select a.steps_id
                            ,ifnull((select max(x.log_issucceed) from app_ekidbook_steps_log as x where x.steps_id=a.steps_id and x.student_id='{$request['student_id']}' and x.class_id='{$request['class_id']}' limit 0,1 ),0) as log_issucceed
                            from app_ekidbook_steps as a
                            inner join eas_course_times as b on a.week_id=b.times_sort
                            inner join app_ekidbook_textbook as c on a.textbook_id=c.textbook_id
                            inner join app_ekidbook_textbooksuit as d on c.textbooksuit_id=d.textbooksuit_id
                            where b.course_branch='{$this->classesOne['course_branch']}' and b.times_id='{$classTasksOne['times_id']}' and d.textbooksuit_status=0 and d.textbooksuit_upstatus = 1
                            and exists(select 1 from app_textbook_learning as x where x.course_branch=b.course_branch and x.textbook_id=d.textbooksuit_textbook_id)
                            and not exists(SELECT 1 FROM app_school_notextbook AS x,app_school AS y WHERE x.school_id = y.school_id AND y.school_branch = '{$this->classesOne['school_branch']}' AND x.textbook_id = d.textbooksuit_textbook_id)
                             AND EXISTS (SELECT 1 FROM app_ekidbook_textbookcate_apply AS x WHERE x.textbookcate_id = d.textbookcate_id AND x.course_branch = b.course_branch)
                            "
                            ;

                        $Model = new \Model\Stuappapi\ActivateModel();
                        $idBooks = $Model->getEquityBooksList('Ebookkid',$request['student_id'],$request['class_id']);
                        if(!$idBooks){
                            $idBooks=0;
                        }

                        $sql .= " AND d.textbooksuit_textbook_id IN ({$idBooks})";

                        if($this->Show_css->selectOne($sql.' limit 0,1')){

                            $csql= "select a.weektype_imgurl,b.colormatch_color,b.colormatch_imgurl 
                                from app_taskitems_weektype as a 
                                left join app_taskitems_colormatch as b on a.colormatch_id=b.colormatch_id 
                                where a.weektype_value=5";

                            $colorOne=$this->Show_css->selectOne($csql);

                            $tem_taskArray[5]['taskitems_title']='周五';
                            $tem_taskArray[5]['allNum']=1;
                            $tem_taskArray[5]['weektype_imgurl']=$colorOne['weektype_imgurl'];
                            $tem_taskArray[5]['colormatch_color']=$colorOne['colormatch_color'];
                            $tem_taskArray[5]['colormatch_imgurl']=$colorOne['colormatch_imgurl'];
                            $tem_taskArray[5]['isbaobeile']=1;//是不是堡贝乐任务 0 不是 1是

                            $sql.=" having log_issucceed=0";

                            $data=array();

                            if($this->Show_css->selectOne($sql)){
                                $tem_taskArray[5]['comNum']=0;
                                $data['isComplete']=0;
                            }else{
                                $tem_taskArray[5]['comNum']=1;
                                $data['isComplete']=1;
                            }

                            $data['taskitems_id']=0;
                            $data['learnitems_id']=0;
                            $data['taskitems_week']=5;
                            $data['taskitems_modetype']=0;
                            $data['taskitems_tasktype']=100;
                            $tem_taskArray[5]['taskList'][]=$data;
                        }

                    }
                }

                ksort($tem_taskArray);

            }else{
                //少儿
                $taskswhere="a.classtasks_id='{$classTasksOne['classtasks_id']}'";

//                $sql = "SELECT a.taskitems_id,a.learnitems_id,d.taskitems_tasktype,c.times_id,d.taskitems_week,d.taskitems_modetype,g.modetype_name,h.colormatch_color,h.colormatch_imgurl,g.modetype_imgurl,d.taskitems_title,d.taskitems_isbook,g.modetype_sort,f.times_sort
//                        ,ifnull((select x.learnitemslog_id from app_student_learnitemslog as x where x.learnitems_id=a.learnitems_id and x.student_id=c.student_id limit 0,1),0) as isComplete
//                        FROM
//                            eas_classes_tasks_learnitems AS a
//                            INNER JOIN eas_classes_tasks AS b ON a.classtasks_id = b.classtasks_id
//                            INNER JOIN eas_classes_studytimes AS c ON c.times_id = b.times_id AND c.class_id = b.class_id AND c.studytimes_status > 0
//                            INNER JOIN app_taskitems AS d ON d.taskitems_id = a.taskitems_id
//                            INNER JOIN eas_course_times AS f ON f.times_id = d.times_id
//                            LEFT JOIN eas_classes_study_notextbook AS e ON e.class_id = c.class_id AND e.student_id = c.student_id AND e.textbook_id = d.textbook_id
//                            left join app_taskitems_modetype as g on g.modetype_id=d.taskitems_modetype
//                            left join app_taskitems_colormatch as h on h.colormatch_id=g.colormatch_id
//	                        LEFT JOIN cms_variablelist AS i ON i.list_parameter = d.taskitems_tasktype and i.variable_id=9
//                        WHERE {$taskswhere}
//                            AND c.student_id = '{$request['student_id']}'
//                            AND b.class_id = '{$request['class_id']}'
//                            AND b.classtasks_tasksviewtimes<=unix_timestamp(now())
//                            AND ( d.textbook_id = 0 OR e.notextbook_id IS NULL )
//                            AND EXISTS (
//                                    SELECT
//                                        1
//                                    FROM
//                                        pro_carditem AS aa
//                                        INNER JOIN pro_sales_channel AS bb ON aa.channel_id = bb.channel_id
//                                        INNER JOIN pro_sales_fucmodule AS dd ON dd.plan_id = bb.plan_id
//                                        INNER JOIN pro_product_fucmodule AS ee ON ee.fucmodule_id = dd.fucmodule_id
//                                        INNER JOIN app_student AS ff ON ff.student_branch = aa.student_branch
//                                    WHERE
//                                        aa.course_branch = f.course_branch
//                                        AND ff.student_id = c.student_id
//		                                AND (ee.fucmodule_branch = i.list_enname or i.list_enname=''))
//                        ORDER BY g.modetype_sort asc,a.learnitems_sort asc";

                $sql = "SELECT a.taskitems_id,a.learnitems_id,d.taskitems_tasktype,c.times_id,d.taskitems_week,d.taskitems_modetype,g.modetype_name,h.colormatch_color,h.colormatch_imgurl,g.modetype_imgurl,d.taskitems_title,d.taskitems_isbook,g.modetype_sort,f.times_sort
                        ,ifnull((select x.learnitemslog_id from app_student_learnitemslog as x where x.learnitems_id=a.learnitems_id and x.student_id=c.student_id limit 0,1),0) as isComplete
                        FROM
                            eas_classes_tasks_learnitems AS a
                            INNER JOIN eas_classes_tasks AS b ON a.classtasks_id = b.classtasks_id
                            INNER JOIN eas_classes_studytimes AS c ON c.times_id = b.times_id AND c.class_id = b.class_id AND c.studytimes_status > 0
                            INNER JOIN app_taskitems AS d ON d.taskitems_id = a.taskitems_id
                            INNER JOIN eas_course_times AS f ON f.times_id = d.times_id
                            LEFT JOIN eas_classes_study_notextbook AS e ON e.class_id = c.class_id AND e.student_id = c.student_id AND e.textbook_id = d.textbook_id 
                            left join app_taskitems_modetype as g on g.modetype_id=d.taskitems_modetype
                            left join app_taskitems_colormatch as h on h.colormatch_id=g.colormatch_id
                        WHERE {$taskswhere}
                            AND c.student_id = '{$request['student_id']}' 
                            AND b.class_id = '{$request['class_id']}' 
                            AND b.classtasks_tasksviewtimes<=unix_timestamp(now())
                            AND ( d.textbook_id = 0 OR e.notextbook_id IS NULL ) 
                            and (d.fucmodule_branch='' or d.fucmodule_branch in ($moduleStr))
                            ORDER BY g.modetype_sort asc,a.learnitems_sort asc
                            ";

                $taskList=$this->Show_css->selectClear($sql);

                if(!$taskList){
                    $this->error = true;
                    $this->errortip = "本周精彩内容正在准备中";
                    return false;
                }
                $allNum=0;
                $completeNum=0;

                foreach($taskList as $taskOne){
//                    $allNum++;
                    $taskOne['taskitems_title']=$taskOne['taskitems_title'].'_'.$taskOne['taskitems_id'];

                    if($this->classesOne['class_nowtimessort']>0 && $taskOne['taskitems_tasktype']==8){
                        $sql = "select x.picbooks_id,x.taskitems_title
                            from irc_taskitems_picbooks as x 
                            where x.taskitems_id='{$taskOne['taskitems_id']}' and x.now_times_sort='{$this->classesOne['class_nowtimessort']}' and x.times_sort='{$taskOne['times_sort']}'";

                        $picOne=$this->Show_css->selectOne($sql);

                        if($picOne){
                            $taskOne['taskitems_title']=$picOne['taskitems_title'].'_'.$taskOne['taskitems_id'];
                        }
                    }


                    $tem_taskArray[$taskOne['taskitems_modetype']]['taskitems_title']=$taskOne['modetype_name'];
                    $tem_taskArray[$taskOne['taskitems_modetype']]['modetype_imgurl']=$taskOne['modetype_imgurl'];
                    $tem_taskArray[$taskOne['taskitems_modetype']]['colormatch_color']=$taskOne['colormatch_color'];
                    $tem_taskArray[$taskOne['taskitems_modetype']]['colormatch_imgurl']=$taskOne['colormatch_imgurl'];
                    $tem_taskArray[$taskOne['taskitems_modetype']]['modetype_sort']=$taskOne['modetype_sort'];

                    $tem_taskArray[$taskOne['taskitems_modetype']]['isbaobeile']=0;//是不是堡贝乐任务 0 不是 1是

                    if($tem_taskArray[$taskOne['taskitems_modetype']]){
                        $tem_taskArray[$taskOne['taskitems_modetype']]['allNum']+=1;
                    }else{
                        $tem_taskArray[$taskOne['taskitems_modetype']]['allNum']=1;
                    }
                    if($taskOne['isComplete']>0){
                        $tem_taskArray[$taskOne['taskitems_modetype']]['comNum']=$tem_taskArray[$taskOne['taskitems_modetype']]['comNum']?$tem_taskArray[$taskOne['taskitems_modetype']]['comNum']+1:1;
                        $completeNum++;
                    }

                    if(!$tem_taskArray[$taskOne['taskitems_modetype']]['comNum']){
                        $tem_taskArray[$taskOne['taskitems_modetype']]['comNum']=0;
                    }

                    $tem_taskArray[$taskOne['taskitems_modetype']]['taskList'][]=$taskOne;
                }

                $tem_taskArray = $this->arraySort($tem_taskArray, 'modetype_sort', 'asc');

//                ksort($tem_taskArray);

            }

            /*$data=array();
            $data['taskitems_title']='全部';
            $data['allNum']=$allNum;
            $data['comNum']=$completeNum;
            $data['taskList']=$taskList;

            $tem_taskArray[-1]=$data;*/

            $data=array();

            $data['is_right']=1;
            $data['list']=$tem_taskArray;


            if(isset($request['times_id']) && $request['times_id']!='' && $this->classesOne['course_isexamassess']==1){

                if($this->Show_css->getFieldOne("eas_stu_course_module_authority","authority_id","student_id='{$request['student_id']}' and course_branch='{$this->classesOne['course_branch']}' and Consolidatecvd=1")){
                    
                    $sql = "SELECT a.evaluatlevel_id 
                    from staes_evaluatact_evaluatlevel_splcourse as a
                    inner join staes_evaluatact_evaluatlevel as b on b.evaluatlevel_id=a.evaluatlevel_id
                    inner join staes_evaluatexam as c on c.evaluatpaper_id=b.evaluatpaper_id
                    where c.student_id='{$request['student_id']}' and c.class_id='{$request['class_id']}' and a.course_times_id='{$request['times_id']}' and c.evaluatexam_papertime>0
                    limit 0,1
                    ";
                    if($this->Show_css->selectOne($sql)){
                        $data['has_consolidate']=1;
                    }else{
                        $data['has_consolidate']=0;
                    }
                }else{
                    $data['has_consolidate']=0;
                }

                

            }else{
                $data['has_consolidate']=0;
            }

            return $data;

        }



    }

    function arraySort($arr, $keys, $type = 'asc')
    {
        $keysvalue = $new_array = array();
        foreach ($arr as $k => $v) {
            $keysvalue[$k] = $v[$keys];
        }
        $type == 'asc' ? asort($keysvalue) : arsort($keysvalue);
        reset($keysvalue);
        foreach ($keysvalue as $k => $v) {
            $new_array[$k] = $arr[$k];
        }
        return $new_array;
    }

    function getStuUndoneTaskList($request){

        $sql = "select a.times_id 
                from eas_classes_studytimes as a,eas_course_times as b 
                where a.times_id=b.times_id and a.class_id='{$request['class_id']}' and a.student_id='{$request['student_id']}'
                order by b.times_sort desc limit 0,1";
        $timesOne=$this->Show_css->selectOne($sql);

        if(!$timesOne){
            $this->error = true;
            $this->errortip = "无任务";
            return false;
        }

        $moduleStr=$this->getStuModuleStr($request['class_id'],$request['student_id']);

        $datawhere=" 1 ";

        if(isset($request['times_id']) && $request['times_id']!=''){
            $datawhere.=" and d.times_id='{$request['times_id']}'";
        }


        $sql = "SELECT
                    a.taskitems_id,a.learnitems_id,c.times_id,f.times_name,d.taskitems_tasktype,d.taskitems_modetype,h.modetype_imgurl,h.modetype_name,d.taskitems_title,d.taskitems_isbook
                FROM
                    eas_classes_tasks_learnitems AS a
                    INNER JOIN eas_classes_tasks AS b ON a.classtasks_id = b.classtasks_id
                    INNER JOIN eas_classes_studytimes AS c ON c.times_id = b.times_id AND c.class_id = b.class_id AND c.studytimes_status > 0
                    INNER JOIN app_taskitems AS d ON d.taskitems_id = a.taskitems_id
                    INNER JOIN eas_course_times AS f ON f.times_id = d.times_id
                    LEFT JOIN eas_classes_study_notextbook AS e ON e.class_id = c.class_id AND e.student_id = c.student_id AND e.textbook_id = d.textbook_id 
                    LEFT JOIN app_student_learnitemslog AS g ON g.learnitems_id = a.learnitems_id AND g.student_id=c.student_id
                    LEFT JOIN app_taskitems_modetype as h on d.taskitems_modetype=h.modetype_id
                WHERE {$datawhere}
                    AND c.student_id = '{$request['student_id']}' 
                    AND b.class_id = '{$request['class_id']}' 
                    AND b.classtasks_tasksviewtimes<=unix_timestamp(now())
                    AND ( d.textbook_id = 0 OR e.notextbook_id IS NULL ) and g.learnitemslog_id is null 
                    AND (d.taskitems_week='' or (from_unixtime(b.classtasks_tasksviewtimes, '%Y-%m-%d')<(DATE_SUB( CURDATE(), INTERVAL WEEKDAY( CURDATE()) + 0 DAY )) or d.taskitems_week<=(WEEKDAY( CURDATE())+1)))
                    and (d.fucmodule_branch='' or d.fucmodule_branch in ($moduleStr))
                ORDER BY
                    f.times_sort desc,d.taskitems_week asc,d.taskitems_sort asc,a.learnitems_sort ASC";

        $taskList=$this->Show_css->selectClear($sql);
        if(!$taskList){
            $this->error = true;
            $this->errortip = "无任务";
            return false;
        }

        $tem_taskArray=array();
        foreach($taskList as $taskOne){

            $tem_taskArray[$taskOne['times_id']]['times_name']=$taskOne['times_name'];

            $tem_taskArray[$taskOne['times_id']]['taskList'][]=$taskOne;
        }

        $data=array();
        foreach($tem_taskArray as $temOne){
            $data[]=$temOne;
        }

        return $data;

    }

    function getStuTaskHistoryList($request){

        $studentOne=$this->Show_css->getFieldOne("app_student","student_branch,is_devstudent","student_id='{$request['student_id']}'");

        $Model = new \Model\Stuappapi\PowerModel();
        $moduleveyType = $Model->gitfucmoduleType($studentOne['student_branch'],$this->classesOne['course_branch']);

        if(!$moduleveyType){
            $this->error = true;
            $this->errortip = "无权限";
            return false;
        }

        if($this->classesOne['course_mold']==1){

            if(substr($request['member_id'], 0, 1 ) == 'T' || $studentOne['is_devstudent']==1){
                $where=" from_unixtime(b.classtasks_tasksviewtimes, '%Y-%m-%d')<=CURDATE() ";
            }else{
                $where=" (d.taskitems_week='' or (from_unixtime(b.classtasks_tasksviewtimes, '%Y-%m-%d')<(DATE_SUB( CURDATE(), INTERVAL WEEKDAY( CURDATE()) + 0 DAY )) or d.taskitems_week<=(WEEKDAY( CURDATE())+1)))
                    ";
            }

            $sql = "select aa.times_id,aa.times_sort
                    ,ifnull((SELECT a.taskitems_id
                            FROM
                                eas_classes_tasks_learnitems AS a
                                INNER JOIN eas_classes_tasks AS b ON a.classtasks_id = b.classtasks_id
                                INNER JOIN eas_classes_studytimes AS c ON c.times_id = b.times_id AND c.class_id = b.class_id AND c.studytimes_status > 0
                                INNER JOIN app_taskitems AS d ON d.taskitems_id = a.taskitems_id
                                INNER JOIN eas_course_times AS f ON f.times_id = d.times_id
                                LEFT JOIN eas_classes_study_notextbook AS e ON e.class_id = c.class_id AND e.student_id = c.student_id AND e.textbook_id = d.textbook_id 
                            WHERE {$where} and b.times_id=aa.times_id
                                AND c.student_id = bb.student_id
                                AND b.class_id = bb.class_id
                                -- AND b.classtasks_tasksviewtimes<=unix_timestamp(now())
                                AND ( d.textbook_id = 0 OR e.notextbook_id IS NULL )
                            ORDER BY a.learnitems_sort asc limit 0,1),'0') as release_status
                    from eas_course_times as aa 
                    inner join eas_classes_studytimes as bb on bb.times_id=aa.times_id and bb.student_id='{$request['student_id']}' and bb.studytimes_status>=1 and bb.class_id='{$request['class_id']}'
                    where aa.course_branch='{$this->classesOne['course_branch']}'
                    order by aa.times_sort asc
                    ";
        }else{
            $sql = "select a.times_id,a.times_sort
                ,ifnull((select x.classtasks_id from eas_classes_tasks as x where x.times_id=a.times_id and x.class_id='{$request['class_id']}'),'0') as release_status
                from eas_course_times as a 
                inner join eas_classes_studytimes as b on b.times_id=a.times_id and b.student_id='{$request['student_id']}' and b.studytimes_status>=1 and b.class_id='{$request['class_id']}'
                where a.course_branch='{$this->classesOne['course_branch']}'
                order by a.times_sort asc
                ";
        }

        $timesList=$this->Show_css->selectClear($sql);
        if(!$timesList){
            $this->error = true;
            $this->errortip = "无课次";
            return false;
        }

        $moduleStr=$this->getStuModuleStr($request['class_id'],$request['student_id']);

        $datawhere=" 1 ";

        if(isset($request['times_id']) && $request['times_id']!=''){
            $datawhere.=" and d.times_id='{$request['times_id']}'";
        }

        if(substr($request['member_id'], 0, 1 ) == 'T' || $studentOne['is_devstudent']==1){
            $datawhere.=" and from_unixtime(b.classtasks_tasksviewtimes, '%Y-%m-%d')<=CURDATE() ";
        }else{
            $datawhere.=" AND (d.taskitems_week='' or (from_unixtime(b.classtasks_tasksviewtimes, '%Y-%m-%d')<(DATE_SUB( CURDATE(), INTERVAL WEEKDAY( CURDATE()) + 0 DAY )) or d.taskitems_week<=(WEEKDAY( CURDATE())+1)))
                    ";
        }

        $sql = "SELECT
                    a.taskitems_id,a.learnitems_id,c.times_id,f.times_name,d.taskitems_tasktype,d.taskitems_modetype,h.modetype_imgurl,h.modetype_name,d.taskitems_title,ifnull(g.learnitemslog_id,0) as learnitemslog_id,f.times_sort,d.taskitems_isbook
                FROM
                    eas_classes_tasks_learnitems AS a
                    INNER JOIN eas_classes_tasks AS b ON a.classtasks_id = b.classtasks_id
                    INNER JOIN eas_classes_studytimes AS c ON c.times_id = b.times_id AND c.class_id = b.class_id AND c.studytimes_status > 0
                    INNER JOIN app_taskitems AS d ON d.taskitems_id = a.taskitems_id
                    INNER JOIN eas_course_times AS f ON f.times_id = d.times_id
                    LEFT JOIN eas_classes_study_notextbook AS e ON e.class_id = c.class_id AND e.student_id = c.student_id AND e.textbook_id = d.textbook_id 
                    LEFT JOIN app_student_learnitemslog AS g ON g.learnitems_id = a.learnitems_id AND g.student_id=c.student_id
                    LEFT JOIN app_taskitems_modetype as h on d.taskitems_modetype=h.modetype_id
                WHERE {$datawhere}
                    AND c.student_id = '{$request['student_id']}' 
                    AND b.class_id = '{$request['class_id']}' 
                    AND b.classtasks_tasksviewtimes<=unix_timestamp(now())
                    AND ( d.textbook_id = 0 OR e.notextbook_id IS NULL )
                    -- AND (d.taskitems_week='' or (from_unixtime(b.classtasks_tasksviewtimes, '%Y-%m-%d')<(DATE_SUB( CURDATE(), INTERVAL WEEKDAY( CURDATE()) + 0 DAY )) or d.taskitems_week<=(WEEKDAY( CURDATE())+1)))
                    AND EXISTS (
                        SELECT
                            dd.fucmodule_id 
                        FROM
                            pro_carditem AS aa
                            INNER JOIN pro_sales_channel AS bb ON aa.channel_id = bb.channel_id
                            INNER JOIN pro_sales_fucmodule AS dd ON dd.plan_id = bb.plan_id
                            INNER JOIN pro_product_fucmodule AS ee ON ee.fucmodule_id = dd.fucmodule_id
                            INNER JOIN app_student AS ff ON ff.student_branch = aa.student_branch 
                        WHERE
                            aa.course_branch = f.course_branch 
                            AND ff.student_id = c.student_id 
                            AND from_unixtime( aa.carditem_invalidtime, '%Y-%m-%d' )>= CURDATE() 
                            AND ( bb.channel_finaltime = 0 OR from_unixtime( bb.channel_finaltime, '%Y-%m-%d' )>= CURDATE() ) 
                        AND ee.fucmodule_branch = 'Taskstu' 
                        )
                        and (d.fucmodule_branch='' or d.fucmodule_branch in ($moduleStr))
                ORDER BY
                    f.times_sort desc,d.taskitems_week asc,d.taskitems_sort asc,a.learnitems_sort ASC";

        $taskList=$this->Show_css->selectClear($sql);
        if(!$taskList){
            $this->error = true;
            $this->errortip = "无任务";
            return false;
        }

        $tem_taskArray=array();
        foreach($taskList as $taskOne){

            $tem_taskArray[$taskOne['times_id']]['times_sort']=$taskOne['times_sort'];
            $tem_taskArray[$taskOne['times_id']]['times_id']=$taskOne['times_id'];

            $tem_taskArray[$taskOne['times_id']]['taskList'][]=$taskOne;
        }

        $data=array();
        foreach($tem_taskArray as $temOne){

            $medalOne=$this->Show_css->getFieldOne("eas_course_medal","medal_mainimg,medal_shadowimg","times_id='{$temOne['times_id']}'");

            if($medalOne){
                $temOne['medal_mainimg']=$medalOne['medal_mainimg'];
                $temOne['medal_shadowimg']=$medalOne['medal_shadowimg'];
            }else{
                $temOne['medal_mainimg']='';
                $temOne['medal_shadowimg']='';
            }

            foreach($temOne['taskList'] as $one){
                if($one['learnitemslog_id']==0){
                    $temOne['isComplete']=0;
                    break;
                }else{
                    $temOne['isComplete']=1;
                }
            }

            $data[]=$temOne;
        }

        $lastArray=array();

        foreach($timesList as $timesOne){
            if($timesOne['release_status']=='0'){
                $array=array();
                $array['times_sort']=$timesOne['times_sort'];
                $array['times_id']=$timesOne['times_id'];
                $array['taskList']=array();
                $array['isComplete']='-1';
                $array['medal_mainimg']='';
                $array['medal_shadowimg']='';
                $lastArray[]=$array;
            }else{
                foreach($data as $dataOne){
                    if($dataOne['times_id']==$timesOne['times_id']){
                        $lastArray[]=$dataOne;
                    }
                }
            }
        }

        return $lastArray;

    }


    function getTaskItemOne($request){
        if(!isset($request['learnitems_id']) || $request['learnitems_id']==''){
            $this->error = true;
            $this->errortip = "任务id必须传";
            return false;
        }

        $Behaviorlog = new \Model\BehaviorlogModel('App-Taskstu');
        $userecord['member_id'] = $request['member_id'];
        $userecord['school_id'] = $request['school_id'];
        $userecord['class_id'] = $request['class_id'];
        $userecord['student_id'] = $request['student_id'];
        $userecord['userecord_playport'] = $request['program_type'];
        $userecord['userecord_other'] = "V{$request['app_version']}";
        $Behaviorlog->studentUserecord($userecord);

        $Model = new \Model\Stuappapi\TaskItemModel($request['learnitems_id'],$request);
        $data = $Model->getTaskItemJsonArray($request['student_id']);
        if($data){
            return $data;
        }else{
            $this->error = true;
            $this->errortip = $Model->errortip;
            return false;
        }

    }

    //提交任务
    function SubmitTask($request)
    {

        $studentOne = $this->Show_css->getFieldOne("app_student","student_branch,is_devstudent,student_diamondprice","student_id='{$request['student_id']}'");
//
//        if (strpos($request['member_id'], 'T') !== false || $studentOne['is_devstudent']==1) {
////            $this->error = true;
////            $this->errortip = "教师账号/测试学生不会提交任务记录";
////            return false;
//            $log = array();
//            $log['playprice'] = 0;
//            $log['playclass'] = 1;
//            $log['totalPrice'] = $studentOne['student_diamondprice'];
//            return $log;
//        }

        if (strpos($request['member_id'], 'T') !== false) {
//            $this->error = true;
//            $this->errortip = "教师账号/测试学生不会提交任务记录";
//            return false;
            $log = array();
            $log['playprice'] = 0;
            $log['playclass'] = 1;
            $log['totalPrice'] = $studentOne['student_diamondprice'];
            return $log;

        }


            //学生更新修改
        $sql = "select b.*,b.taskitems_modetype,a.learnitems_content,a.learnitems_diamond,a.learnitems_goldcoin,c.class_id,c.times_id,c.class_id,d.list_name
                from eas_classes_tasks_learnitems as a
                inner join app_taskitems as b on a.taskitems_id=b.taskitems_id
                inner join eas_classes_tasks as c on a.classtasks_id=c.classtasks_id
                left join cms_variablelist as d on d.list_parameter=b.taskitems_tasktype and d.variable_id=9
                where a.learnitems_id='{$request['learnitems_id']}'";
        $stutaskOne=$this->Show_css->selectOne($sql);
        if(!$stutaskOne){
            $this->error = true;
            $this->errortip = "未查询到班级任务信息";
            return false;
        }

        $moduleStr=$this->getStuModuleStr($stutaskOne['class_id'],$request['student_id']);

        if($this->Show_css->getFieldOne("app_student_learnitemslog","learnitemslog_id","learnitems_id='{$request['learnitems_id']}' and student_id='{$request['student_id']}'")){

//            $studentData = $this->Show_css->selectOne("select s.student_diamondprice from app_student as s where s.student_id='{$request['student_id']}'");
            $totalPrice = $studentOne['student_diamondprice'];

            $log = array();
            $log['playprice'] = 0;
            $log['playclass'] = 1;
            $log['totalPrice'] = $totalPrice;
            return $log;
        }

        $data=array();
        $data['learnitems_id']=$request['learnitems_id'];
        $data['student_id']=$request['student_id'];
        $data['class_id']=$stutaskOne['class_id'];
        $data['times_id']=$stutaskOne['times_id'];
        $data['learnitemslog_duringtime']=$request['duringtime'];
        $data['learnitemslog_score']=$request['score'];
        $data['learnitemslog_diamondprice']=$stutaskOne['learnitems_diamond'];
        $data['learnitemslog_videourl']=$request['videourl']?$request['videourl']:'';
        $data['learnitemslog_createtime']=time();
        if($this->Show_css->insertData("app_student_learnitemslog",$data)){
            $this->oktip = "任务提交成功";
            $Model = new \Model\Stuappapi\StudentModel($request['student_id']);
            $Model->AddDiamond($stutaskOne['list_name'], $stutaskOne['learnitems_diamond'], "学员完成任务:{$stutaskOne['taskitems_title']}，获得+{$stutaskOne['learnitems_diamond']}钻石。",$stutaskOne['class_id']);

            if($stutaskOne['taskitems_tasktype']==0){
                $sql="select b.times_id,a.media_id from app_taskitems_media as a,eas_course_media as b where a.media_id=b.media_id and a.taskitems_id='{$stutaskOne['taskitems_id']}'";
                $mediaOne=$this->Show_css->selectOne($sql);

                if($mediaOne){
                    $kctvdata = array();
                    $kctvdata['student_id'] = $request['student_id'];
                    $kctvdata['kctvlog_type'] = '1';
                    $kctvdata['class_id'] = $stutaskOne['class_id'];
                    $kctvdata['times_id'] = $mediaOne['times_id'];
                    $kctvdata['media_id'] = $mediaOne['media_id'];
                    $kctvdata['kctvlog_createtime'] = time();
                    $this->Show_css->insertData("app_student_kctvlog", $kctvdata);
                }
            }

            $studentData = $this->Show_css->selectOne("select s.student_diamondprice from app_student as s where s.student_id='{$request['student_id']}'");
            $totalPrice = $studentData['student_diamondprice'];

            //查看有任务是否未完成
//            $sql = "SELECT
//                        a.learnitems_id
//                    FROM
//                        eas_classes_tasks_learnitems AS a
//                        INNER JOIN eas_classes_tasks AS b ON a.classtasks_id = b.classtasks_id
//                        INNER JOIN eas_classes_studytimes AS c ON c.times_id = b.times_id
//                        AND c.class_id = b.class_id
//                        AND c.studytimes_status > 0
//                        INNER JOIN app_taskitems AS d ON d.taskitems_id = a.taskitems_id
//                        LEFT JOIN eas_classes_study_notextbook AS e ON e.class_id = c.class_id
//                        AND e.student_id = c.student_id
//                        AND e.textbook_id = d.textbook_id
//                    WHERE b.class_id='{$stutaskOne['class_id']}' AND c.student_id='{$request['student_id']}' AND b.times_id='{$stutaskOne['times_id']}'
//                        AND ( d.textbook_id = 0 OR e.notextbook_id IS NULL )
//                        AND b.classtasks_tasksviewtimes <= unix_timestamp(
//                        now())
//                        AND not exists(select 1 from app_student_learnitemslog as x where x.learnitems_id=a.learnitems_id and x.student_id=c.student_id)
//                    limit 0,1";


            $sql = "SELECT a.learnitems_id
                    FROM
                        eas_classes_tasks_learnitems AS a
                        INNER JOIN eas_classes_tasks AS b ON a.classtasks_id = b.classtasks_id
                        INNER JOIN eas_classes_studytimes AS c ON c.times_id = b.times_id 
                        AND c.class_id = b.class_id 
                        AND c.studytimes_status > 0
                        INNER JOIN app_taskitems AS d ON d.taskitems_id = a.taskitems_id
                        INNER JOIN eas_classes AS f ON f.class_id = b.class_id
                        LEFT JOIN eas_classes_study_notextbook AS e ON e.class_id = c.class_id 
                        AND e.student_id = c.student_id 
                        AND e.textbook_id = d.textbook_id
                        LEFT JOIN app_student_learnitemslog AS g ON g.learnitems_id = a.learnitems_id 
                        AND g.student_id = c.student_id
                    WHERE
                        ( d.textbook_id = 0 OR e.notextbook_id IS NULL ) 
                        and b.class_id='{$stutaskOne['class_id']}' AND c.student_id='{$request['student_id']}' AND b.times_id='{$stutaskOne['times_id']}' and g.learnitemslog_id is null
                        AND EXISTS (
                        SELECT
                            dd.fucmodule_id 
                        FROM
                            pro_carditem AS aa
                            INNER JOIN pro_sales_channel AS bb ON aa.channel_id = bb.channel_id
                            INNER JOIN pro_sales_fucmodule AS dd ON dd.plan_id = bb.plan_id
                            INNER JOIN pro_product_fucmodule AS ee ON ee.fucmodule_id = dd.fucmodule_id
                            INNER JOIN app_student AS ff ON ff.student_branch = aa.student_branch 
                        WHERE
                            aa.course_branch = f.course_branch 
                            AND ff.student_id = c.student_id 
                            AND ee.fucmodule_branch = 'Taskstu' 
                        ) 
                    and (d.fucmodule_branch='' or d.fucmodule_branch in ($moduleStr))
                    ";

            if(!$this->Show_css->selectOne($sql)){

               $Model = new \Model\Stuappapi\MedalModel();
               $data=array();
               $data['student_id']=$request['student_id'];
               $data['class_id']=$stutaskOne['class_id'];
               $data['times_id']=$stutaskOne['times_id'];
               $Model->addStudentMedallog($data);
            }

            $log = array();
            $log['playprice'] = $stutaskOne['learnitems_diamond'];
            $log['playclass'] = 1;
            $log['totalPrice'] = $totalPrice;
            return $log;
        }else{
            $this->error = true;
            $this->errortip = "任务完成失败";
            return false;
        }
    }

    function SubmitFtvTask($request){

        if (strpos($request['member_id'], 'T') !== false) {
            $this->error = true;
            $this->errortip = "教师账号不会提交记录";
            return false;
        }

        if (!isset($request['item_id']) || $request['item_id'] == '') {
            $this->error = true;
            $this->errortip = "节庆任务详情id必须传";
            return false;
        }

        $sql = "select a.item_title,a.item_diamondprice,a.item_goldcorrect,b.medal_id,c.medal_mainimg 
                from app_ftvtask_item as a
                left join app_ftvtask as b on a.ftvtask_id=b.ftvtask_id
                left join eas_course_medal as c on c.medal_id=b.medal_id
                where a.item_id='{$request['item_id']}'";

        $itemOne=$this->Show_css->selectOne($sql);

        if(!$itemOne){
            $this->error = true;
            $this->errortip = "节庆任务详情不存在";
            return false;
        }

        $logOne=$this->Show_css->getFieldOne("app_student_ftvitemlog","ftvitemlog_id","item_id='{$request['item_id']}' and student_id='{$request['student_id']}'");

//        if($this->Show_css->getFieldOne("app_student_ftvitemlog","ftvitemlog_id","student_id='{$request['student_id']}' and item_id='{$request['item_id']}'")){
//            $this->error = true;
//            $this->errortip = "节庆任务已完成,不可重复完成";
//            return false;
//        }

        $data=array();
        $data['student_id']=$request['student_id'];
        $data['item_id']=$request['item_id'];
        if($logOne){
            $data['log_diamondprice']=0;
        }else{
            $data['log_diamondprice']=$itemOne['item_diamondprice'];
        }

        $data['log_goldprice']=$itemOne['item_goldcorrect'];
        $data['ftvitemlog_createtime']=time();

        if($this->Show_css->insertData("app_student_ftvitemlog",$data)){
            $Model = new \Model\Stuappapi\StudentModel($request['student_id']);

            $addgold=0;
            $adddiamond=0;

            if(!$logOne && $itemOne['item_diamondprice']>0){
                $Model->AddDiamond('节庆任务', $itemOne['item_diamondprice'], "学员完成任务:{$itemOne['item_title']}，获得+{$itemOne['item_diamondprice']}钻石。",$request['class_id']);

                $adddiamond=$itemOne['item_diamondprice'];
            }

            if($itemOne['item_goldcorrect']>0){
                $Model->AddGold("节庆任务",$itemOne['item_goldcorrect'],"学员完成一次节庆任务操作，获得+{$itemOne['item_goldcorrect']}金币。",$request['class_id']);
                $addgold=$itemOne['item_goldcorrect'];
            }

            if(!$logOne && $itemOne['medal_id']>0){
                $Model = new \Model\Stuappapi\MedalModel();
                $data=array();
                $data['student_id']=$request['student_id'];
                $data['class_id']=$request['class_id'];
                $data['medal_id']=$itemOne['medal_id'];
                $Model->addStudentMedallog($data,1);
            }

            $studentOne=$this->Show_css->getFieldOne("app_student","student_goldprice,student_diamondprice","student_id='{$request['student_id']}'");

            $log = array();
            $log['goldprice'] = $addgold;
            $log['totalgoldprice'] = $studentOne['student_goldprice'];
            $log['diamondprice'] = $adddiamond;
            $log['totaldiamondprice'] = $studentOne['student_diamondprice'];
            $log['medal_mainimg'] = $itemOne['medal_mainimg'];

            return $log;

        }else{
            $this->error = true;
            $this->errortip = "任务完成失败";
            return false;
        }

    }

    function SubmitPreviewVideo($request){

        if($this->Show_css->getFieldOne("app_stuclass_previewlog","previewlog_id","student_id='{$request['student_id']}' and previewlog_createtime>(UNIX_TIMESTAMP()-5)")){
            $this->error = true;
            $this->errortip = "请勿快速重复完成";
            return false;
        }

        $data=array();
        $data['student_id']=$request['student_id'];
        $data['class_id']=$request['class_id'];
        $data['course_id']=$request['course_id'];
        $data['previewlog_createtime']=time();
        if($this->Show_css->insertData("app_stuclass_previewlog",$data)){
            $this->error = true;
            $this->errortip = "记录失败";
            return false;
        }else{
            $this->error = true;
            $this->errortip = "记录成功";
            return false;
        }

    }




    function checkStuTaskStatus($request){

        if (!isset($request['learnitems_id']) || $request['learnitems_id'] == '') {
            $this->error = true;
            $this->errortip = "班级任务id必须传";
            return false;
        }

        $sql = "select a.learnitemslog_id 
                from app_student_learnitemslog as a 
                where a.learnitems_id='{$request['learnitems_id']}' and a.student_id='{$request['student_id']}'";

        $data=array();
        if($this->Show_css->selectOne($sql)){
            $data['status']=1;
        }else{
            $data['status']=0;
        }

        return $data;
    }

    //判断week是否完成以及 对应的勋章
    function checkStuWeekTaskStatus($request){

        if (!isset($request['times_id']) || $request['times_id'] == '') {
            $this->error = true;
            $this->errortip = "班级周次id必须传";
            return false;
        }

        $moduleStr=$this->getStuModuleStr($this->classesOne['class_id'],$request['student_id']);

        $medalOne = $this->Show_css->selectOne("select m.medal_title,m.medal_mainimg,m.medal_shadowimg from eas_course_medal as m 
            where m.course_branch = '{$this->classesOne['course_branch']}' and m.times_id = '{$request['times_id']}' ");

        $data = array();

        $data['medal_mainimg'] = $medalOne['medal_mainimg']?$medalOne['medal_mainimg']:'';

        $studentOne = $this->Show_css->getFieldOne("app_student","student_branch,is_devstudent","student_id='{$request['student_id']}'");

        $sql = "select d.fucmodule_id
                from pro_carditem as a
                inner join pro_sales_channel as b on a.channel_id = b.channel_id
                inner join pro_sales_fucmodule as d on d.plan_id = b.plan_id
                inner join pro_product_fucmodule as e on e.fucmodule_id=d.fucmodule_id
                where a.course_branch='{$this->classesOne['course_branch']}' and a.student_branch='{$studentOne['student_branch']}'
                and from_unixtime(a.carditem_invalidtime, '%Y-%m-%d')>=curdate() and (b.channel_finaltime=0 or from_unixtime(b.channel_finaltime, '%Y-%m-%d')>=curdate()) and e.fucmodule_branch='Taskstu'
                group by d.fucmodule_id";

        //判断权限
        if($this->Show_css->selectOne($sql)){
            $sql="SELECT
                 count( a.learnitems_id ) AS taskitemsnum
                ,sum(IF( h.learnitemslog_id IS NULL, 0, 1 )) AS completenum
                 ,ifnull(sum(IF((d.taskitems_week = '' 
                    OR (
                        from_unixtime( b.classtasks_tasksviewtimes, '%Y-%m-%d' )<(
                        DATE_SUB( CURDATE(), INTERVAL WEEKDAY( CURDATE()) + 0 DAY )) 
                        OR d.taskitems_week <=(
                            WEEKDAY(
                            CURDATE())+ 1 
                        ))),1,0)),0) as nowtaskitemsnum    
                FROM eas_classes_tasks_learnitems AS a
                INNER JOIN eas_classes_tasks AS b ON a.classtasks_id = b.classtasks_id
                INNER JOIN eas_classes_studytimes AS c ON c.times_id = b.times_id 
                AND c.class_id = b.class_id 
                AND c.studytimes_status > 0
                INNER JOIN app_taskitems AS d ON d.taskitems_id = a.taskitems_id
                INNER JOIN eas_classes AS f ON f.class_id = b.class_id
                LEFT JOIN eas_classes_study_notextbook AS e ON e.class_id = c.class_id 
                AND e.student_id = c.student_id 
                AND e.textbook_id = d.textbook_id
                left join eas_course_times as g on g.times_id=b.times_id    
                left join app_student_learnitemslog as h on h.learnitems_id=a.learnitems_id and h.student_id=c.student_id
                WHERE b.class_id = '{$this->classesOne['class_id']}' AND c.student_id='{$request['student_id']}' AND b.times_id='{$request['times_id']}'
                    AND ( d.textbook_id = 0 OR e.notextbook_id IS NULL ) 
                    AND b.classtasks_tasksviewtimes <= unix_timestamp(now())  
                    AND EXISTS (
                                SELECT
                                    dd.fucmodule_id 
                                FROM
                                    pro_carditem AS aa
                                    INNER JOIN pro_sales_channel AS bb ON aa.channel_id = bb.channel_id
                                    INNER JOIN pro_sales_fucmodule AS dd ON dd.plan_id = bb.plan_id
                                    INNER JOIN pro_product_fucmodule AS ee ON ee.fucmodule_id = dd.fucmodule_id
                                    INNER JOIN app_student AS ff ON ff.student_branch = aa.student_branch 
                                WHERE
                                    aa.course_branch = f.course_branch 
                                    AND ff.student_id = c.student_id 
                                    AND ee.fucmodule_branch = 'Taskstu' 
                                ) 
                    and (d.fucmodule_branch='' or d.fucmodule_branch in ($moduleStr))
                    
                    ";

            $taskOne = $this->Show_css->selectOne($sql);
            $data['isallsuccess'] = $taskOne['taskitemsnum']<=0?0:(($taskOne['completenum']>=$taskOne['taskitemsnum'])?1:0);
            $data['issnowuccess'] = ($taskOne['completenum']>=$taskOne['nowtaskitemsnum'])?1:0;

            //qyh加
            if($data['isallsuccess'] == '1'){
                $isset = $this->Show_css->getFieldOne("app_student_weektask","weektask_id","student_id = '{$request['student_id']}' and times_id = '{$request['times_id']}'");
                if(!$isset){
                    $sql = "SELECT g.times_name,h.learnitemslog_id,g.times_id
                            FROM
                                eas_classes_tasks_learnitems AS a
                                INNER JOIN eas_classes_tasks AS b ON a.classtasks_id = b.classtasks_id
                                INNER JOIN eas_classes_studytimes AS c ON c.times_id = b.times_id 
                                AND c.class_id = b.class_id 
                                AND c.studytimes_status > 0
                                INNER JOIN app_taskitems AS d ON d.taskitems_id = a.taskitems_id
                                INNER JOIN eas_classes AS f ON f.class_id = b.class_id
                                LEFT JOIN eas_classes_study_notextbook AS e ON e.class_id = c.class_id 
                                AND e.student_id = c.student_id 
                                AND e.textbook_id = d.textbook_id
                                left join eas_course_times as g on g.times_id=b.times_id    
                                left join app_student_learnitemslog as h on h.learnitems_id=a.learnitems_id and h.student_id=c.student_id
                            WHERE c.student_id='{$request['student_id']}' and c.class_id='{$this->classesOne['class_id']}'
                                AND ( d.textbook_id = 0 OR e.notextbook_id IS NULL ) 
                                AND b.classtasks_tasksviewtimes <= unix_timestamp(
                                now()) 
                                AND (
                                    d.taskitems_week = '' 
                                    OR (
                                        from_unixtime( b.classtasks_tasksviewtimes, '%Y-%m-%d' )<(
                                        DATE_SUB( CURDATE(), INTERVAL WEEKDAY( CURDATE()) + 0 DAY )) 
                                        OR d.taskitems_week <=(
                                            WEEKDAY(
                                            CURDATE())+ 1 
                                        ))) 
                                AND EXISTS (
                                SELECT
                                    dd.fucmodule_id 
                                FROM
                                    pro_carditem AS aa
                                    INNER JOIN pro_sales_channel AS bb ON aa.channel_id = bb.channel_id
                                    INNER JOIN pro_sales_fucmodule AS dd ON dd.plan_id = bb.plan_id
                                    INNER JOIN pro_product_fucmodule AS ee ON ee.fucmodule_id = dd.fucmodule_id
                                    INNER JOIN app_student AS ff ON ff.student_branch = aa.student_branch 
                                WHERE
                                    aa.course_branch = f.course_branch 
                                    AND ff.student_id = c.student_id 
                                    AND ee.fucmodule_branch = 'Taskstu' 
                                )
                                and (d.fucmodule_branch='' or d.fucmodule_branch in ($moduleStr))
                                
                                ";

                    $timesOne=$this->Show_css->selectOne($sql.' order by g.times_sort desc limit 0,1');

                    $val['times_id']=$timesOne?$timesOne['times_id']:'0';

                    if($val['times_id'] == $request['times_id']){
                        $datatask = array();
                        $datatask['student_id'] = $request['student_id'];
                        $datatask['times_id'] = $request['times_id'];
                        $this->Show_css->insertData("app_student_weektask",$datatask);
                        $stu = $this->Show_css->getFieldOne("app_student","student_freenum","student_id = '{$request['student_id']}'");
                        $datastu = array();
                        $datastu['student_freenum'] = $stu['student_freenum'] + 1;
                        $this->Show_css->updateData("app_student","student_id = '{$request['student_id']}'",$datastu);
                    }
                }


            }


        }else{
            $data['isallsuccess']=0;//判断全部任务是否已经完成
            $data['issnowuccess']=1;//判断当前是否已经完成
        }

        return $data;
    }

    function getStuftvtaskList($request){

        $courseOne=$this->Show_css->getFieldOne("eas_course","course_id","course_branch='{$this->classesOne['course_branch']}'");

//        $studentOne=$this->Show_css->getFieldOne("app_student","student_id","student_id='{$request['student_id']}' and is_devstudent=1");
//
//        if(!$studentOne){
//            $this->error = true;
//            $this->errortip = "暂时只开发测试";
//            return false;
//        }


        $sql = "select a.ftvtask_id,b.ftvtask_branch,b.ftvtask_startday,b.ftvtask_mainjson,b.ftvtask_thumbnailurl,a.item_resourceurl,a.item_id,a.item_title,a.item_content,b.ftvtask_startday,b.ftvtask_endday,a.steps_id
                ,ifnull((select 1 from app_student_ftvitemlog as x where x.student_id='{$request['student_id']}' and x.item_id=a.item_id limit 0,1),0) as isComplete
                ,ifnull((select x.log_id from app_maps_steps_log as x where x.steps_id=a.steps_id and x.class_id='{$request['class_id']}' and x.student_id='{$request['student_id']}' order by x.log_id desc limit 0,1),0) as log_id
                from app_ftvtask_item as a,app_ftvtask as b 
                where a.ftvtask_id=b.ftvtask_id and b.ftvtask_endday>=DATE_FORMAT(CURDATE(),'%Y%m%d') and b.ftvtask_startday<=DATE_FORMAT(CURDATE(),'%Y%m%d') and b.ftvtask_isopen=1
                and exists(select 1 from app_ftvtask_apply as x where x.ftvtask_id=a.ftvtask_id and x.ftvitem_id=a.item_id and x.course_id='{$courseOne['course_id']}')
                order by b.ftvtask_startday asc,b.ftvtask_id asc,a.item_sort asc
                ";

        $itemList=$this->Show_css->selectClear($sql);

        if(!$itemList){
            $this->error = true;
            $this->errortip = "无可用节庆任务";
            return false;
        }

        $taskArray=array();

        foreach($itemList as $itemOne){

            if($itemOne['steps_id']>0){
                $stepsOne=$this->Show_css->getFieldOne("app_maps_steps","games_id","steps_id='{$itemOne['steps_id']}'");

                $GamesModel = new \Model\Stuappapi\GamesModel();
                $itemOne['item_resourceurl'] = $GamesModel->gameStepUrl($stepsOne['games_id'])."?member_id=".$request['member_id'].'&token='.$request['token'].'&student_id='.$request['student_id'].'&steps_id='.$itemOne['steps_id'].'&class_id='.$request['class_id'].'&taskitem_id=J'.$itemOne['item_id'].'&from=map&fromport=napp';

                if(SITE_URL=='kidcastle.cn' || SITE_URL=='kidcastleapp.tw'){
                    $itemOne['item_resourceurl'].='&fromserve=pro';
                }else{
                    $itemOne['item_resourceurl'].='&fromserve=dev';
                }
            }


            $taskArray[$itemOne['ftvtask_id']]['ftvtask_thumbnailurl']=$itemOne['ftvtask_thumbnailurl'];

            if(strtotime($itemOne['ftvtask_endday']." 23:59:59")>=time()){
                $dif=ceil((int)(strtotime($itemOne['ftvtask_endday']." 23:59:59")-time())/(3600*24));
            }else{
                $dif=0;
            }



            $itemOne['ftvtask_startday']=date("Y-m-d",strtotime($itemOne['ftvtask_startday']));
            $itemOne['ftvtask_endday']=date("Y-m-d",strtotime($itemOne['ftvtask_endday']));

            $taskArray[$itemOne['ftvtask_id']]['dif']=$dif;

            $s_year=date("Y",strtotime($itemOne['ftvtask_startday']));
            $s_month=date("m",strtotime($itemOne['ftvtask_startday']));
            $s_day=date("d",strtotime($itemOne['ftvtask_startday']));

            $e_year=date("Y",strtotime($itemOne['ftvtask_endday']));
            $e_month=date("m",strtotime($itemOne['ftvtask_endday']));
            $e_day=date("d",strtotime($itemOne['ftvtask_endday']));


            $taskArray[$itemOne['ftvtask_id']]['dayRange']=$s_year.'年'.$s_month.'月'.$s_day.'日至'.$e_year.'年'.$e_month.'月'.$e_day.'日';

            $taskArray[$itemOne['ftvtask_id']]['taskList'][]=$itemOne;

        }

        $tem_taskArray = $this->arraySort($taskArray, 'dif', 'asc');

        return $tem_taskArray;

    }



}