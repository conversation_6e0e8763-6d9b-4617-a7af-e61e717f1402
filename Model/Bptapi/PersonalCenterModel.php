<?php
/**
 * ============================================================================
 * 版权所有 : https://www.mohism.cn
 * 网站地址 : https://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2017/4/14
 * Time: 0:58
 */

namespace Model\Bptapi;

use Model\modelTpl;
use Model\Teasx\ClassModel;
use Model\Teasx\LanguageModel;

class PersonalCenterModel extends modelTpl
{
    public $classesOne = false;//当前班级信息
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = ['list' => []];
    public $allnum = 0;

    function __construct($request)
    {
        parent::__construct();
        if (isset($request['class_id']) && $request['class_id'] != '' && $request['class_id'] != 0) {
            $this->verdictclass($request['class_id']);
        }
    }

    //验证班级状态
    function verdictclass($class_id)
    {
        $sql = "select a.class_id,b.course_mold,b.course_branch from eas_classes as a,eas_course as b where a.course_branch=b.course_branch and a.class_id = '{$class_id}' limit 0,1";

        $this->classesOne = $this->Show_css->selectOne($sql);
        if (!$this->classesOne) {
            $this->error = 1;
            $this->errortip = "班级信息不存在";
            return false;
        }
    }

    /*
     * 相册列表 （仿照瞿屹晖给的假数据返回的数据）
     */
    function PicturesList($request)
    {
        $studyOne = $this->Show_css->selectOne("select GROUP_CONCAT(DISTINCT(t.school_id)) as schoolids,GROUP_CONCAT(
	DISTINCT ( t.class_id )) AS class_ids 
                FROM app_student_study as t 
                LEFT JOIN eas_classes as c ON t.class_id = c.class_id  
                WHERE  
                t.student_id = '{$request['student_id']}'  
                and t.study_islearning = '1'
                and ifnull(c.class_enddate,'')<>''
                and ifnull(t.study_endday,'')<>''
                and (case when t.study_beginday < c.class_stdate then c.class_stdate else t.study_beginday end)<=DATE_FORMAT(now(),'%Y%m%d')
                and (case when t.study_endday > c.class_enddate then c.class_enddate else t.study_endday end)>=DATE_FORMAT(now(),'%Y%m%d') 
                limit 0,1");
        if (!$studyOne['schoolids']) {
            $this->error = 1;
            $this->errortip = "学生不在班！";
            return false;
        }

        $Behaviorlog = new \Model\BehaviorlogModel('Bpt-Album');
        $userecord['member_id'] = $request['member_id'];
        $userecord['school_id'] = $request['school_id'];
        $userecord['student_id'] = $request['student_id'];
        $userecord['userecord_playport'] = '微信小程序';
        $Behaviorlog->memberUserecord($userecord);

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10000';
        }
        $pagestart = ($page - 1) * $num;


        $datalist = $this->Show_css->selectClear(" SELECT b.album_id,b.album_name,from_unixtime(b.album_createtime,'%m-%d %H:%i') as album_createtime,h.school_id  
                FROM eas_classes_album_apply as a 
                LEFT JOIN eas_classes_album as b ON a.album_id = b.album_id 
                LEFT JOIN eas_classes AS c ON b.class_id = c.class_id
                LEFT JOIN app_school AS h ON c.school_branch = h.school_branch
                WHERE a.student_id = '{$request['student_id']}' 
                and h.school_id in ({$studyOne['schoolids']}) 
	            AND b.class_id IN ( {$studyOne['class_ids']} )  and b.album_status = '1'
                order by b.album_createtime desc LIMIT {$pagestart},{$num}");
        if ($datalist) {
            foreach ($datalist as &$datavar) {
                $photolist = $this->Show_css->selectClear("select photos_id,photos_imgurl,photos_videourl,photos_starlevel from eas_classes_album_photos where album_id = '{$datavar['album_id']}' ");
                if ($photolist) {
                    foreach ($photolist as &$val) {
                        if ($val['photos_imgurl']) {
                            $val['photos_imgdetial'] = $val['photos_imgurl'];
                            $val['photos_imgurl'] = $val['photos_imgurl'] . '?x-oss-process=style/wmx';
                            $iscollect = $this->Show_css->getFieldOne("eas_classes_album_collect", "collect_id", "photos_id = '{$val['photos_id']}' and student_id = '{$request['student_id']}'");
                            if ($iscollect) {
                                $val['iscollect'] = '1';
                            } else {
                                $val['iscollect'] = '0';
                            }
                        }
                    }

                }
                $datavar['photo'] = $photolist;

            }
            $allsql = $this->Show_css->selectClear(" SELECT b.album_id,b.album_name,from_unixtime(b.album_createtime,'%m-%d %H:%i') as album_createtime,h.school_id  
                FROM eas_classes_album_apply as a 
                LEFT JOIN eas_classes_album as b ON a.album_id = b.album_id 
                LEFT JOIN eas_classes AS c ON b.class_id = c.class_id
                LEFT JOIN app_school AS h ON c.school_branch = h.school_branch
                WHERE a.student_id = '{$request['student_id']}' and h.school_id in ('{$studyOne['schoolids']}') and b.album_status = '1'
                order by b.album_createtime desc");


            $this->error = 0;
            $this->errortip = "相册列表数据获取成功！";
            $this->result = $datalist;
            $this->allnum = count($allsql);
            return true;
        } else {
            $this->error = 1;
            $this->errortip = "暂时还没有上传照片！";
            $this->result = array();
            return false;
        }
    }

    function getStudentClassList($request)
    {

        if (!isset($request['student_id']) || $request['student_id'] == '') {
            $this->error = true;
            $this->errortip = "学生id必须传";
            return false;
        }
        $datawhere = ' 1 ';
        if (isset($request['school_id']) && $request['school_id'] != '') {
            $datawhere .= " and c.school_id = '{$request['school_id']}' ";
        }

        //更新学生班级信息
        $sql = "SELECT
                    a.class_id,
                    if(d.course_mold=1,concat(b.class_cnname,'(',d.course_cnname,')'),b.class_cnname) as class_cnname,
                    b.class_enname,
                    b.class_branch,
                    c.school_id,
                    c.school_cnname,
                    a.study_id,
                    a.study_endday,
                    b.course_branch,
                    d.course_mold,
                    d.coursecat_branch,
                    date_sub(CURDATE(),interval 14 day)  as d
                FROM
                    app_student_study AS a,
                    eas_classes AS b,
                    app_school AS c,
                    eas_course AS d
                WHERE 
                    {$datawhere}
                    AND a.class_id = b.class_id 
                    AND a.school_id = c.school_id 
                    AND d.course_branch = b.course_branch 
                    AND a.student_id = '{$request['student_id']}' 
                    AND IF(d.course_mold = 0, d.course_showeffect = '1', 1)
                HAVING 
                    a.study_endday > d
                ORDER BY
                    a.study_endday DESC";

        $classList = $this->Show_css->selectClear($sql);

        if ($classList) {
            foreach ($classList as &$val) {
                if ($val['course_mold'] == '1') {
                    $val['course_mold'] = '1';
                } else {
                    $val['course_mold'] = '0';
                }
                if ($val['course_mold'] == '0' && $val['coursecat_branch'] == 'K') {
                    $val['course_isk'] = '1';
                } else {
                    $val['course_isk'] = '0';
                }
            }
        }

        if (!$classList) {
            $this->error = true;
            $this->errortip = "无班级列表";
            return false;
        }

        return $classList;
    }

    function getStudentActivityClassList($request)
    {

        if (!isset($request['student_id']) || $request['student_id'] == '') {
            $this->error = true;
            $this->errortip = "学生id必须传";
            return false;
        }
        $datawhere = ' 1 ';
        if (isset($request['school_id']) && $request['school_id'] != '') {
            $datawhere .= " and c.school_id = '{$request['school_id']}' ";
        }

        if (isset($request['coursecat_branch']) && $request['coursecat_branch'] != '') {
            $datawhere .= " and b.coursecat_branch = '{$request['coursecat_branch']}' ";
        }

        if (isset($request['school_branch']) && $request['school_branch'] != '') {
            $datawhere .= " and c.school_branch = '{$request['school_branch']}' ";
        }

        if (isset($request['isonlyread']) && $request['isonlyread'] != '' && $request['isonlyread'] != '0') {
            $datawhere .= " and a.study_islearning = '{$request['isonlyread']}' ";
        }

        //更新学生班级信息
        $sql = "
            SELECT
                a.class_id,
                if(d.course_mold=1,concat(b.class_cnname,'(',d.course_cnname,')'),b.class_cnname) as class_cnname,
                b.class_enname,
                b.class_branch,
                c.school_id,
                c.school_cnname,
                c.school_branch,
                a.study_id,
                b.course_branch,
                d.course_mold,
                d.coursecat_branch,
                a.study_islearning
            FROM
                app_student_study AS a,
                eas_classes AS b,
                app_school AS c,
                eas_course AS d
            WHERE {$datawhere}
                AND a.class_id = b.class_id 
                AND a.school_id = c.school_id 
                AND d.course_branch = b.course_branch 
                AND a.student_id = '{$request['student_id']}' 
            ORDER BY
                a.study_islearning DESC,b.class_createtime DESC";

        $classList = $this->Show_css->selectClear($sql);

        if ($classList) {
            foreach ($classList as &$val) {
                if ($val['course_mold'] == '1') {
                    $val['course_mold'] = '1';
                } else {
                    $val['course_mold'] = '0';
                }
                if ($val['course_mold'] == '0' && $val['coursecat_branch'] == 'K') {
                    $val['course_isk'] = '1';
                } else {
                    $val['course_isk'] = '0';
                }
                $taskOne = $this->Show_css->getFieldOne("eas_classes_tasks", "times_id", "class_id = '{$val['class_id']}' and school_id = '{$request['school_id']}' order by times_id DESC");
                $tsort = $this->Show_css->getFieldOne("eas_course_times", "times_sort", "times_id = '{$taskOne['times_id']}'");
                $val['times_sort'] = $tsort['times_sort'];
                if (!$tsort['times_sort']) {
                    $val['times_sort'] = '--';
                }
            }
        }

        if (!$classList) {
            $this->error = true;
            $this->errortip = "无班级列表";
            return false;
        }

        return $classList;
    }

    /*
     * 加入收藏
     */
    function addCollectAlbumPhotos($request)
    {
        $photosArray = json_decode(stripslashes($request['photos_json']), 1);
        if (empty($photosArray)) {
            $this->error = 1;
            $this->errortip = "未选择数据！";
            return false;
        }

        foreach ($photosArray as $photosVar) {
            if (!$this->Show_css->selectOne("select collect_id from eas_classes_album_collect where album_id='{$photosVar['album_id']}' and photos_id='{$photosVar['photos_id']}' and student_id='{$request['student_id']}' ")) {
                $data = array();
                $data['album_id'] = $photosVar['album_id'];
                $data['photos_id'] = $photosVar['photos_id'];
                $data['student_id'] = $request['student_id'];
                $data['collect_createtime'] = time();
                $this->Show_css->insertData("eas_classes_album_collect", $data);
            }
        }

        $this->error = 0;
        $this->errortip = "收藏成功！";
        return true;
    }

    /*
     * 取消收藏
     */
    function delCollectAlbumPhotos($request)
    {
        $collectArray = json_decode(stripslashes($request['collect_json']), 1);
        if (empty($collectArray)) {
            $this->error = 1;
            $this->errortip = "未选择数据！";
            return false;
        }

        foreach ($collectArray as $collectVar) {
            $collectOne = $this->Show_css->selectOne("select collect_id from eas_classes_album_collect where collect_id='{$collectVar['collect_id']}' limit 0,1");
            if ($collectOne) {
                $this->Show_css->delData("eas_classes_album_collect", " collect_id='{$collectOne['collect_id']}'  ");
            }
        }

        $this->error = 0;
        $this->errortip = "取消成功！";
        return true;

    }

    /*
     * 相册收藏列表
     */
    function PicturesCollect($request)
    {

        $datawhere = " c.student_id = '{$request['student_id']}'  ";

        if (isset($request['photostype']) && $request['photostype'] == '1') {
            $datawhere .= " AND photos_imgurl <> '' and photos_imgurl is not null ";
        } elseif (isset($request['photostype']) && $request['photostype'] == '2') {
            $datawhere .= " AND  photos_videourl <> '' and photos_videourl is not null ";
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $list = $this->Show_css->selectClear("select c.collect_id,c.photos_id,c.album_id,p.photos_imgurl,p.photos_videourl,p.photos_starlevel 
                from eas_classes_album_collect as c 
                left join eas_classes_album_photos as p ON c.photos_id = p.photos_id 
                where {$datawhere}
                order by c.collect_createtime desc  
                limit {$pagestart},{$num}");

        if ($list) {
            foreach ($list as &$val) {
                if ($val['photos_imgurl']) {
                    $val['photos_imgdetial'] = $val['photos_imgurl'];
                    $val['photos_imgurl'] = $val['photos_imgurl'] . '?x-oss-process=style/wmx';
                }
            }
        }

        $sql = "SELECT  COUNT(c.collect_id)  as datanum from eas_classes_album_collect as c 
            left join eas_classes_album_photos as p ON c.photos_id = p.photos_id where {$datawhere} ";
        $count = $this->Show_css->selectOne($sql);
        $count = $count['datanum'] + 0;

        $result = array();
        $result['count'] = $count;
        if ($list) {
            $result['list'] = $list;
        } else {
            $result['list'] = array();
        }

        $this->error = 0;
        $this->errortip = "获取收藏信息成功！";
        $this->result = $result;
        return true;
    }

    //根据年月获取日历
    function getDateList($request)
    {

        $studentOne = $this->Show_css->getFieldOne('app_student', 'student_id,student_branch', " (student_branch='{$request['student_branch']}' or student_id='{$request['student_id']}')");
        if (!$studentOne) {
            $this->error = 1;
            $this->errortip = "学号不存在";
            return false;
        }

        if ($request['day']) {
            //月初 月末
            $date = getthemonth($request['day']);
        } else {
            //月初 月末
            $date = getthemonth(date("Y-m"));
        }


        $count = date('j', strtotime($date[1]));

        $mothListArray = array();
        for ($i = 1; $i <= $count; $i++) {
            if ($i < 10) {
                $i = '0' . $i;
            }
            $data = array();
            $data['year'] = date('Y', strtotime($date[0]));
            $data['month'] = date('m', strtotime($date[0]));
            $data['day'] = "$i";
            $today = $data['year'] . '-' . $data['month'] . '-' . $data['day'];
            $timestamp = strtotime($today);
            $weekday = date('N', $timestamp); // 1 (monday) to 7 (sunday)
            if ($weekday >= 6) {
                $data['rest'] = 1;
            } else {
                $data['rest'] = 0;
            }
            array_push($mothListArray, $data);
        }

//
//        $sql = "select log_id,is_read,submit_time,status
//                from app_student_stationlog
//                where student_branch='{$studentOne['student_branch']}' and submit_time between '{$date[0]}' and '{$date[1]}' and submit_status=2
//                group by submit_time";
//
//        $mothListArray = $this->Show_css->selectClear($sql);
//        if ($mothListArray) {
//            foreach ($mothListArray as $k => &$v) {
////                $v['year'] = date('Y', strtotime($v['submit_time']));
////                $v['month'] = date('m', strtotime($v['submit_time']));
////                $v['day'] = date('d', strtotime($v['submit_time']));
////                unset($mothListArray[$k]['submit_time']);
//            }
//            $monthArr = array_column($mothListArray, 'day');
//        }
//
//        $count = date('j', strtotime($date[1]));
//        if ($mothListArray) {
//            for ($i = 1; $i <= $count; $i++) {
//                if ($i < 10) {
//                    $i = '0' . $i;
//                }
//                if (!in_array($i, $monthArr)) {
//                    $data['year'] = date('Y', strtotime($date[0]));
//                    $data['month'] = date('m', strtotime($date[0]));
//                    $data['day'] = "$i";
//                    $data['is_read'] = strval(-1);
//                    $data['status'] = strval(-1);
//                    array_push($mothListArray, $data);
//                }
//            }
//            usort($mothListArray, function ($a, $b) {
//                if ($a['day'] == $b['day']) return 0;
//                return $a['day'] > $b['day'] ? 1 : -1;
//            });
//        } else {
//            $mothListArray = array();
//            for ($i = 1; $i <= $count; $i++) {
//                if ($i < 10) {
//                    $i = '0' . $i;
//                }
//                $data = array();
//                $data['year'] = date('Y', strtotime($date[0]));
//                $data['month'] = date('m', strtotime($date[0]));
//                $data['day'] = "$i";
//                $data['is_read'] = strval(-1);
//                $data['status'] = strval(-1);
//                array_push($mothListArray, $data);
//            }
//        }

        $result = array();
        $result['list'] = $mothListArray;

        $this->error = 0;
        $this->errortip = "获取成功！";
        $this->result = $result;
        return true;
    }


    //家联本日期列表
    function getStationDateList($request)
    {

        $studentOne = $this->Show_css->getFieldOne('app_student', 'student_id,student_branch', " (student_branch='{$request['student_branch']}' or student_id='{$request['student_id']}')");
        if (!$studentOne) {
            $this->error = 1;
            $this->errortip = "学号不存在";
            return false;
        }

        //月初 月末
        $date = getthemonth($request['day']);

        $sql = "select log_id,is_read,submit_time,status 
                from app_student_stationlog 
                where student_branch='{$studentOne['student_branch']}' and submit_time between '{$date[0]}' and '{$date[1]}' and submit_status=2 
                group by submit_time";

        $mothListArray = $this->Show_css->selectClear($sql);
        if ($mothListArray) {
            foreach ($mothListArray as $k => &$v) {
                $v['year'] = date('Y', strtotime($v['submit_time']));
                $v['month'] = date('m', strtotime($v['submit_time']));
                $v['day'] = date('d', strtotime($v['submit_time']));
                unset($mothListArray[$k]['submit_time']);
            }
            $monthArr = array_column($mothListArray, 'day');
        }

        $count = date('j', strtotime($date[1]));
        if ($mothListArray) {
            for ($i = 1; $i <= $count; $i++) {
                if ($i < 10) {
                    $i = '0' . $i;
                }
                if (!in_array($i, $monthArr)) {
                    $data['year'] = date('Y', strtotime($date[0]));
                    $data['month'] = date('m', strtotime($date[0]));
                    $data['day'] = "$i";
                    $data['is_read'] = strval(-1);
                    $data['status'] = strval(-1);
                    array_push($mothListArray, $data);
                }
            }
            usort($mothListArray, function ($a, $b) {
                if ($a['day'] == $b['day']) return 0;
                return $a['day'] > $b['day'] ? 1 : -1;
            });
        } else {
            $mothListArray = array();
            for ($i = 1; $i <= $count; $i++) {
                if ($i < 10) {
                    $i = '0' . $i;
                }
                $data = array();
                $data['year'] = date('Y', strtotime($date[0]));
                $data['month'] = date('m', strtotime($date[0]));
                $data['day'] = "$i";
                $data['is_read'] = strval(-1);
                $data['status'] = strval(-1);
                array_push($mothListArray, $data);
            }
        }

        $result = array();
        $result['list'] = $mothListArray;

        $this->error = 0;
        $this->errortip = "获取成功！";
        $this->result = $result;
        return true;
    }


    //阅读反馈日期列表
    function getFeedbackDateList($request)
    {

        //月初 月末
        $date = getthemonth($request['day']);

        $sql = "select feedback_id,feedback_isread as is_read,feedback_date 
                from eas_classes_feedback 
                where student_id='{$request['student_id']}' and feedback_date between '{$date[0]}' and '{$date[1]}' and feedback_status=1 
                group by feedback_date";

        $mothListArray = $this->Show_css->selectClear($sql);
        if ($mothListArray) {
            foreach ($mothListArray as $k => &$v) {
                $v['year'] = date('Y', strtotime($v['feedback_date']));
                $v['month'] = date('m', strtotime($v['feedback_date']));
                $v['day'] = date('d', strtotime($v['feedback_date']));
                unset($mothListArray[$k]['feedback_date']);
            }
            $monthArr = array_column($mothListArray, 'day');
        }

        $count = date('j', strtotime($date[1]));
        if ($mothListArray) {
            for ($i = 1; $i <= $count; $i++) {
                if ($i < 10) {
                    $i = '0' . $i;
                }
                if (!in_array($i, $monthArr)) {
                    $data['year'] = date('Y', strtotime($date[0]));
                    $data['month'] = date('m', strtotime($date[0]));
                    $data['day'] = "$i";
                    $data['is_read'] = strval(-1);
                    array_push($mothListArray, $data);
                }
            }
            usort($mothListArray, function ($a, $b) {
                if ($a['day'] == $b['day']) return 0;
                return $a['day'] > $b['day'] ? 1 : -1;
            });
        } else {
            $mothListArray = array();
            for ($i = 1; $i <= $count; $i++) {
                if ($i < 10) {
                    $i = '0' . $i;
                }
                $data = array();
                $data['year'] = date('Y', strtotime($date[0]));
                $data['month'] = date('m', strtotime($date[0]));
                $data['day'] = "$i";
                $data['is_read'] = strval(-1);
                array_push($mothListArray, $data);
            }
        }

        $result = array();
        $result['list'] = $mothListArray;

        $this->error = 0;
        $this->errortip = "获取成功！";
        $this->result = $result;
        return true;
    }

    //预约接送日期列表
    function getAppointmentDateList($request)
    {

        //月初 月末
        $date = getthemonth($request['day']);

        $sql = "select *
                from eas_appointment_msinfo 
                where student_id='{$request['student_id']}' and msinfo_day between '{$date[0]}' and '{$date[1]}' and msinfo_status > '-1' 
                group by msinfo_day";

        $mothListArray = $this->Show_css->selectClear($sql);
        if ($mothListArray) {
            foreach ($mothListArray as $k => &$v) {
                $v['year'] = date('Y', strtotime($v['msinfo_day']));
                $v['month'] = date('m', strtotime($v['msinfo_day']));
                $v['day'] = date('d', strtotime($v['msinfo_day']));
                unset($mothListArray[$k]['msinfo_day']);
            }
            $monthArr = array_column($mothListArray, 'day');
        }

        $count = date('j', strtotime($date[1]));
        if ($mothListArray) {
            for ($i = 1; $i <= $count; $i++) {
                if ($i < 10) {
                    $i = '0' . $i;
                }
                if (!in_array($i, $monthArr)) {
                    $data['year'] = date('Y', strtotime($date[0]));
                    $data['month'] = date('m', strtotime($date[0]));
                    $data['day'] = "$i";
//                    $data['is_read'] = strval(-1);
                    array_push($mothListArray, $data);
                }
            }
            usort($mothListArray, function ($a, $b) {
                if ($a['day'] == $b['day']) return 0;
                return $a['day'] > $b['day'] ? 1 : -1;
            });
        } else {
            $mothListArray = array();
            for ($i = 1; $i <= $count; $i++) {
                if ($i < 10) {
                    $i = '0' . $i;
                }
                $data = array();
                $data['year'] = date('Y', strtotime($date[0]));
                $data['month'] = date('m', strtotime($date[0]));
                $data['day'] = "$i";
//                $data['is_read'] = strval(-1);
                array_push($mothListArray, $data);
            }
        }

        $result = array();
        $result['list'] = $mothListArray;

        $this->error = 0;
        $this->errortip = "获取成功！";
        $this->result = $result;
        return true;
    }

    //获取学习报告
    function courseTimesDetail($request)
    {
        if (!isset($request['times_id']) || $request['times_id'] == '') {
            $this->error = 1;
            $this->errortip = '课次参数不能为空！';
            return false;
        }

        $timesOne = $this->Show_css->selectOne(" select times_name,course_branch,times_topic,times_target,times_preview,times_propose,times_sector,times_compare from eas_course_times where times_id = '{$request['times_id']}' ");
        $timesOne['times_topic'] = trim($timesOne['times_topic']) ? $timesOne['times_topic'] : '';
        $timesOne['times_target'] = trim($timesOne['times_target']) ? $timesOne['times_target'] : '';
        $timesOne['times_preview'] = trim($timesOne['times_preview']) ? $timesOne['times_preview'] : '';
        $timesOne['times_propose'] = trim($timesOne['times_propose']) ? $timesOne['times_propose'] : '';
        $timesOne['times_sector'] = trim($timesOne['times_sector']) ? $timesOne['times_sector'] : '';
        $timesOne['times_compare'] = trim($timesOne['times_compare']) ? $timesOne['times_compare'] : '';

        //自定义菜单名 20250314补充  -- app 要求
        $key = 0;
        if ($timesOne['times_topic']) {
            $timesOne['res'][$key]['title'] = "课题";
            $timesOne['res'][$key]['content'] = trim($timesOne['times_topic']);
            $key++;
        }
        if ($timesOne['times_target']) {
            $timesOne['res'][$key]['title'] = "教学目标";
            $timesOne['res'][$key]['content'] = trim($timesOne['times_target']);
            $key++;
        }
        if ($timesOne['times_preview']) {
            $timesOne['res'][$key]['title'] = "学习进度";
            $timesOne['res'][$key]['content'] = trim($timesOne['times_preview']);
            $key++;
        }
        if ($timesOne['times_propose']) {
            $timesOne['res'][$key]['title'] = "家辅建议";
            $timesOne['res'][$key]['content'] = trim($timesOne['times_propose']);
            $key++;
        }
        if ($timesOne['times_sector']) {
            $timesOne['res'][$key]['title'] = "知识板块";
            $timesOne['res'][$key]['content'] = trim($timesOne['times_sector']);
            $key++;
        }
        if ($timesOne['times_compare']) {
            $timesOne['res'][$key]['title'] = "知识对标";
            $timesOne['res'][$key]['content'] = trim($timesOne['times_compare']);
            $key++;
        }

        //是否默认的
        $sql = "select a.times_id,a.times_name
                ,ifnull((select 1 from eas_classes_studytimes as x,eas_classes_tasks as y where x.class_id=y.class_id and x.times_id=y.times_id and x.student_id='{$request['student_id']}' and x.times_id=a.times_id and x.studytimes_status=1),0) as status
                from eas_course_times as a 
                where a.course_branch='{$timesOne['course_branch']}' 
                having status = 1 
                order by a.times_sort desc 
                ";
        $nowtimesOne = $this->Show_css->selectOne($sql);

        if ($timesOne) {
            if ($nowtimesOne['times_id'] == $request['times_id']) {
                $timesOne['status'] = '1';
            } else {
                $timesOne['status'] = '0';
            }

            $result = array();
            $result['list'] = $timesOne;

            $this->error = 0;
            $this->errortip = '信息获取成功！';
            $this->result = $result;
            return false;
        } else {
            $this->error = 1;
            $this->errortip = '课次信息还未设置！';
            return false;
        }
    }

    //成长记录
    function termKidSummary($request)
    {
        //学生信息
        $studentOne = $this->Show_css->selectOne(" select s.student_id,s.student_cnname,s.student_enname,s.student_img,s.student_sex,
            b.class_id,b.class_cnname,b.course_branch 
            from app_student as s,app_student_study AS a, eas_classes AS b, app_school AS c 
            where s.student_id = '{$request['student_id']}' 
            and s.student_id = a.student_id
            and a.school_id = c.school_id and c.school_branch = '{$request['school_branch']}' 
            and a.class_id = b.class_id  
            order by a.study_endday desc ");

        if (!$studentOne['student_img']) {
            if ($studentOne['student_sex'] == '男') {
                $studentOne['student_img'] = 'https://oss.kidcastle.cn/manage/202411071639x480002336.png';
            } else {
                $studentOne['student_img'] = 'https://oss.kidcastle.cn/manage/202408141717x012146992.png';
            }
        }
        //教师信息
        $teacherOne = $this->Show_css->selectOne(" select  h.teacher_cnname,h.teacher_enname
                from eas_classes_teach as t 
                left join app_teacher as h ON t.teacher_id = h.teacher_id 
                where t.class_id = '{$studentOne['class_id']}' 
                order by t.teach_type ASC limit 0,1 ");
        //任务进度比
        $sql = "select a.times_id,a.times_name
                ,ifnull((select 1 from eas_classes_studytimes as x,eas_classes_tasks as y where x.class_id=y.class_id and x.times_id=y.times_id and x.student_id='{$request['student_id']}' and x.times_id=a.times_id and x.studytimes_status=1),0) as status
                from eas_course_times as a 
                where a.course_branch='{$studentOne['course_branch']}'  
                order by a.times_sort desc 
                ";
        $timesList = $this->Show_css->selectClear($sql);
        $timesListstatus = array_column($timesList, 'status');//发布状态数组
        $timesListstatuscount = array_count_values($timesListstatus);//各个状态 个数
        $alltimesnum = count($timesListstatus);//一共多少个课次任务
        $alltimesnumone = $timesListstatuscount['1'];//已经发布的个数

        $studentOne['student_cnname'] = $studentOne['student_cnname'] ? $studentOne['student_cnname'] : '';
        $studentOne['student_enname'] = $studentOne['student_enname'] ? $studentOne['student_enname'] : '';

        $studentOne['class_cnname'] = $studentOne['class_cnname'];
        $studentOne['teacher_cnname'] = $teacherOne['teacher_enname'] ? ($teacherOne['teacher_cnname'] . '/' . $teacherOne['teacher_enname']) : $teacherOne['teacher_cnname'];
        $studentOne['content'] = "恭喜你完成了今天的内容";
        $studentOne['timesrate'] = ceil($alltimesnumone / $alltimesnum * 100);

        //金币
        $coinOne = $this->Show_css->selectOne("select sum(a.log_playprice) as allcoin from app_student_accountlog as a 
                where a.student_id = '{$request['student_id']}' and a.class_id = '{$studentOne['class_id']}' and a.log_playclass = '+' and a.log_class = '0' ");
        //钻石
        $diamondsOne = $this->Show_css->selectOne("select sum(a.log_playprice) as alldiamonds from app_student_accountlog as a 
                where a.student_id = '{$request['student_id']}' and a.class_id = '{$studentOne['class_id']}' and a.log_playclass = '+' and a.log_class = '1' ");
        //勋章
        $medalOne = $this->Show_css->selectOne(" select count(l.medallog_id) as medalnum from app_student_medallog as l 
                where l.student_id = '{$request['student_id']}' and l.class_id = '{$studentOne['class_id']}' ");

        //金币  钻石  勋章
        $studentOne['allcoin'] = $coinOne['allcoin'] ? intval($coinOne['allcoin']) : 0;
        $studentOne['alldiamonds'] = $diamondsOne['alldiamonds'] ? intval($diamondsOne['alldiamonds']) : 0;
        $studentOne['medalnum'] = $medalOne['medalnum'] ? $medalOne['medalnum'] : 0;

        //----------------------- 我的成就 ------------ 下边代码 --------------------

        //获取第一次入班的时间  //来吉的堡天数
        $studyone = $this->Show_css->selectOne("select study_beginday from app_student_study where student_id = '{$request['student_id']}' order by study_beginday asc limit 0,1 ");
        if ($studyone['study_beginday']) {
            $studytime = strtotime($studyone['study_beginday']);
            $nowtime = time();
            $studyday = ceil(($nowtime - $studytime) / 86400);
        } else {
            $studyday = 0;
        }

        //获得勋章数量
        $medalnum = $this->Show_css->selectOne(" select count(medallog_id) as num from app_student_medallog where student_id = '{$request['student_id']}' and class_id = '{$studentOne['class_id']}' limit 0,1  ");

        //完成任务数量 app_student_learnitemslog
        $tasknum = $this->Show_css->selectOne(" select count(learnitemslog_id) as num from app_student_learnitemslog where student_id = '{$request['student_id']}' and class_id = '{$studentOne['class_id']}' limit 0,1  ");

        //地图闯关数量
        $stepsnum = $this->Show_css->selectOne(" select count(log_id) as num from app_ekidbook_steps_log where student_id = '{$request['student_id']}' and class_id = '{$studentOne['class_id']}' limit 0,1  ");

        //堡贝乐数量
        $kidbooknum = $this->Show_css->selectOne(" select count(work_id) as num from app_ekidbook_work where student_id = '{$request['student_id']}'  limit 0,1  ");
        //百乐汇数量
        $bailehuinum = $this->Show_css->selectOne(" select count(medialog_id) as num from app_member_medialog where student_id = '{$request['student_id']}' and medialog_status = '1' limit 0,1  ");
        //配音秀数量
        $audioknum = $this->Show_css->selectOne(" select count(work_id) as num from app_audiorecords_work where student_id = '{$request['student_id']}' and class_id = '{$studentOne['class_id']}' limit 0,1  ");

        //获得证书数量
        $cardnum = $this->Show_css->selectOne(" select count(b.dynamic_id) as num 
                    FROM ptc_dynamic_student as a 
                    LEFT JOIN ptc_dynamic as b ON a.dynamic_id = b.dynamic_id and b.codemodels_id = '15' 
                    LEFT JOIN ptc_dynamic_class as c ON b.dynamic_id = c.dynamic_id 
                    WHERE a.student_id = '{$request['student_id']}' and c.class_id = '{$studentOne['class_id']}' limit 0,1 ");

        //我的成就
        $schedule = array();
        $schedule['studyday'] = (int)$studyday;//来吉的堡天数
        $schedule['medalnum'] = (int)$medalnum['num'];//获得勋章数量
        $schedule['cardnum'] = (int)$cardnum['num'];//获得证书数量
        $schedule['tasknum'] = (int)$tasknum['num'];//获得完成任务数量
        $schedule['stepsnum'] = (int)$stepsnum['num'];//获得完成关卡数量
        $schedule['kidbooknum'] = (int)$kidbooknum['num'];//获得堡贝乐数量
        $schedule['bailehuinum'] = (int)$bailehuinum['num'];//获得百乐汇
        $schedule['audioknum'] = (int)$audioknum['num'];//获得配音秀数量

        $datalist = array();
        $datalist['studentone'] = $studentOne;//学生 班级 老师 金币 砖石 勋章
        $datalist['schedule'] = $schedule;//整体概括

        $result = array();
        $result['list'] = $datalist;

        $this->error = 0;
        $this->errortip = '数据获取成功！';
        $this->result = $result;
        return false;
    }

    //我的荣誉
    function getStuHonour($request)
    {
        //学生信息
        $studentOne = $this->Show_css->selectOne(" select s.student_id,s.student_cnname,s.student_enname,s.student_img,s.student_sex,
            (select count(d.dynamic_id) from ptc_dynamic as d,ptc_dynamic_student as t where d.codemodels_id = '15' and d.dynamic_id = t.dynamic_id and t.student_id = s.student_id ) as honournum, 
            (select count(d.dynamic_id) from ptc_dynamic as d,ptc_dynamic_student as t where d.codemodels_id = '16' and d.dynamic_id = t.dynamic_id and t.student_id = s.student_id ) as medalnum
            from app_student as s,app_student_study AS a 
            where s.student_id = '{$request['student_id']}' 
            and s.student_id = a.student_id and a.study_islearning = '1' 
            and a.school_id = '{$request['school_id']}' ");

        if ($studentOne) {
            $this->error = 0;
            $this->errortip = "学生基本信息获取成功！";
            $this->result = $studentOne;
            return true;
        } else {
            $this->error = 1;
            $this->errortip = "暂时还没有学生基本！";
            $this->result = array();
            return false;
        }
    }

    //我的荣誉
    function getMyHonour($request)
    {

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $dataList = $this->Show_css->selectClear(" select c.* from (
                    select from_unixtime(d.dynamic_createtime,'%Y-%m-%d') as now_createtime
                    from ptc_dynamic as d,ptc_dynamic_student as t 
                    where ((d.codemodels_id = '15' and d.dynamic_content is not null) or (d.codemodels_id = '16' and d.dynamic_listimgurl is not null ))
                    and d.dynamic_id = t.dynamic_id and t.student_id = '{$request['student_id']}' 
                    group by from_unixtime(d.dynamic_createtime,'%Y-%m-%d')
                ) as c order by c.now_createtime desc 
                LIMIT {$pagestart},{$num}");
        if ($dataList) {
            foreach ($dataList as &$dataVar) {
                $starttime = strtotime($dataVar['now_createtime']);
                $endtime = $starttime + 86400;
                $honourdata = $this->Show_css->selectClear(" select d.codemodels_id,d.dynamic_id,d.dynamic_content,d.dynamic_listimgurl 
                    from ptc_dynamic as d,ptc_dynamic_student as t 
                    where ( (d.codemodels_id = '15' and d.dynamic_content is not null) or (d.codemodels_id = '16' and d.dynamic_listimgurl is not null )) 
                    and d.dynamic_createtime > '{$starttime}' and d.dynamic_createtime < '{$endtime}'
                    and d.dynamic_id = t.dynamic_id and t.student_id = '{$request['student_id']}'  
                    order by d.dynamic_createtime ASC");
                if ($honourdata) {
                    foreach ($honourdata as &$honourvar) {
                        if ($honourvar['codemodels_id'] == '15' && $honourvar['dynamic_content'] <> '') {
                            $dynamic_content = json_decode($honourvar['dynamic_content'], true);
                            if ($dynamic_content) {
                                foreach ($dynamic_content as $dynamicvar) {
                                    if ($dynamicvar['student_id'] == $request['student_id']) {
                                        $honourvar['dynamic_listimgurl'] = $dynamicvar['url'];
                                    }
                                }
                            }
                        } else {
                            $honourvar['dynamic_content'] = $honourvar['dynamic_content'] ? $honourvar['dynamic_content'] : '';
                            $honourvar['dynamic_listimgurl'] = $honourvar['dynamic_listimgurl'] ? $honourvar['dynamic_listimgurl'] : '';
                        }
                    }
                }
                $dataVar['honourdata'] = $honourdata;
            }
            $alldata = $this->Show_css->selectClear(" select from_unixtime(d.dynamic_createtime,'%Y-%m-%d') as now_createtime
                    from ptc_dynamic as d,ptc_dynamic_student as t 
                    where d.codemodels_id in ('15','16') 
                    and d.dynamic_id = t.dynamic_id and t.student_id = '{$request['student_id']}' 
                    group by from_unixtime(d.dynamic_createtime,'%Y-%m-%d')");

            $result = array();
            $result['allnum'] = $alldata ? count($alldata) : 0;;
            $result['list'] = $dataList;

            $this->error = 0;
            $this->errortip = "荣誉信息数据获取成功！";
            $this->result = $result;
            return true;
        } else {
            $this->error = 1;
            $this->errortip = "暂时还没有荣誉信息！";
            $this->result = array();
            return false;
        }

    }

    //学期总结
    function termSummary($request)
    {

        //金币
        $coinOne = $this->Show_css->selectOne("select sum(a.log_playprice) as allcoin from app_student_accountlog as a 
                where a.student_id = '{$request['student_id']}' and a.class_id = '{$request['class_id']}' and a.log_playclass = '+' and a.log_class = '0' ");
        //钻石
        $diamondsOne = $this->Show_css->selectOne("select sum(a.log_playprice) as alldiamonds from app_student_accountlog as a 
                where a.student_id = '{$request['student_id']}' and a.class_id = '{$request['class_id']}' and a.log_playclass = '+' and a.log_class = '1' ");
        //勋章
        $medalOne = $this->Show_css->selectOne(" select count(l.medallog_id) as medalnum from app_student_medallog as l 
                where l.student_id = '{$request['student_id']}' and l.class_id = '{$request['class_id']}' ");

        //金币  钻石  勋章
        $studentOne = array();
        $studentOne['allcoin'] = $coinOne['allcoin'] ? $coinOne['allcoin'] : 0;
        $studentOne['alldiamonds'] = $diamondsOne['alldiamonds'] ? $diamondsOne['alldiamonds'] : 0;
        $studentOne['medalnum'] = $medalOne['medalnum'] ? $medalOne['medalnum'] : 0;


        //任务数量 : 当前这个学生在这个班级中的所有任务中完成了多少个
        $nowtime = time();
        $sql = " select count(n.learnitems_id) as taskitemsnum,count(l.learnitemslog_id) as completenum 
                from eas_classes_tasks as c
                inner join eas_classes_tasks_learnitems as n ON c.classtasks_id = n.classtasks_id  
                left join app_student_learnitemslog as l ON (n.learnitems_id = l.learnitems_id and l.student_id = '{$request['student_id']}') 
                where c.class_id = '{$this->classesOne['class_id']}' and c.classtasks_tasksviewtimes <= '{$nowtime}'  ";
        $taskOne = $this->Show_css->selectOne($sql);

        //各个任务数量统计
        $nowtime = time();
        $sql = " select k.taskitems_tasktype,count(n.learnitems_id) as taskitemsnum,count(l.learnitemslog_id) as completenum
                from eas_classes_tasks as c
                inner join eas_classes_tasks_learnitems as n ON c.classtasks_id = n.classtasks_id   
                inner join app_taskitems as k ON n.taskitems_id = k.taskitems_id  
                left join app_taskitems_modetype as m ON k.taskitems_modetype = m.modetype_id  
                left join app_student_learnitemslog as l ON (n.learnitems_id = l.learnitems_id and l.student_id = '{$request['student_id']}')
                where c.class_id = '{$this->classesOne['class_id']}' and c.classtasks_tasksviewtimes <= '{$nowtime}' 
                group by k.taskitems_tasktype ";
        $taskList = $this->Show_css->selectClear($sql);

        $opennumone = 0;//开口次数(录音任务）
        $opennumtwo = 0;//开口次数(小奇阅读任务）
        $themenum = 0;
        $blhaudionum = 0;
        $blhvideoknum = 0;

        if ($taskList) {
            foreach ($taskList as $taskvar) {
                if ($taskvar['taskitems_tasktype'] == '0') {

                } elseif ($taskvar['taskitems_tasktype'] == '1') {//地图闯关(复习任务）
                    $blhaudionum = $taskvar['completenum'];
                } elseif ($taskvar['taskitems_tasktype'] == '2') {//主动挑战(检测任务）
                    $themenum = $taskvar['completenum'];
                } elseif ($taskvar['taskitems_tasktype'] == '3') {

                } elseif ($taskvar['taskitems_tasktype'] == '4') {//图书馆
                    $blhvideoknum = $taskvar['completenum'];
                } elseif ($taskvar['taskitems_tasktype'] == '5') {

                } elseif ($taskvar['taskitems_tasktype'] == '6') {//开口次数(录音任务）
                    $opennumone = $taskvar['completenum'];
                } elseif ($taskvar['taskitems_tasktype'] == '7') {//开口次数(小奇阅读任务）
                    $opennumtwo = $taskvar['completenum'];
                }
            }
        }

        //配音作品数量
        $workOne = $this->Show_css->selectOne(" select count(work_id) as worknum from app_audiorecords_work where student_id = '{$request['student_id']}' and class_id = '{$this->classesOne['class_id']}' and work_status = '1' ");

        //整体概括 -- 变量是瞿神之前定义的
        $behavior = array();
        $behavior['tasknum'] = (int)$taskOne['completenum'];//任务数量
        $behavior['opennum'] = (int)$opennumone + (int)$opennumtwo;//开口次数
        $behavior['themenum'] = (int)$themenum;//主动挑战(检测任务）
        $behavior['daysnum'] = (int)$workOne['worknum'];//配音作品
        $behavior['blhaudionum'] = (int)$blhaudionum;//地图闯关(复习任务）
        $behavior['blhvideoknum'] = (int)$blhvideoknum;//图书馆


        //任务完成率
        if ($taskOne['taskitemsnum'] == 0) {
            $OneRate = 0;
        } else {
            $OneRate = ($taskOne['taskitemsnum'] > 0) ? ceil($taskOne['completenum'] / $taskOne['taskitemsnum'] * 10) / 10 : 1;
        }
        if ($OneRate >= 0.9) {
            $Onestars = 3;
        } elseif ($OneRate >= 0.7) {
            $Onestars = 2;
        } else {
            $Onestars = 1;
        }
        //答题正确率
        $examineList = $this->Show_css->selectClear(" select examine_correctnums,examine_wrongnums from app_examine where student_id = '{$request['student_id']}' and class_id = '{$this->classesOne['class_id']}' and examine_score > '0'");

        $allrate = 0;
        if ($examineList) {
            foreach ($examineList as $examineVar) {
                if ($examineVar['examine_correctnums'] > 0) {
                    $examintrate = ceil(($examineVar['examine_correctnums'] - $examineVar['examine_wrongnums']) / $examineVar['examine_correctnums'] * 10) / 10;
                    $allrate += $examintrate;
                }
            }
            $allnum = count($examineList);
            $TwoRate = ceil($allrate / $allnum * 10) / 10;
        } else {
            $TwoRate = 0;
        }
        if ($TwoRate >= 0.9) {
            $Twostars = 3;
        } elseif ($TwoRate >= 0.7) {
            $Twostars = 2;
        } else {
            $Twostars = 1;
        }

        //老师评价
//            $request['class_id'] = 957739;
//            $request['times_id'] = 8701;
//            $request['student_id'] = 406269;
        $effectlog = $this->Show_css->selectClear("select e.studytimes_id,e.effectlog_evaldimensjson 
                from eas_classes_studytimes as s
                left join eas_classes_studytimes_effectlog as e ON s.studytimes_id = e.studytimes_id
                where s.class_id = '{$request['class_id']}' and s.student_id = '{$request['student_id']}' ");

        $allchildRate = 0;
        $allchildRatenum = 0;
        if ($effectlog) {
            foreach ($effectlog as $effectlogvar) {
                $effectlog_evaldimensarray = json_decode($effectlogvar['effectlog_evaldimensjson'], true);
                if ($effectlog_evaldimensarray) {
                    $childStars = 0;
                    $childAllStars = 0;
                    foreach ($effectlog_evaldimensarray as $effectlog_evaldimensarrayvar) {
                        $childStars += $effectlog_evaldimensarrayvar['evaldimens_level'];////本次一共 获得了 多少星
                    }
                    $effectlog_evaldimensarraynum = count($effectlog_evaldimensarray);
                    $childAllStars = 5 * $effectlog_evaldimensarraynum;//本次一共多少星

                    $childRate = ceil($childStars / $childAllStars * 10) / 10;

                    $allchildRate += $childRate;
                    $allchildRatenum += 1;
                }
            }
        }
        if ($allchildRatenum > 0) {
            $ThreeRate = ceil($allchildRate / $allchildRatenum * 10) / 10;
        } else {
            $ThreeRate = 0;
        }

        if ($ThreeRate >= 0.9) {
            $Threestars = 3;
        } elseif ($ThreeRate >= 0.7) {
            $Threestars = 2;
        } else {
            $Threestars = 1;
        }

        //分析
        $analysis = array();
        $analysis['one']['rate'] = $OneRate;//率
        $analysis['one']['stars'] = $Onestars;//星
        $analysis['two']['rate'] = $TwoRate;//率
        $analysis['two']['stars'] = $Twostars;//星
        $analysis['three']['rate'] = $ThreeRate;//率
        $analysis['three']['stars'] = $Threestars;//星

        //课程信息
        $courseOne = $this->Show_css->selectOne("select u.course_studyplan,u.course_studyaims,u.course_studyfocus,u.course_nextstudyplan 
                from eas_classes as c 
                left join eas_course as u ON c.course_branch = u.course_branch 
                where c.class_id = '{$request['class_id']}'  ");

        $datalist = array();
        $datalist['studentone'] = $studentOne;//金币 砖石 勋章
        $datalist['behavior'] = $behavior;//整体概括
        $datalist['analysis'] = $analysis;//分析
        $datalist['course'] = $courseOne;//课程（班别）信息

        $result = array();
        $result['list'] = $datalist;

        $this->error = 0;
        $this->errortip = '数据获取成功！';
        $this->result = $result;
        return false;


    }

    //获取学生动态IDString, type 0实时获取 1缓存获取
    function getDynamicId($request, $type = 0)
    {
        $stutemp = $this->Show_css->selectOne("select stutemp_id_ids,stutemp_createtime from ptc_dynamic_stutemp_times 
            where school_id = '{$request['school_id']}' and student_id = '{$request['student_id']}' order by stutemp_id desc limit 1");
        if ($type == 0) {
            if ($stutemp && $stutemp['stutemp_createtime'] > time()-60*15) {
                return $stutemp['stutemp_id_ids'];
            }
            $begintime=time()-86400*60;

            $schoolOne = $this->Show_css->selectOne("select s.school_nature from app_school as s where s.school_id = '{$request['school_id']}'");

            //查到学生所有动态
            $dynamicId = $this->Show_css->selectClear("
                SELECT d.dynamic_id
                FROM ptc_dynamic AS d
                LEFT JOIN ptc_dynamic_class AS c ON d.dynamic_id = c.dynamic_id
                WHERE d.dynamic_status = '1'
                AND d.dynamic_passage LIKE '%{$schoolOne['school_nature']}%'
                AND d.is_restrictstudent = '0'
                AND (d.dynamic_schoolspl = '1' OR EXISTS (SELECT 1 FROM ptc_dynamic_school AS s
                    WHERE s.school_id = '{$request['school_id']}'
                    AND d.dynamic_id = s.dynamic_id))
                AND c.class_id IN (SELECT sd.class_id
                    FROM app_student_study AS sd
                    WHERE sd.student_id = '{$request['student_id']}'
                    AND from_unixtime(d.dynamic_createtime,'%Y%m%d') <= sd.study_endday
                    AND from_unixtime(d.dynamic_createtime,'%Y%m%d') >= sd.study_beginday)
                AND d.dynamic_createtime>='{$begintime}'
                
                UNION ALL
                    SELECT d.dynamic_id
                    FROM ptc_dynamic AS d, ptc_dynamic_student AS s
                    WHERE s.dynamic_id = d.dynamic_id
                    AND d.dynamic_status = '1'
                    AND s.student_id = '{$request['student_id']}'
                    AND d.dynamic_createtime>='{$begintime}'
                
                UNION ALL
                    SELECT d.dynamic_id
                    FROM ptc_dynamic AS d
                    left join ptc_dynamic_school sl on sl.school_id = '{$request['school_id']}' AND d.dynamic_id = sl.dynamic_id
                    left join ptc_dynamic_course AS s on d.dynamic_id = s.dynamic_id 
                    WHERE d.is_restrictclass = '0' 
                    AND d.is_restrictstudent = '0' 
                    AND (d.dynamic_schoolspl = '1' OR sl.school_id>0)
                    AND (d.dynamic_coursespl = '1' OR exists (SELECT 1 
                    FROM app_student_study AS sd 
                    WHERE sd.student_id = '{$request['student_id']}' and sd.course_id=s.course_id
                    AND from_unixtime( d.dynamic_createtime, '%Y%m%d' ) <= sd.study_endday 
                    AND from_unixtime( d.dynamic_createtime, '%Y%m%d' ) >= sd.study_beginday 
                    )) 
                    AND d.dynamic_createtime>='{$begintime}'
                    group by d.dynamic_id
                
                UNION ALL            
                    SELECT d.dynamic_id 
                    FROM ptc_dynamic AS d, ptc_dynamic_course AS s 
                    WHERE s.dynamic_id = d.dynamic_id 
                    AND d.dynamic_status = '1' 
                    AND s.course_id IN (SELECT co.course_id 
                        FROM app_student_study AS sd 
                        LEFT JOIN eas_classes AS cl ON cl.class_id = sd.class_id
                        LEFT JOIN eas_course AS co ON co.course_branch = cl.course_branch 
                        WHERE sd.student_id = '{$request['student_id']}'
                        AND from_unixtime( d.dynamic_createtime, '%Y%m%d' ) <= sd.study_endday 
                        AND from_unixtime( d.dynamic_createtime, '%Y%m%d' ) >= sd.study_beginday )
                    AND d.dynamic_createtime>='{$begintime}'
            ");
            if ($dynamicId) {
                $b = array_column($dynamicId, 'dynamic_id');
                $dynamicIdString = implode(",", $b);
            } else {
                $dynamicIdString = 0;
            }
            $data = array();
            $data['school_id'] = $request['school_id'];
            $data['student_id'] = $request['student_id'];
            $data['stutemp_createtime'] = time();
            $data['stutemp_id_ids'] = $dynamicIdString;
            $this->Show_css->insertData("ptc_dynamic_stutemp_times", $data);
        } else {
            if ($stutemp) {
                $dynamicIdString = $stutemp['stutemp_id_ids'];
            } else {
                $dynamicIdString = 0;
            }
        }

        return $dynamicIdString;
    }

    //家联本
    function getStationlog($request)
    {
        $effectOne = $this->Show_css->selectOne("select count(s.log_id) as num
                from app_student_stationlog as s,app_student_stationlog_info as i 
                where s.student_id='{$request['student_id']}' and s.submit_status = 2 and s.is_read = '0' and s.log_id = i.log_id and i.log_id > 0");

        $datawhere = " student_id='{$request['student_id']}' and submit_status = 2 ";
        if (isset($request['log_id']) && $request['log_id'] != '') {
            $datawhere .= " and s.log_id='{$request['log_id']}'";
        } else {
            if (isset($request['starttime']) && $request['starttime'] != '') {
                $datawhere .= " and s.submit_time='{$request['starttime']}'";
            } else {
                $datawhere .= " and s.submit_time=curdate()";
            }
        }

        $sql = "select s.*,IF((select 1 from app_student_stationlog_info as i where i.log_id = s.log_id limit 0,1 ),1,0) as isnewlog  from app_student_stationlog as s where {$datawhere} ";
        $stationlogOne = $this->Show_css->selectOne($sql);
        if ($stationlogOne) {

            if ($stationlogOne['status'] == '0') {
                $stationlogOne['attendancename'] = '正常';
            } elseif ($stationlogOne['status'] == '1') {
                $stationlogOne['attendancename'] = '缺勤';
            } elseif ($stationlogOne['status'] == '2') {
                $stationlogOne['attendancename'] = '异常';
            }

            if ($stationlogOne['log_id'] > 0) {
                $this->Show_css->updateData("app_student_stationlog", "log_id='{$stationlogOne['log_id']}'", array('is_read' => 1));
            }

            $stationlogOne['member_note'] = (string)$stationlogOne['member_note'];
            $stationlogOne['lesson_note'] = (string)$stationlogOne['lesson_note'];
            $stationlogOne['stationlog_reply_content'] = (string)$stationlogOne['stationlog_reply_content'];

            $stationlogOne['evaldimensArray'] = $stationlogOne['stationlog_evaldimensjson'] != '' ? json_decode($stationlogOne['stationlog_evaldimensjson'], true) : array();
            //为 app 加的一个格式数据 同 $stationlogOne['evaldimensArray']
            if ($stationlogOne['evaldimensArray']) {
                $evaldimensTwo = array();
                foreach ($stationlogOne['evaldimensArray'] as $key => $evaldimensVar) {
                    if ($evaldimensVar) {
                        foreach ($evaldimensVar as $k => $v) {
                            $evaldimensTwo[$key]['title'] = $k;
                            $evaldimensTwo[$key]['value'] = $v;
                        }
                    }
                }
                $stationlogOne['evaldimensArrayTwo'] = $evaldimensTwo;
            } else {
                $stationlogOne['evaldimensArrayTwo'] = array();
            }

            //为了app方便使用，故处理数据类型  //note_json  中  field_type 类型 -1单选 1下拉选择 2多文本域 3文本框 4多选
            $notearray = json_decode($stationlogOne['note_json'], true);
            if ($notearray) {
                foreach ($notearray as &$noteVar) {
                    if ($noteVar['list']) {
                        foreach ($noteVar['list'] as &$var) {
                            if ($var['type'] != '4') {
                                $var['value'] = explode(',,,,,,', $var['value']);
                            }
                        }
                    }
                }
                $stationlogOne['note_json_app'] = $notearray;
            } else {
                $stationlogOne['note_json_app'] = [];
            }
            $stationlogOne['note_json'] = json_decode($stationlogOne['note_json'], true);
            $stationlogOne['img_json'] = json_decode($stationlogOne['img_json'], true);
            $stationlogOne['familyfield_json'] = json_decode($stationlogOne['familyfield_json'], true);
            $stationlogOne['stationlog_evaldimensjson'] = json_decode($stationlogOne['stationlog_evaldimensjson'], true);

            $chattinglist = $this->Show_css->selectClear("select s.chatting_type,s.chatting_note,FROM_UNIXTIME(s.chatting_createtime, '%m.%d %H:%i') as chatting_createtime,t.teacher_cnname,t.teacher_enname,t.teacher_img,m.member_nickname,'' as member_img 
                            from app_student_stationlog_chatting as s 
                            left join app_member as m ON s.member_id = m.member_id  
                            left join app_teacher as t ON s.teacher_id = t.teacher_id 
                            where s.log_id = '{$stationlogOne['log_id']}' and chatting_isrevoke = '0' and (s.chatting_type = '1' or (s.chatting_type = '2' and s.chatting_status = '1')) ");
            if ($chattinglist) {
                foreach ($chattinglist as &$chattingvar) {
                    if ($chattingvar['teacher_enname']) {
                        $chattingvar['teacher_cnname'] = $chattingvar['teacher_cnname'] . '/' . $chattingvar['teacher_enname'];
                    }
                }
            }
            $stationlogOne['chattinglist'] = $chattinglist ?: array();//没数据是改为 空数组 app 用

            $result = array();
            $result['list'] = $stationlogOne;
            $result['effectnum'] = $effectOne['num'] ?: 0;

            $this->error = 0;
            $this->errortip = "获取家联本成功";
            $this->result = $result;
            return true;
        } else {
            $result = array();
            $result['list'] = array();
            $result['effectnum'] = $effectOne['num'] ?: 0;

            $this->error = 1;
            $this->errortip = "老师还未发布家联本哦！";
            $this->result = $result;
            return false;
        }
    }


    //少儿的 家长留言
    function MemberReply($request)
    {
        if ($this->Show_css->selectOne(" select studytimes_id from eas_classes_studytimes_effectlog where studytimes_id = '{$request['studytimes_id']}' and effectlog_memberstar > '0' limit 0,1 ")) {
            if (!isset($request['replylog_note']) || $request['replylog_note'] == '') {
                $this->error = 1;
                $this->errortip = '回复内容不能为空！';
                return false;
            }
        } else {
            if (!isset($request['effectlog_memberstar']) || $request['effectlog_memberstar'] == '') {
                $this->error = 1;
                $this->errortip = '评星不能为空！';
                return false;
            }
            if (!isset($request['effectlog_starnote']) || $request['effectlog_starnote'] == '') {
                $this->error = 1;
                $this->errortip = '家长选评文字不能为空！';
                return false;
            }
        }

        $logOne = $this->Show_css->getFieldOne("eas_classes_studytimes_effectlog", "studytimes_id,effectlog_memberstar", "studytimes_id='{$request['studytimes_id']}' ");
        if ($logOne) {
//            $data = array();
//            $data['is_read'] = 1;
//            $this->Show_css->updateData("app_student_stationlog", "log_id='{$request['log_id']}'", $data);
            $this->Show_css->query("SET NAMES utf8mb4");
            $data = array();
            $data['studytimes_id'] = $request['studytimes_id'];
            $data['member_id'] = $request['member_id'];
            $data['replylog_note'] = $request['replylog_note'] ? emoji_encode($request['replylog_note']) : '家长满意度提交';//提交只有第一次可以空
            $data['replylog_time'] = time();
            if ($this->Show_css->insertData("eas_classes_studytimes_replylog", $data)) {
                if ($logOne['effectlog_memberstar'] == '0' || is_null($logOne['effectlog_memberstar'])) {
                    $datatwo = array();
                    $datatwo['effectlog_memberstar'] = $request['effectlog_memberstar'];
                    $datatwo['effectlog_starnote'] = emoji_encode($request['effectlog_starnote']);
                    $this->Show_css->updateData("eas_classes_studytimes_effectlog", "studytimes_id='{$request['studytimes_id']}'", $datatwo);
                }

                $this->error = 0;
                $this->errortip = '回复成功！';
                return true;
            } else {
                $this->error = 1;
                $this->errortip = '回复失败！';
                return false;
            }
        } else {
            $this->error = 1;
            $this->errortip = '未获取到信息！';
            return false;
        }
    }

    //家长留言
    function memberNote($request)
    {
        if (!isset($request['chatting_note']) || $request['chatting_note'] == '') {
            $this->error = 1;
            $this->errortip = '回复内容不能为空！';
            return false;
        }
        $logOne = $this->Show_css->getFieldOne("app_student_stationlog", "createtime,stationlog_send_fixeddate,stationlog_isfixtimesend", "log_id='{$request['log_id']}' and student_id = '{$request['student_id']}' ");
        if ($logOne) {
//            if ($logOne && APPVER == 'TW' && $logOne['stationlog_isfixtimesend'] == 0) {
//                if (date("Y-m-d H:i:s") > date("Y-m-d", strtotime('+1 days', $logOne['createtime'])) . ' 08:30:00') {
//                    $this->error = 1;
//                    $this->errortip = '已超过可回复的时间';
//                    return false;
//                }
//            } elseif ($logOne && APPVER == 'TW' && $logOne['stationlog_isfixtimesend'] == 1) {
//                if (date("Y-m-d H:i:s") > date("Y-m-d", strtotime('+1 days', strtotime($logOne['stationlog_send_fixeddate']))) . ' 08:30:00') {
//                    $this->error = 1;
//                    $this->errortip = '已超过可回复的时间';
//                    return false;
//                }
//            }

            $data = array();
            $data['is_read'] = 1;
            $this->Show_css->updateData("app_student_stationlog", "log_id='{$request['log_id']}'", $data);
            $this->Show_css->query("SET NAMES utf8mb4");
            $data = array();
            $data['log_id'] = $request['log_id'];
            $data['member_id'] = $request['member_id'];
            $data['chatting_note'] = emoji_encode($request['chatting_note']);
            $data['chatting_type'] = 1;
            $data['chatting_createtime'] = time();
            if ($this->Show_css->insertData("app_student_stationlog_chatting", $data)) {
                $this->error = 0;
                $this->errortip = '回复成功！';
                return true;
            } else {
                $this->error = 1;
                $this->errortip = '回复失败！';
                return false;
            }
        } else {
            $this->error = 1;
            $this->errortip = '未获取到家联本信息！';
            return false;
        }
    }

    //获取家校通 学习报告
    function getProgressOne($request)
    {
        //学生课次信息
        $timesone = $this->Show_css->selectOne(" select times_id,course_branch,times_name,times_isreadbook,times_isminchallenge,times_ismaxchallenge,times_ispicbooksuptask,times_ispicbooksclockin,times_ispicbooksdowntask,times_sort from eas_course_times where times_id = '{$request['times_id']}' limit 0,1 ");
        if ($timesone) {
            //学生信息
            $studentOne = $this->Show_css->selectOne(" select student_id,s.student_cnname,s.student_enname,s.student_img from app_student as s where student_id = '{$request['student_id']}' ");
            //班级信息
            $classesOne = $this->Show_css->selectOne(" select c.class_enname as class_cnname,c.class_branch,h.teacher_cnname,h.teacher_enname  
                from eas_classes as c 
                left join eas_classes_teach as t ON c.class_id = t.class_id and t.teach_type = '0' and t.teach_status = '0'
                left join app_teacher as h ON t.teacher_id = h.teacher_id 
                where c.class_id = '{$request['class_id']}' ");
            //任务进度比
            $sql = "select a.times_id,a.times_name
                ,ifnull((select 1 from eas_classes_studytimes as x,eas_classes_tasks as y where x.class_id=y.class_id and x.times_id=y.times_id and x.student_id='{$request['student_id']}' and x.times_id=a.times_id and x.studytimes_status=1 limit 0,1),0) as status
                from eas_course_times as a 
                where a.course_branch='{$timesone['course_branch']}'  
                order by a.times_sort desc 
                ";
            $timesList = $this->Show_css->selectClear($sql);
            $timesListstatus = array_column($timesList, 'status');//发布状态数组
            $timesListstatuscount = array_count_values($timesListstatus);//各个状态 个数
            $alltimesnum = count($timesListstatus);//一共多少个课次任务
//            $alltimesnumone = $timesListstatuscount['1'];//已经发布的个数

            $sql = "select count(a.times_id) as num  from eas_course_times as a 
            where a.course_branch='{$timesone['course_branch']}' and a.times_sort <= '{$timesone['times_sort']}'
            order by a.times_sort desc  ";
            $nowtimesnumsqlone = $this->Show_css->selectOne($sql);
            $nowtimesnumone = $nowtimesnumsqlone['num'];//已经发布的个数

            $studentOne['student_cnname'] = $studentOne['student_enname'] ? ($studentOne['student_cnname'] . '/' . $studentOne['student_enname']) : $studentOne['student_cnname'];
            $studentOne['class_cnname'] = $classesOne['class_cnname'];
            $studentOne['teacher_cnname'] = $classesOne['teacher_enname'] ? ($classesOne['teacher_cnname'] . '/' . $classesOne['teacher_enname']) : $classesOne['teacher_cnname'];
            $studentOne['content'] = "恭喜你完成了" . $timesone['times_name'] . "的学习任务";
//            $studentOne['timesrate'] = ceil($alltimesnumone/$alltimesnum*100);
            $studentOne['timesrate'] = ceil($nowtimesnumone / $alltimesnum * 100);

            //奇趣任务进度统计
            $sql = " select m.modetype_name,count(c.classtasks_id) as taskitemsnum,count(l.learnitemslog_id) as completenum
                from eas_classes_tasks as c
                LEFT JOIN eas_classes_tasks_learnitems as n ON c.classtasks_id = n.classtasks_id  
                left join app_taskitems as k ON n.taskitems_id = k.taskitems_id
                left join app_taskitems_modetype as m ON k.taskitems_modetype = m.modetype_id
                left join app_student_learnitemslog as l ON (n.learnitems_id = l.learnitems_id and l.student_id = '{$request['student_id']}')
                where c.class_id = '{$this->classesOne['class_id']}' and c.times_id = '{$request['times_id']}' and k.taskitems_tasktype not in ('4','8') 
                group by k.taskitems_modetype ";
//            and m.modetype_id <> '4'
            $taskList = $this->Show_css->selectClear($sql);
            if ($taskList) {
                foreach ($taskList as &$taskVar) {
                    if ($taskVar['taskitemsnum'] == '0') {
                        $taskVar['rate'] = 0;
                    } else {
                        $taskVar['rate'] = $taskVar['taskitemsnum'] ? ceil($taskVar['completenum'] / $taskVar['taskitemsnum'] * 100) : 0;
                    }
                }
            }
            //奇趣阅读任务统计
            $sql = " select G.* from 
                (select m.modetype_name,t.times_sort,count(c.classtasks_id) as taskitemsnum,count(l.learnitemslog_id) as completenum,k.taskitems_id,k.taskitems_tasktype,n.learnitems_id,l.learnitemslog_id
                from eas_classes_tasks as c 
                left join eas_course_times as t ON c.times_id = t.times_id 
                LEFT JOIN eas_classes_tasks_learnitems as n ON c.classtasks_id = n.classtasks_id  
                left join app_taskitems as k ON n.taskitems_id = k.taskitems_id
                left join app_taskitems_modetype as m ON k.taskitems_modetype = m.modetype_id
                left join app_student_learnitemslog as l ON (n.learnitems_id = l.learnitems_id and l.student_id = '{$request['student_id']}')
                where c.class_id = '{$this->classesOne['class_id']}' 
                and k.taskitems_tasktype in ('4','8') 
                and n.learnitems_id > 0 
                group by k.taskitems_id) as G order by G.times_sort ASC ";
//            and m.modetype_id = '4'
            $readtaskList = $this->Show_css->selectClear($sql);

            if ($readtaskList) {
                $readtaskData = array();
                foreach ($readtaskList as $key => $taskVarOne) {
                    if ($taskVarOne['learnitems_id'] > 0 && $taskVarOne['learnitems_id'] != '') {
                        if ($taskVarOne['taskitems_tasktype'] == '4') {//图书馆 任务--app_readbooks
                            $bookOne = $this->Show_css->selectOne("select b.readbooks_title as bookstitle from app_taskitems_readbooks as r,app_readbooks as b 
                                where r.taskitems_id = '{$taskVarOne['taskitems_id']}' and r.readbooks_id = b.readbooks_id ");
                        } elseif ($taskVarOne['taskitems_tasktype'] == '8') {//小奇阅读 任务--app_picbooks
                            $bookOne = $this->Show_css->selectOne("select b.picbooks_title as bookstitle from app_taskitems_picbooks as r,app_picbooks as b 
                                where r.taskitems_id = '{$taskVarOne['taskitems_id']}' and r.picbooks_id = b.picbooks_id ");
                        }
                        if ($bookOne['bookstitle']) {
                            $readtaskData[$key] = $taskVarOne;
                            $readtaskData[$key]['bookstitle'] = $bookOne['bookstitle'];
                            $readtaskData[$key]['is_complete'] = $taskVarOne['learnitemslog_id'] > 0 ? 1 : 0;
                        }
                    }
                }
            }
            $readtaskList = $readtaskData;

            //回复记录
            $chattinglist = $this->Show_css->selectClear(" select s.studytimes_id,m.member_id,m.member_imghead,m.member_nickname,t.teacher_cnname,t.teacher_enname,t.teacher_img,
                r.replylog_note,r.replylog_time 
                from eas_classes_studytimes as s 
                left join eas_classes_studytimes_replylog as r ON s.studytimes_id = r.studytimes_id   
                left join app_member as m ON r.member_id = m.member_id
                left join app_teacher as t ON r.teacher_id = t.teacher_id 
                where s.class_id = '{$request['class_id']}' and s.times_id = '{$request['times_id']}' and s.student_id = '{$request['student_id']}' 
                and r.replylog_id > 0 and r.replylog_note <> '' and r.replylog_note is not null ");
            if ($chattinglist) {
                $aa = 0;
                foreach ($chattinglist as $key => &$chattingvar) {
                    $chattingvar['ismember'] = $chattingvar['member_id'] > 0 ? 1 : 0;
                    if ($chattingvar['ismember'] == 1) {
                        $aa++;
                        if ($aa == 1) {
                            $effectlogOne = $this->Show_css->selectOne(" select effectlog_memberstar,effectlog_starnote from eas_classes_studytimes_effectlog where studytimes_id = '{$chattingvar['studytimes_id']}' ");
                            $chattingvar['effectlog_memberstar'] = (int)$effectlogOne['effectlog_memberstar'];
                            $chattingvar['effectlog_starnote'] = is_null($effectlogOne['effectlog_starnote']) ? '' : $effectlogOne['effectlog_starnote'];

                            $chattingvar['replylog_note'] = (is_null($chattingvar['replylog_note']) || $chattingvar['replylog_note'] == '家长满意度提交') ? '' : $chattingvar['replylog_note'];
                        }
                    }
                    $chattingvar['member_imghead'] = $chattingvar['member_imghead'] ? $chattingvar['member_imghead'] : 'https://oss.kidcastle.cn/manage/202408141636x639331811.png';
                    $chattingvar['member_nickname'] = '家长说';
                    $chattingvar['teacher_cnname'] = '老师说';
                    if (!$chattingvar['teacher_img']) {
                        $chattingvar['teacher_img'] = 'https://oss.kidcastle.cn/manage/202411071704x818845379.png';
                    }
                    $chattingvar['replylog_time'] = $chattingvar['replylog_time'] ? date("m-d H:i", $chattingvar['replylog_time']) : 'https://oss.kidcastle.cn/manage/202408141636x639331811.png';
                }
            }

            //满意文字
            $satisfied = [
                '谢谢老师，我对本次课成效非常满意',
                '谢谢老师，我对本次课成效较为满意',
                '对于本次课成效，我觉得一般',
                '觉得本次课成效还有待加强',
            ];

            //电测等级
            $trackscorelist = array();
            $trackscore = $this->Show_css->selectOne(" select s.trackscore_name from eas_student_track as t,eas_code_trackscore as s where t.class_id = '{$request['class_id']}' and t.times_id = '{$request['times_id']}' and student_id = '{$request['student_id']}' and t.trackscore_id = s.trackscore_id limit 0,1 ");
            if ($trackscore['trackscore_name'] == '' && is_null($trackscore['trackscore_name'])) {
                $trackscorelist['scorename'] = '';
            } else {
                $trackscorelist['scorename'] = $trackscore['trackscore_name'];
            }

            //学生课次表现 (学习报告、学习评估）
            $studytimesOne = $this->Show_css->selectOne("select s.studytimes_id FROM eas_classes_studytimes as s where s.class_id = '{$request['class_id']}' and s.times_id = '{$request['times_id']}' and s.student_id = '{$request['student_id']}'");
            if (!$studytimesOne) {
                //更新学员学习明细表
                $Model = new \Model\SchoolManageModel();
                $Model->UpdataClassStudytimes($classesOne['class_branch']);
            }

            $effectlogssql = " select s.studytimes_id,
                e.effectlogs_id,e.effect_id,e.effectlogs_var,
                f.effect_name,f.effect_type,f.effect_class,f.effect_ispersonal,f.effect_minvar,f.effect_maxvar
                from eas_classes_studytimes as s
                left join eas_classes_studytimes_effectlogs as e ON s.studytimes_id = e.studytimes_id
                left join eas_course_effect as f ON e.effect_id = f.effect_id 
                where s.studytimes_id = '{$studytimesOne['studytimes_id']}' and f.effect_ismember = '1'
                 ";
            // where s.studytimes_id = '{$studytimesOne['studytimes_id']}' and f.effect_ismember = '1'
            //where s.studytimes_id = '31044' and f.effect_ismember = '1'
            $effectlognew = $this->Show_css->selectClear($effectlogssql);

            $effectlognewarray = array();//新数组返回给前端

            if ($effectlognew) {
                foreach ($effectlognew as $key => $effectlognewVar) {

                    $effectlognewarray[$key]['studytimes_id'] = $effectlognewVar['studytimes_id'];//成效
                    $effectlognewarray[$key]['effectlogs_id'] = $effectlognewVar['effectlogs_id'];//成效
                    $effectlognewarray[$key]['effect_id'] = $effectlognewVar['effect_id'];//成效 ID
                    $effectlognewarray[$key]['effect_name'] = $effectlognewVar['effect_name'];//成效 名称

                    if ($effectlognewVar['effect_type'] == '1' || $effectlognewVar['effect_type'] == '3') {
                        $effectlognewarray[$key]['effect_minvar'] = 0;//成效 最小值
                        $effectlognewarray[$key]['effect_maxvar'] = 100;//成效 最大值
                        $effectlognewarray[$key]['effect_class'] = 0;//显示样式0进度条1星级
                    } else {
                        $effectlognewarray[$key]['effect_minvar'] = (int)$effectlognewVar['effect_minvar'];//成效 最小值
                        $effectlognewarray[$key]['effect_maxvar'] = (int)$effectlognewVar['effect_maxvar'];//成效 最大值
                        $effectlognewarray[$key]['effect_class'] = (int)$effectlognewVar['effect_class'];//显示样式0进度条1星级
                    }
                    $effectlognewarray[$key]['effectlogs_var'] = (int)$effectlognewVar['effectlogs_var'];//进度数值  0-100

                    if ($effectlognewVar['effect_type'] == '1') {
                        //数字模式有补考，取补考最后一个值
                        $examlogsOne = $this->Show_css->selectOne(" select examlogs_score from eas_classes_studytimes_effectlogs_examlogs where effectlogs_id = '{$effectlognewVar['effectlogs_id']}' order by examlogs_createtime desc limit 0,1  ");
                        if ($examlogsOne) {
                            $effectlognewarray[$key]['effectlogs_var'] = (int)$examlogsOne['examlogs_score'];//进度数值  0-100
                            $effectlognewVar['effectlogs_var'] = (int)$examlogsOne['examlogs_score'];//进度数值  0-100
                        }

                        $numberOne = $this->Show_css->selectOne(" select number_id,number_personal from eas_course_effect_number where effect_id = '{$effectlognewVar['effect_id']}' and number_mincompar <= '{$effectlognewVar['effectlogs_var']}' and  number_maxcompar >= '{$effectlognewVar['effectlogs_var']}' ");

                        if ($effectlognewVar['effect_ispersonal'] == '1') {//是否开启个性化显示
                            $effectlognewarray[$key]['personal_name'] = is_null($numberOne['number_personal']) ? '' : $numberOne['number_personal'];//成效 个性化名称
                        } else {
                            $effectlognewarray[$key]['personal_name'] = $examlogsOne['examlogs_score'] ? (int)$examlogsOne['examlogs_score'] : (int)$effectlognewVar['effectlogs_var'];//成效 个性化名称
                        }

                        $effectlognewarray[$key]['rate'] = $effectlognewarray[$key]['effect_maxvar'] == '0' ? 0 : (sprintf("%.2f", ($effectlognewarray[$key]['effectlogs_var'] / $effectlognewarray[$key]['effect_maxvar'])) * 100);//百分比
                    } elseif ($effectlognewVar['effect_type'] == '2') {
                        $optionOne = $this->Show_css->selectOne(" select option_id,option_name,option_personal,option_percent from eas_course_effect_option where effect_id = '{$effectlognewVar['effect_id']}' and option_name like '%{$effectlognewarray[$key]['effectlogs_var']}%'  limit 0,1  ");

                        if ($effectlognewVar['effect_ispersonal'] == '1') {//是否开启个性化显示
                            $effectlognewarray[$key]['personal_name'] = is_null($optionOne['option_personal']) ? '' : $optionOne['option_personal'];//成效 个性化名称
                        } else {
                            $effectlognewarray[$key]['personal_name'] = is_null($optionOne['option_name']) ? '' : $optionOne['option_name'];//成效 个性化名称
                        }

                        //显示样式0进度条1星级   不同样式 百分比取值不一样
                        if ($effectlognewVar['effect_class'] == '1') {
                            $effectlognewarray[$key]['rate'] = $effectlognewarray[$key]['effect_maxvar'] == '0' ? 0 : (sprintf("%.2f", ($effectlognewarray[$key]['effectlogs_var'] / $effectlognewarray[$key]['effect_maxvar'])) * 100);//百分比
                        } else {
                            $effectlognewarray[$key]['rate'] = $effectlognewarray[$key]['effect_maxvar'] == '0' ? 0 : (sprintf("%.2f", ($optionOne['option_percent'] / 100)) * 100);//百分比
                        }

                    } elseif ($effectlognewVar['effect_type'] == '3') {
                        $optionOne = $this->Show_css->selectOne(" select option_id,option_name,option_personal,option_percent from eas_course_effect_option where effect_id = '{$effectlognewVar['effect_id']}' and option_id = '{$effectlognewVar['effectlogs_var']}' ");

                        if ($effectlognewVar['effect_ispersonal'] == '1') {//是否开启个性化显示
                            $effectlognewarray[$key]['personal_name'] = is_null($optionOne['option_personal']) ? '' : $optionOne['option_personal'];//成效 个性化名称
                        } else {
                            $effectlognewarray[$key]['personal_name'] = is_null($optionOne['option_name']) ? '' : $optionOne['option_name'];//成效 个性化名称
                        }

                        $effectlognewarray[$key]['rate'] = $effectlognewarray[$key]['effect_maxvar'] == '0' ? 0 : (sprintf("%.2f", ($optionOne['option_percent'] / $effectlognewarray[$key]['effect_maxvar'])) * 100);//百分比
                    }
                }
            }

            $effectlog = $this->Show_css->selectOne("select s.studytimes_id,e.effectlog_ischecking,
                                                                e.effectlog_comment,e.effectlog_memberstar,e.effectlog_evaldimensjson,e.effectlog_bookcheck 
                from eas_classes_studytimes as s
                left join eas_classes_studytimes_effectlog as e ON s.studytimes_id = e.studytimes_id
                where s.studytimes_id = '{$studytimesOne['studytimes_id']}'");
            $effectlog['effectlog_evaldimensarray'] = json_decode($effectlog['effectlog_evaldimensjson'], true);
            if ($effectlog['studytimes_id'] > 0) {
                $this->Show_css->updateData("eas_classes_studytimes_effectlog", "studytimes_id='{$effectlog['studytimes_id']}'", array('effectlog_isconfirm' => 1, 'effectlog_updatetime' => time()));
            }


            $sql = "
           SELECT
	a.*,
	( @i := @i + 1 ) AS 'num' 
FROM
	(
	SELECT
		pp.paragraph_id,
		pp.paragraph_img,
		pp.paragraph_content,
		pp.paragraph_audiourl,
		r.readparts_sort,
		p.minibookpage_sort 
	FROM
		eas_course_times_minibookpage_readparts AS r
		LEFT JOIN app_textbook_minibookpage AS p ON r.minibookpage_id = p.minibookpage_id
		LEFT JOIN app_textbook_minibookpage_paragraph AS pp ON pp.minibookpage_id = p.minibookpage_id 
	WHERE
		r.times_id = '{$request['times_id']}' 
	ORDER BY
		r.readparts_sort ASC,
		p.minibookpage_sort ASC,
		pp.paragraph_sort ASC 
		) AS a,(
	SELECT
	@i := 0 
	) AS itable";
            $datalist = $this->Show_css->selectClear($sql);
            if ($datalist) {
                $score = '0';
                $count = '0';
                foreach ($datalist as &$val) {
                    $issub = $this->Show_css->getFieldOne("app_student_minibookpage_readparts_stuscore", "stuscore_id,stuscore_audiourl", "paragraph_id = '{$val['paragraph_id']}' and student_id = '{$request['student_id']}' and class_id = '{$request['class_id']}' and times_id = '{$request['times_id']}'");
                    if ($issub) {
                        $val['status'] = '1';
                    } else {
                        $val['status'] = '0';
                    }

                    if ($val['status'] == '1') {
                        $scoreid = $this->Show_css->getFieldOne("app_student_minibookpage_readparts_stuscore", "stuscore_id,stuscore_audiourl,stuscore_aiscore", "paragraph_id = '{$val['paragraph_id']}' and student_id = '{$request['student_id']}' and class_id = '{$request['class_id']}' and times_id = '{$request['times_id']}' order by stuscore_createtime DESC");
                        $val['stuscore_id'] = $scoreid['stuscore_id'];
                        $val['stuaudio'] = $scoreid['stuscore_audiourl'];
                        $val['score'] = $scoreid['stuscore_aiscore'];

                        $high = $this->Show_css->getFieldOne("app_student_minibookpage_readparts_stuscore", "stuscore_id,stuscore_aiscore,stuscore_audiourl", "paragraph_id = '{$val['paragraph_id']}' and student_id = '{$request['student_id']}' and teacher_id > '0' and class_id = '{$request['class_id']}'  and times_id = '{$request['times_id']}' order by stuscore_aiscore DESC");
//
                        if ($high) {
                            if ($high['stuscore_aiscore'] >= '80') {
                                $val['redstatus'] = '0';
                            } else {
                                $val['redstatus'] = '1';
                            }
                        } else {
                            $val['redstatus'] = '1';
                        }
                    } else {
                        $val['redstatus'] = '0';
                    }

                    $val['part'] = 'part' . $val['num'];
                    //红点

                    $isscore = $this->Show_css->getFieldOne("app_student_minibookpage_readparts_stuscore", "stuscore_id,stuscore_audiourl,stuscore_aiscore", "paragraph_id = '{$val['paragraph_id']}' and student_id = '{$request['student_id']}' and teacher_id > '0' and class_id = '{$request['class_id']}' and times_id = '{$request['times_id']}' order by stuscore_createtime DESC");
                    if (!$isscore) {
                        $score = '0';
                    } else {
                        $count += '1';
                        $score += $isscore['stuscore_aiscore'];
                    }

                }
            }

            $readbook['readbook_score'] = $score ? $score / $count : 0;

            $data = array();

            if ($readbook) {
                if ($readbook['readbook_score'] == '100') {
                    $readbook['star'] = '5';
                } elseif ($readbook['readbook_score'] < '100' && $readbook['readbook_score'] >= '90') {
                    $readbook['star'] = '4';
                } elseif ($readbook['readbook_score'] < '90' && $readbook['readbook_score'] >= '80') {
                    $readbook['star'] = '3';
                } elseif ($readbook['readbook_score'] < '80' && $readbook['readbook_score'] >= '70') {
                    $readbook['star'] = '2';
                } elseif ($readbook['readbook_score'] < '70' && $readbook['readbook_score'] >= '60') {
                    $readbook['star'] = '1';
                } elseif ($readbook['readbook_score'] < '60') {
                    $readbook['star'] = '0';
                }
                $data['readbook_star'] = $readbook['star'];
                $data['readbook_score'] = $readbook['readbook_score'];
            }

            $data['studentone'] = $studentOne;
            $data['tasklist'] = $taskList ? $taskList : array();
            $data['readlist'] = $readtaskList ? $readtaskList : array();
            $data['effectlog'] = $effectlog ? $effectlog : array();
            $data['chattinglist'] = $chattinglist ? $chattinglist : array();
            $data['satisfied'] = $satisfied ? $satisfied : array();
            $data['studytimesone'] = isset($datareport) ? $datareport : array();//老方式 可以删除
            $data['studytimesonenew'] = $effectlognewarray ? $effectlognewarray : array();
            $data['trackscore'] = $trackscorelist;


            $result = array();
            $result['list'] = $data;

            $this->error = 0;
            $this->errortip = '学习报告信息获取成功！';
            $this->result = $result;
            return false;
        } else {
            $this->error = 1;
            $this->errortip = '暂无学习报告信息！';
            return false;
        }

    }

    function getTaskitem($request)
    {

        $schoolOne = $this->Show_css->getFieldOne("app_school", "school_branch", "school_id='{$request['school_id']}'");

        if (!$schoolOne) {
            $this->error = 1;
            $this->errortip = '无对应学校！';
            return false;
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }

        $pagestart = ($page - 1) * $num;

        $sql = "select e.times_name as hour_name,d.course_branch,c.taskitems_title as item_title,c.taskitems_tasktype as item_tasktype,b.learnitems_diamond as taskitem_diamondprice,from_unixtime(a.learnitemslog_createtime,'%Y-%m-%d') as taskitem_finishtime
                from app_student_learnitemslog as a 
                inner join eas_classes_tasks_learnitems as b on a.learnitems_id=b.learnitems_id
                inner join app_taskitems as c on c.taskitems_id=b.taskitems_id
                inner join eas_classes as d on d.class_id=a.class_id
                inner join eas_course_times as e on e.times_id=a.times_id
                where a.student_id='{$request['student_id']}' and d.school_branch='{$schoolOne['school_branch']}'
                order by a.learnitemslog_createtime desc
                ";

        $sql .= " limit {$pagestart},{$num}";
        $hourList = $this->Show_css->selectClear($sql);
        $hourArray = array();

        if ($hourList) {
            $typeArray = array('1' => 'i 探险', '2' => 'i 挑战', '3' => '活动吧', '4' => '趣阅读', '5' => '亲子乐', '6' => '流利说');

            foreach ($hourList as $hourOne) {
                $itemOne = array();
                $itemOne['course_branch'] = $hourOne['course_branch'];
                $itemOne['hour_name'] = $hourOne['hour_name'];
                $itemOne['item_title'] = $hourOne['item_title'];
                $itemOne['item_tasktype'] = $typeArray[$hourOne['item_tasktype']];
                $itemOne['taskitem_diamondprice'] = $hourOne['taskitem_diamondprice'];
                $itemOne['taskitem_finishtime'] = $hourOne['taskitem_finishtime'];
                $hourArray[] = $itemOne;
            }
        }

        return $hourArray;
    }

    function getExamineList($request)
    {

        $schoolOne = $this->Show_css->getFieldOne("app_school", "school_branch", "school_id='{$request['school_id']}'");

        if (!$schoolOne) {
            $this->error = 1;
            $this->errortip = '无对应学校！';
            return false;
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }

        $pagestart = ($page - 1) * $num;

        $sql = "SELECT e.examine_name, e.examine_class,e.examine_correctnums, e.examine_branch,e.examine_type, e.examine_playprice, e.examine_score, e.examine_hascpk, e.examine_cpkjson, e.examine_papertime
                FROM app_examine e
                inner join eas_classes as b on b.class_id=e.class_id
                WHERE e.student_id = '{$request['student_id']}' AND e.examine_papertime <> '0' and b.school_branch='{$schoolOne['school_branch']}'
                ORDER BY e.examine_papertime DESC 
                ";

        $sql .= " limit {$pagestart},{$num}";

        $examineList = $this->Show_css->selectClear($sql);
        $examineArray = array();
        if (!$examineList) {
            $this->error = 1;
            $this->errortip = '无检测轨迹！';
            return false;
        }

        foreach ($examineList as $examineOne) {
            $itemOne = array();
            $Title = explode("-", $examineOne['examine_name']);
            $itemOne['examine_title'] = trim($Title[0]);
            $itemOne['examine_name'] = $examineOne['examine_name'];
            $itemOne['examine_class'] = $examineOne['examine_class'];
            $itemOne['examine_type'] = $examineOne['examine_type'];
            $itemOne['examine_playprice'] = $examineOne['examine_playprice'];
            $itemOne['examine_score'] = $examineOne['examine_score'];
            $itemOne['examine_hascpk'] = $examineOne['examine_hascpk'];

            if (json_decode($examineOne['examine_cpkjson'], 1)) {
                $itemOne['examine_cpkjson'] = json_decode($examineOne['examine_cpkjson'], 1);
            } else {
                $cpk_sql = "SELECT d.ability_id, f.ability_name, count(d.ability_id) AS sum
            FROM app_examine AS b
            LEFT JOIN app_examine_question AS c ON c.examine_id = b.examine_id
            LEFT JOIN app_testing_question AS d ON d.question_id = c.question_id
            LEFT JOIN app_testing_ability AS f ON f.ability_id = d.ability_id
            WHERE
                b.examine_branch = '{$examineOne['examine_branch']}' and c.is_right=1
            GROUP BY
                d.ability_id";

                $rel_cpk_sql = "SELECT
                d.ability_id,
                f.ability_name,
                count(d.ability_id) AS sum
            FROM
                app_examine AS b
            LEFT JOIN app_examine_question AS c ON c.examine_id = b.examine_id
            LEFT JOIN app_testing_question AS d ON d.question_id = c.question_id
            LEFT JOIN app_testing_ability AS f ON f.ability_id = d.ability_id
            WHERE
                b.examine_branch = '{$examineOne['examine_branch']}'
            GROUP BY
                d.ability_id";
                $rel_data = $this->Show_css->selectClear($rel_cpk_sql); //总数
                $exam_data = $this->Show_css->selectClear($cpk_sql);  //正确的数量
                if (!$exam_data) {
                    $exam_data = array();
                }
                $cpkjson = array();
                foreach ($rel_data as $key => $val) {
                    if (isset($val['ability_id'])) {
                        $cpkjson[$key]['ability_id'] = $val['ability_id'];
                        $cpkjson[$key]['ability_name'] = $val['ability_name'];
                        $cpkjson[$key]['total'] = $examineOne['examine_correctnums'] > 0 ? $val['sum'] * (100 / $examineOne['examine_correctnums']) : 0;
                        if (is_array($exam_data[$key])) {
                            if (in_array($val['ability_id'], $exam_data[$key])) {
                                $cpkjson[$key]['right'] = $examineOne['examine_correctnums'] > 0 ? $exam_data[$key]['sum'] * (100 / $examineOne['examine_correctnums']) : 0;
                            } else {
                                $cpkjson[$key]['right'] = 0;
                            }
                        } else {
                            $cpkjson[$key]['right'] = 0;
                        }
                    }
                }

                $itemOne['examine_cpkjson'] = $cpkjson;
            }
            $itemOne['examine_papertime'] = date("Y-m-d", $examineOne['examine_papertime']);
            $examineArray[] = $itemOne;
        }

        return $examineArray;
    }

    function addStudentIntegral($school_id, $student_id, $integral = 0, $integrallog_rule, $playname = '', $note = '', $time = '', $class_id, $nature)
    {
        if (!$time || $time == '') {
            $time = time();
        }

        $stuIntOne = $this->Show_css->getFieldOne("sgr_student_integral", "integral_price", "student_id='{$student_id}'");
        $stuIntOne['integral_price'] = $stuIntOne['integral_price'] ? $stuIntOne['integral_price'] : 0;

        //积分余额
        if ($this->Show_css->getFieldOne("sgr_student_integral", "integral_price", "student_id='{$student_id}'")) {
            $data = array();
            $data['integral_price'] = $stuIntOne['integral_price'] + $integral;
            $this->Show_css->updateData("sgr_student_integral", "student_id='{$student_id}'", $data);
        } else {
            $data = array();
            $data['student_id'] = $student_id;
            $data['integral_price'] = $integral;
            $data['integral_nature'] = $nature;
            $this->Show_css->insertData("sgr_student_integral", $data);
        }
        $integrallog_data = array();
        $integrallog_data['student_id'] = $student_id;
        $integrallog_data['school_id'] = $school_id;
        $integrallog_data['class_id'] = $class_id;

        $integrallog_data['integrallog_playname'] = $playname ? $playname : '积分增加';
        $integrallog_data['integrallog_playclass'] = '+';
        $integrallog_data['integrallog_fromamount'] = $stuIntOne['integral_price'];
        $integrallog_data['integrallog_playamount'] = $integral;
        $integrallog_data['integrallog_finalamount'] = $stuIntOne['integral_price'] + $integral;

        $integrallog_data['integrallog_remark'] = $note;
        $integrallog_data['integrallog_reason'] = $integrallog_rule;
        $integrallog_data['integrallog_createtime'] = $time;
        $this->Show_css->insertData("sgr_student_integrallog", $integrallog_data);

        return true;
    }

    function reduceStudentIntegral($school_id, $student_id, $integral = 0, $integrallog_rule, $playname = '', $note = '', $time = '', $class_id, $nature)
    {
        if (!$time || $time == '') {
            $time = time();
        }

        $stuIntOne = $this->Show_css->getFieldOne("sgr_student_integral", "integral_price", "student_id='{$student_id}'");
        $stuIntOne['integral_price'] = $stuIntOne['integral_price'] ? $stuIntOne['integral_price'] : 0;

        //积分余额
        if ($stuIntOne['integral_price'] < $integral || !$stuIntOne) {
            $this->error = true;
            $this->errortip = "积分不足";
            return false;
        }

        $data = array();
        $data['integral_price'] = $stuIntOne['integral_price'] - $integral;
        $this->Show_css->updateData("sgr_student_integral", "student_id='{$student_id}'", $data);

        $integrallog_data = array();
        $integrallog_data['student_id'] = $student_id;
        $integrallog_data['school_id'] = $school_id;
        $integrallog_data['class_id'] = $class_id;

        $integrallog_data['integrallog_playname'] = $playname ? $playname : '积分减少';
        $integrallog_data['integrallog_playclass'] = '-';
        $integrallog_data['integrallog_fromamount'] = $stuIntOne['integral_price'];
        $integrallog_data['integrallog_playamount'] = $integral;
        $integrallog_data['integrallog_finalamount'] = $stuIntOne['integral_price'] - $integral;

        $integrallog_data['integrallog_remark'] = $note;
        $integrallog_data['integrallog_reason'] = $integrallog_rule;
        $integrallog_data['integrallog_createtime'] = $time;
        $rid = $this->Show_css->insertData("sgr_student_integrallog", $integrallog_data);

        return $rid;
    }

    function getMapsstepsLog($request)
    {
        $schoolOne = $this->Show_css->getFieldOne("app_school", "school_branch", "school_id='{$request['school_id']}'");

        if (!$schoolOne) {
            $this->error = 1;
            $this->errortip = '无对应学校！';
            return false;
        }

        if (isset($request['p']) && $request['p'] !== '') {
            $page = $request['p'];
        } else {
            $page = '1';
        }
        if (isset($request['num']) && $request['num'] !== '') {
            $num = $request['num'];
        } else {
            $num = '10';
        }

        $pagestart = ($page - 1) * $num;

        $sql = "SELECT m.maps_title, s.steps_title, l.log_class, l.log_issucceed, log_playprice , log_createtime 
                FROM app_maps_steps_log l, app_maps_steps s, app_maps m,eas_classes as b
                WHERE (s.steps_id = l.steps_id AND m.maps_id = s.maps_id and l.class_id=b.class_id AND l.student_id = '{$request['student_id']}' AND l.log_issucceed <> '0') and b.school_branch='{$schoolOne['school_branch']}' 
                ORDER BY l.log_createtime DESC";

        $sql .= " limit {$pagestart},{$num}";

        $stepslogList = $this->Show_css->selectClear($sql);

        $stepslogArray = array();

        if (!$stepslogList) {
            $this->error = 1;
            $this->errortip = '无闯关轨迹！';
            return false;
        }

        foreach ($stepslogList as $stepslogOne) {
            $itemOne = array();
            $itemOne['maps_title'] = $stepslogOne['maps_title'];
            $itemOne['steps_title'] = $stepslogOne['steps_title'];
            $itemOne['log_class'] = $stepslogOne['log_class'];
            $itemOne['log_issucceed'] = $stepslogOne['log_issucceed'];
            $itemOne['log_playprice'] = $stepslogOne['log_playprice'];
            $itemOne['log_createtime'] = date("m-d", $stepslogOne['log_createtime']);
            $itemOne['log_createhour'] = date("H i”", $stepslogOne['log_createtime']);
            $stepslogArray[] = $itemOne;
        }

        return $stepslogArray;
    }

    //家校通 服务部分 奇趣任务改版接口
    //任务进度
    function getTaskSituation($request)
    {
        if ($request['school_nature'] == '2') {
            //学生信息
            $studentOne = $this->Show_css->selectOne(" select s.student_id,s.student_cnname,s.student_enname,s.student_img,s.student_sex,
                b.class_id,b.class_cnname,b.course_branch 
                from app_student as s,app_student_study AS a, eas_classes AS b, app_school AS c 
                where s.student_id = '{$request['student_id']}' 
                and s.student_id = a.student_id and a.study_islearning = '1' 
                and a.school_id = c.school_id and c.school_branch = '{$request['school_branch']}' 
                and a.class_id = b.class_id  
                order by a.study_endday desc ");
            $request['class_id'] = $studentOne['class_id'];
        }

        $sql = "SELECT * FROM (
                    SELECT b.class_id,f.class_cnname,b.times_id,g.times_name,g.times_sort,count(d.taskitems_id) as taskNum,count(h.learnitemslog_id) as sucNum  
                    FROM
                     eas_classes_tasks_learnitems AS a
                     INNER JOIN eas_classes_tasks AS b ON a.classtasks_id = b.classtasks_id
                     INNER JOIN eas_classes_studytimes AS c ON c.times_id = b.times_id  AND c.class_id = b.class_id  AND c.studytimes_status > 0
                     INNER JOIN app_taskitems AS d ON d.taskitems_id = a.taskitems_id
                     INNER JOIN eas_classes AS f ON f.class_id = b.class_id
                     LEFT JOIN eas_classes_study_notextbook AS e ON e.class_id = c.class_id  AND e.student_id = c.student_id  AND e.textbook_id = d.textbook_id 
                     left join eas_course_times as g ON g.times_id = b.times_id 
                     left join app_student_learnitemslog as h ON h.learnitems_id = a.learnitems_id and h.student_id = c.student_id 
                    WHERE 
                     c.student_id = '{$request['student_id']}' and b.class_id = '{$request['class_id']}' 
                    and  
                     ( d.textbook_id = 0 OR e.notextbook_id IS NULL )  AND b.classtasks_tasksviewtimes <= unix_timestamp(now()) 
                     AND ( d.taskitems_week = '' 
                        OR ( from_unixtime(b.classtasks_tasksviewtimes,'%Y-%m-%d')<(DATE_SUB( CURDATE(), INTERVAL WEEKDAY( CURDATE()) + 0 DAY)) 
                        OR d.taskitems_week <=(WEEKDAY(CURDATE())+ 1))
                        ) 
                     AND EXISTS (
                     SELECT dd.fucmodule_id 
                     FROM pro_carditem AS aa
                        INNER JOIN pro_sales_channel AS bb ON aa.channel_id = bb.channel_id
                        INNER JOIN pro_sales_fucmodule AS dd ON dd.plan_id = bb.plan_id
                        INNER JOIN pro_product_fucmodule AS ee ON ee.fucmodule_id = dd.fucmodule_id
                        INNER JOIN app_student AS ff ON ff.student_branch = aa.student_branch 
                     WHERE aa.course_branch = f.course_branch 
                        AND ff.student_id = c.student_id 
                        AND from_unixtime( aa.carditem_invalidtime, '%Y-%m-%d' )>= CURDATE() 
                        AND ( bb.channel_finaltime = 0 OR from_unixtime( bb.channel_finaltime, '%Y-%m-%d' )>= CURDATE() ) 
                        AND ee.fucmodule_branch = 'Taskstu' 
                     ) 
                     GROUP BY b.times_id 
                ) as z  ORDER BY z.times_sort ASC ";
        $timesList = $this->Show_css->selectClear($sql);

        if ($timesList) {
            foreach ($timesList as &$timesVar) {
                $timesVar['issuccess'] = ($timesVar['taskNum'] - $timesVar['sucNum']) > 0 ? 0 : 1;
                $timesVar['surplusnum'] = $timesVar['taskNum'] - $timesVar['sucNum'];
            }
        }

        $result = array();
        $result['list'] = $timesList;
        if ($timesList) {
            $this->error = 0;
            $this->errortip = "任务周次信息获取成功！";
            $this->result = $result;
            return true;
        } else {
            $this->error = 1;
            $this->errortip = "暂时还没有上任务周次！";
            $this->result = array();
            return false;
        }
    }

    //课次任务
    function getHourTask($request)
    {
        $timesOne = $this->Show_css->selectOne("select b.course_mold from eas_classes a,eas_course b where a.course_branch=b.course_branch and a.class_id='{$request['class_id']}'");
        $sql = "select c.taskitems_title,d.list_name,c.taskitems_week,c.taskitems_isbook
                from eas_classes_tasks_learnitems as a
                inner join eas_classes_tasks as b on a.classtasks_id=b.classtasks_id
                inner join app_taskitems as c on a.taskitems_id=c.taskitems_id 
                left join cms_variablelist as d on d.list_parameter = c.taskitems_tasktype and d.variable_id=9
                where b.class_id='{$request['class_id']}' and b.times_id='{$request['times_id']}'
                ORDER BY c.taskitems_sort ASC";
        $learntasksList = $this->Show_css->selectClear($sql);
        $taskList = array();
        if ($learntasksList) {
            if ($timesOne['course_mold'] == 1) {
                $tem_array = array();
                foreach ($learntasksList as $one) {
                    if ($one['taskitems_isbook']) {
                        $one['taskitems_title'] = $one['taskitems_title'] . '（预习）';
                    }
                    $tem_array[$one['taskitems_week']]['week'] = self::$WORK_DAY[$one['taskitems_week']]['cn'];
                    $tem_array[$one['taskitems_week']]['taskList'][] = $one;
                }
                //sort($tem_array);
                uksort($tem_array, function($a, $b) {
                    return strcmp($a, $b);
                });
                foreach ($tem_array as $temOne) {
                    $taskList[] = $temOne;
                }
            } else {
                foreach ($learntasksList as &$one) {
                    if ($one['taskitems_isbook']) {
                        $one['taskitems_title'] = $one['taskitems_title'] . '（预习）';
                    }
                }
                $taskList = $learntasksList;
            }
        }

        $result = array();
        $result['list'] = $taskList;
        if ($taskList) {
            $this->error = 0;
            $this->errortip = "课次任务信息获取成功！";
            $this->result = $result;
            return true;
        } else {
            $this->error = 1;
            $this->errortip = "暂时还没有课次任务！";
            $this->result = array();
            return false;
        }
    }

    //任务课次分类
    function getTaskTimeMode($request)
    {
        if ($request['school_nature'] == '1') {
            $sql = "SELECT d.taskitems_modetype,j.modetype_name,count(d.taskitems_id) as taskNum,count(h.learnitemslog_id) as sucNum  
                FROM
                 eas_classes_tasks_learnitems AS a
                 INNER JOIN eas_classes_tasks AS b ON a.classtasks_id = b.classtasks_id
                 INNER JOIN eas_classes_studytimes AS c ON c.times_id = b.times_id  AND c.class_id = b.class_id  AND c.studytimes_status > 0
                 INNER JOIN app_taskitems AS d ON d.taskitems_id = a.taskitems_id
                 INNER JOIN eas_classes AS f ON f.class_id = b.class_id
                 LEFT JOIN eas_classes_study_notextbook AS e ON e.class_id = c.class_id  AND e.student_id = c.student_id  AND e.textbook_id = d.textbook_id  
	             LEFT JOIN app_student_learnitemslog as h ON h.learnitems_id = a.learnitems_id and h.student_id = c.student_id and h.times_id = b.times_id 
                 INNER JOIN app_taskitems_modetype as j ON d.taskitems_modetype = j.modetype_id 
                WHERE 
                 c.student_id = '{$request['student_id']}' and b.class_id = '{$request['class_id']}' and b.times_id = '{$request['times_id']}'
                and  ( d.textbook_id = 0 OR e.notextbook_id IS NULL )  AND b.classtasks_tasksviewtimes <= unix_timestamp(now()) 
                 AND ( d.taskitems_week = '' 
                    OR ( from_unixtime(b.classtasks_tasksviewtimes,'%Y-%m-%d')<(DATE_SUB( CURDATE(), INTERVAL WEEKDAY( CURDATE()) + 0 DAY)) 
                    OR d.taskitems_week <=(WEEKDAY(CURDATE())+ 1)) ) 
                 AND EXISTS (
                 SELECT dd.fucmodule_id 
                 FROM pro_carditem AS aa
                    INNER JOIN pro_sales_channel AS bb ON aa.channel_id = bb.channel_id
                    INNER JOIN pro_sales_fucmodule AS dd ON dd.plan_id = bb.plan_id
                    INNER JOIN pro_product_fucmodule AS ee ON ee.fucmodule_id = dd.fucmodule_id
                    INNER JOIN app_student AS ff ON ff.student_branch = aa.student_branch 
                 WHERE aa.course_branch = f.course_branch 
                    AND ff.student_id = c.student_id 
                    AND from_unixtime( aa.carditem_invalidtime, '%Y-%m-%d' )>= CURDATE() 
                    AND ( bb.channel_finaltime = 0 OR from_unixtime( bb.channel_finaltime, '%Y-%m-%d' )>= CURDATE() ) 
                    AND ee.fucmodule_branch = 'Taskstu' 
                 ) 
                GROUP BY d.taskitems_modetype";
        } else {
            $sql = "SELECT d.taskitems_modetype,d.taskitems_week,count(d.taskitems_id) as taskNum,count(h.learnitemslog_id) as sucNum,
                    (select w.weektype_name from app_taskitems_weektype as w where w.weektype_status = 1 and w.weektype_value = d.taskitems_week limit 1) as modetype_name 
                FROM
                 eas_classes_tasks_learnitems AS a
                 INNER JOIN eas_classes_tasks AS b ON a.classtasks_id = b.classtasks_id
                 INNER JOIN eas_classes_studytimes AS c ON c.times_id = b.times_id  AND c.class_id = b.class_id  AND c.studytimes_status > 0
                 INNER JOIN app_taskitems AS d ON d.taskitems_id = a.taskitems_id
                 INNER JOIN eas_classes AS f ON f.class_id = b.class_id
                 LEFT JOIN eas_classes_study_notextbook AS e ON e.class_id = c.class_id  AND e.student_id = c.student_id  AND e.textbook_id = d.textbook_id  
	             LEFT JOIN app_student_learnitemslog as h ON h.learnitems_id = a.learnitems_id and h.student_id = c.student_id and h.times_id = b.times_id 
                WHERE 
                 c.student_id = '{$request['student_id']}' and b.class_id = '{$request['class_id']}' and b.times_id = '{$request['times_id']}'
                and  ( d.textbook_id = 0 OR e.notextbook_id IS NULL )  AND b.classtasks_tasksviewtimes <= unix_timestamp(now()) 
                 AND ( d.taskitems_week = '' 
                    OR ( from_unixtime(b.classtasks_tasksviewtimes,'%Y-%m-%d')<(DATE_SUB( CURDATE(), INTERVAL WEEKDAY( CURDATE()) + 0 DAY)) 
                    OR d.taskitems_week <=(WEEKDAY(CURDATE())+ 1)) ) 
                 AND EXISTS (
                 SELECT dd.fucmodule_id 
                 FROM pro_carditem AS aa
                    INNER JOIN pro_sales_channel AS bb ON aa.channel_id = bb.channel_id
                    INNER JOIN pro_sales_fucmodule AS dd ON dd.plan_id = bb.plan_id
                    INNER JOIN pro_product_fucmodule AS ee ON ee.fucmodule_id = dd.fucmodule_id
                    INNER JOIN app_student AS ff ON ff.student_branch = aa.student_branch 
                 WHERE aa.course_branch = f.course_branch 
                    AND ff.student_id = c.student_id 
                    AND from_unixtime( aa.carditem_invalidtime, '%Y-%m-%d' )>= CURDATE() 
                    AND ( bb.channel_finaltime = 0 OR from_unixtime( bb.channel_finaltime, '%Y-%m-%d' )>= CURDATE() ) 
                    AND ee.fucmodule_branch = 'Taskstu' 
                 ) 
                GROUP BY d.taskitems_week";
        }
        $timesList = $this->Show_css->selectClear($sql);

        if ($timesList) {
            foreach ($timesList as &$timesVar) {
                $timesVar['issuccess'] = ($timesVar['taskNum'] - $timesVar['sucNum']) > 0 ? 0 : 1;
                $timesVar['surplusnum'] = $timesVar['taskNum'] - $timesVar['sucNum'];
            }
        }

        $result = array();
        $result['list'] = $timesList;
        if ($timesList) {
            $this->error = 0;
            $this->errortip = "周次任务分类获取成功！";
            $this->result = $result;
            return true;
        } else {
            $this->error = 1;
            $this->errortip = "暂时还没有周次任务分类！";
            $this->result = array();
            return false;
        }
    }

    //任务课次分类下详细任务
    function getTaskTimeModeList($request)
    {
        if ($request['school_nature'] == '1') {
            $sql = "SELECT d.taskitems_modetype,d.taskitems_id,d.taskitems_title,if(ISNULL(h.learnitemslog_id),0,1) as learnitemslogid,d.taskitems_tasktype,d.taskitems_content
                FROM
                 eas_classes_tasks_learnitems AS a
                 INNER JOIN eas_classes_tasks AS b ON a.classtasks_id = b.classtasks_id
                 INNER JOIN eas_classes_studytimes AS c ON c.times_id = b.times_id  AND c.class_id = b.class_id  AND c.studytimes_status > 0
                 INNER JOIN app_taskitems AS d ON d.taskitems_id = a.taskitems_id
                 INNER JOIN eas_classes AS f ON f.class_id = b.class_id
                 LEFT JOIN eas_classes_study_notextbook AS e ON e.class_id = c.class_id  AND e.student_id = c.student_id  AND e.textbook_id = d.textbook_id  
                 LEFT JOIN app_student_learnitemslog as h ON h.learnitems_id = a.learnitems_id and h.student_id = c.student_id and h.times_id = b.times_id  
                 INNER JOIN app_taskitems_modetype as j ON d.taskitems_modetype = j.modetype_id 
                WHERE 
                 c.student_id = '{$request['student_id']}' and b.class_id = '{$request['class_id']}' 
                 and b.times_id = '{$request['times_id']}' and d.taskitems_modetype = '{$request['taskitems_modetype']}' 
                 and  ( d.textbook_id = 0 OR e.notextbook_id IS NULL )  AND b.classtasks_tasksviewtimes <= unix_timestamp(now()) 
                 AND ( d.taskitems_week = '' 
                    OR ( from_unixtime(b.classtasks_tasksviewtimes,'%Y-%m-%d')<(DATE_SUB( CURDATE(), INTERVAL WEEKDAY( CURDATE()) + 0 DAY)) 
                    OR d.taskitems_week <=(WEEKDAY(CURDATE())+ 1)) ) 
                 AND EXISTS (
                 SELECT dd.fucmodule_id 
                 FROM pro_carditem AS aa
                    INNER JOIN pro_sales_channel AS bb ON aa.channel_id = bb.channel_id
                    INNER JOIN pro_sales_fucmodule AS dd ON dd.plan_id = bb.plan_id
                    INNER JOIN pro_product_fucmodule AS ee ON ee.fucmodule_id = dd.fucmodule_id
                    INNER JOIN app_student AS ff ON ff.student_branch = aa.student_branch 
                 WHERE aa.course_branch = f.course_branch 
                    AND ff.student_id = c.student_id 
                    AND from_unixtime( aa.carditem_invalidtime, '%Y-%m-%d' )>= CURDATE() 
                    AND ( bb.channel_finaltime = 0 OR from_unixtime( bb.channel_finaltime, '%Y-%m-%d' )>= CURDATE() ) 
                    AND ee.fucmodule_branch = 'Taskstu' 
                 )";
        } else {
            $sql = "SELECT d.taskitems_modetype,d.taskitems_id,d.taskitems_title,if(ISNULL(h.learnitemslog_id),0,1) as learnitemslogid,d.taskitems_tasktype,d.taskitems_content
                FROM
                 eas_classes_tasks_learnitems AS a
                 INNER JOIN eas_classes_tasks AS b ON a.classtasks_id = b.classtasks_id
                 INNER JOIN eas_classes_studytimes AS c ON c.times_id = b.times_id  AND c.class_id = b.class_id  AND c.studytimes_status > 0
                 INNER JOIN app_taskitems AS d ON d.taskitems_id = a.taskitems_id
                 INNER JOIN eas_classes AS f ON f.class_id = b.class_id
                 LEFT JOIN eas_classes_study_notextbook AS e ON e.class_id = c.class_id  AND e.student_id = c.student_id  AND e.textbook_id = d.textbook_id  
                 LEFT JOIN app_student_learnitemslog as h ON h.learnitems_id = a.learnitems_id and h.student_id = c.student_id and h.times_id = b.times_id  
                WHERE 
                 c.student_id = '{$request['student_id']}' and b.class_id = '{$request['class_id']}' 
                 and b.times_id = '{$request['times_id']}' and d.taskitems_week = '{$request['taskitems_week']}' 
                 and  ( d.textbook_id = 0 OR e.notextbook_id IS NULL )  AND b.classtasks_tasksviewtimes <= unix_timestamp(now()) 
                 AND ( d.taskitems_week = '' 
                    OR ( from_unixtime(b.classtasks_tasksviewtimes,'%Y-%m-%d')<(DATE_SUB( CURDATE(), INTERVAL WEEKDAY( CURDATE()) + 0 DAY)) 
                    OR d.taskitems_week <=(WEEKDAY(CURDATE())+ 1)) ) 
                 AND EXISTS (
                 SELECT dd.fucmodule_id 
                 FROM pro_carditem AS aa
                    INNER JOIN pro_sales_channel AS bb ON aa.channel_id = bb.channel_id
                    INNER JOIN pro_sales_fucmodule AS dd ON dd.plan_id = bb.plan_id
                    INNER JOIN pro_product_fucmodule AS ee ON ee.fucmodule_id = dd.fucmodule_id
                    INNER JOIN app_student AS ff ON ff.student_branch = aa.student_branch 
                 WHERE aa.course_branch = f.course_branch 
                    AND ff.student_id = c.student_id 
                    AND from_unixtime( aa.carditem_invalidtime, '%Y-%m-%d' )>= CURDATE() 
                    AND ( bb.channel_finaltime = 0 OR from_unixtime( bb.channel_finaltime, '%Y-%m-%d' )>= CURDATE() ) 
                    AND ee.fucmodule_branch = 'Taskstu' 
                 )";
        }
        $timesList = $this->Show_css->selectClear($sql);

        if ($timesList) {
            foreach ($timesList as &$timesVar) {
                $timesVar['issuccess'] = $timesVar['learnitemslogid'];
                if ($timesVar['taskitems_tasktype'] == '3') {
                    $timesVar['taskitems_title'] = $timesVar['taskitems_content'];
                }
            }
        }

        $result = array();
        $result['list'] = $timesList;
        if ($timesList) {
            $this->error = 0;
            $this->errortip = "周次任务分类详情获取成功！";
            $this->result = $result;
            return true;
        } else {
            $this->error = 1;
            $this->errortip = "暂时还没有周次任务分类详情！";
            $this->result = array();
            return false;
        }
    }

    function effectCateList($request)
    {
        if ($request['type'] == '1') {
            $sort = '5';
        } elseif ($request['type'] == '2') {
            $sort = '10';
        } elseif ($request['type'] == '3') {
            $sort = '15';
        } elseif ($request['type'] == '4') {
            $sort = '20';
        }
        $day = date("Ymd");


        $theme = $this->Show_css->selectOne("SELECT
	st.studytimes_id,
	t.times_id,
	t.times_sort 
FROM
	eas_classes_studytimes AS st
	LEFT JOIN eas_course_times AS t ON t.times_id = st.times_id 
    left join app_student_study as sd on sd.student_id = st.student_id and sd.class_id = st.class_id
WHERE
	st.student_id = '{$request['student_id']}' 
	and t.times_sort = '{$sort}' 
	and sd.study_islearning = '1' and  sd.study_beginday <= '{$day}' and sd.study_endday >= '{$day}'
GROUP BY st.studytimes_id");

        $list = $this->Show_css->selectClear("select e.effect_cate from eas_classes_studytimes_effectlogs_comments as c left join eas_course_effect as e on e.effect_id = c.effect_id left join eas_course_times_project as p on p.project_name = e.effect_cate and p.times_id = '{$theme['times_id']}' where c.studytimes_id = '{$theme['studytimes_id']}' and c.comments_isnot = '0' group by e.effect_cate order by p.project_sort ASC");
        $data['list'] = $list;
        return $data;
    }

    //获取配音作品
    function getDubbingWorksApi($paramArray)
    {
        $datawhere = "w.student_id = '{$paramArray['student_id']}' and w.work_status = '1'";
        if (!empty($paramArray['keyword'])) {
            $datawhere .= " and (s.student_cnname like '%{$paramArray['keyword']}%' or s.student_enname like '%{$paramArray['keyword']}%' or s.student_branch like '%{$paramArray['keyword']}%')";
        }
        if (!empty($paramArray['starttime'])) {
            $start_time = strtotime($paramArray['starttime']);
            $datawhere .= "  and w.work_createtime >= '{$start_time}'";
        }
        if (!empty($paramArray['endtime'])) {
            $endtime = strtotime($paramArray['endtime'] . ' 23:59:59');
            $datawhere .= "  and w.work_createtime <= '{$endtime}'";
        }

        $having = "1=1";
        if (isset($paramArray['comment_status']) && $paramArray['comment_status'] !== '') {
            if ($paramArray['comment_status']) {
                $having .= " and is_comment > 0";
            } else {
                $having .= " and is_comment = 0";
            }
        }
        if (!empty($paramArray['work_type'])) {
            $having .= " and work_type = '{$paramArray['work_type']}'";
        }

        if (!empty($paramArray['p'])) {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (!empty($paramArray['num'])) {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;

        $sql = "SELECT  s.student_id,s.student_cnname,s.student_enname,s.student_branch,s.student_sex,s.student_img,'1' as work_type,
                        w.work_id,ar.audiorecords_name as work_name,w.work_score,FROM_UNIXTIME(w.work_createtime,'%Y-%m-%d %H:%i') as work_time,
                        IFNULL((select COUNT(dr.reviewlog_id) from app_dubbingworks_reviewlog as dr where dr.work_id = w.work_id and dr.work_type = '1' and dr.teacher_id > 0), 0) as is_comment,count(aw.workpages_id) as work_thisnum,
                        (select count(aw.workpages_id) from app_audiorecords_work as wk,app_audiorecords_workpages as aw where wk.work_id = aw.work_id and wk.class_id = w.class_id and wk.student_id = w.student_id) as work_allnum,
                        (select count(wk.work_id) from app_audiorecords_work as wk where wk.class_id = w.class_id and wk.student_id = w.student_id) as work_dubnum,ec.class_cnname,tb.textbook_coverminimg as work_img,w.work_type as work_class,'0' as week_id,'0' as textbook_id,ar.audiorecords_type,ar.audiorecords_url,ar.audiorecords_id
                FROM app_audiorecords_workpages as aw
                LEFT JOIN app_audiorecords_work as w ON w.work_id = aw.work_id
                LEFT JOIN app_audiorecords as ar ON ar.audiorecords_id = aw.audiorecords_id
                LEFT JOIN app_student AS s ON s.student_id = w.student_id
                LEFT JOIN eas_classes as ec on ec.class_id = w.class_id
                LEFT JOIN app_textbook AS tb ON tb.textbook_id = ar.textbook_id
                WHERE {$datawhere}
                GROUP BY w.work_id
                HAVING {$having}
                UNION ALL ";

        if ($paramArray['school_nature'] == '2') {
            $sql .= "SELECT s.student_id,s.student_cnname,s.student_enname,s.student_branch,s.student_sex,s.student_img,'3' as work_type,
                        w.work_id,ca.textbookcate_name as work_name,w.work_score,FROM_UNIXTIME(w.work_createtime, '%Y-%m-%d %H:%i:%s') AS work_time,
                        IFNULL((select COUNT(dr.reviewlog_id) from app_dubbingworks_reviewlog as dr where dr.work_id = w.work_id and dr.work_type = '3' and dr.teacher_id > 0), 0) as is_comment,count(e.workpages_id) as work_thisnum,
                        (select count(wp.workpages_id) from app_ekidbook_work as ew,app_ekidbook_workpages as wp where ew.work_id = wp.work_id and ew.class_id = w.class_id and ew.student_id = w.student_id) as work_allnum,
                        (select count(ew.work_id) from app_ekidbook_work as ew where ew.class_id = w.class_id and ew.student_id = w.student_id) as work_dubnum,ec.class_cnname,ca.textbookcate_coverimg as work_img,w.work_type as work_class,es.week_id,es.textbook_id,'0' as audiorecords_type,'' as audiorecords_url,'0' as audiorecords_id
                    FROM app_ekidbook_work AS w
                    LEFT JOIN app_ekidbook_workpages AS e ON e.work_id = w.work_id
                    LEFT JOIN app_ekidbook_steps AS es ON es.steps_id = w.steps_id
                    LEFT JOIN app_ekidbook_textbook AS te ON te.textbook_id = es.textbook_id
                    LEFT JOIN app_student AS s ON s.student_id = w.student_id
                    LEFT JOIN app_ekidbook_textbookcate AS ca ON ca.textbookcate_id = te.textbookcate_id
                    LEFT JOIN eas_classes as ec on ec.class_id = w.class_id
                    WHERE {$datawhere}
                    GROUP BY w.work_id
                    HAVING {$having}
                    ORDER BY work_time DESC
                    LIMIT {$pagestart},{$num}";
        } else {
            $sql .= "SELECT s.student_id,s.student_cnname,s.student_enname,s.student_branch,s.student_sex,s.student_img,'2' as work_type,
                            w.work_id,pb.picbooks_title as work_name,w.work_score,FROM_UNIXTIME(w.work_createtime,'%Y-%m-%d %H:%i') as work_time,
                            IFNULL((select COUNT(dr.reviewlog_id) from app_dubbingworks_reviewlog as dr where dr.work_id = w.work_id and dr.work_type = '2' and dr.teacher_id > 0), 0) as is_comment,count(pw.workpages_id) as work_thisnum,
                            (select count(pw.workpages_id) from app_picbooks_work as wk,app_picbooks_workpages as pw where wk.work_id = pw.work_id and wk.class_id = w.class_id and wk.student_id = w.student_id) as work_allnum,
                            (select count(wk.work_id) from app_picbooks_work as wk where wk.class_id = w.class_id and wk.student_id = w.student_id) as work_dubnum,ec.class_cnname,pb.picbooks_imgurl as work_img,w.work_type as work_class,'0' as week_id,'0' as textbook_id,'0' as audiorecords_type,'' as audiorecords_url,'0' as audiorecords_id
                    FROM app_picbooks_work as w
                    LEFT JOIN app_picbooks_workpages AS pw ON pw.work_id = w.work_id
                    LEFT JOIN app_picbooks as pb ON pb.picbooks_id = w.picbooks_id
                    LEFT JOIN app_student AS s ON s.student_id = w.student_id
                    LEFT JOIN eas_classes as ec on ec.class_id = w.class_id
                    WHERE {$datawhere}
                    GROUP BY w.work_id
                    HAVING {$having} UNION ALL 
                    SELECT s.student_id,s.student_cnname,s.student_enname,s.student_branch,s.student_sex,s.student_img,'3' as work_type,
                        w.work_id,ca.textbookcate_name as work_name,w.work_score,FROM_UNIXTIME(w.work_createtime, '%Y-%m-%d %H:%i:%s') AS work_time,
                        IFNULL((select COUNT(dr.reviewlog_id) from app_dubbingworks_reviewlog as dr where dr.work_id = w.work_id and dr.work_type = '3' and dr.teacher_id > 0), 0) as is_comment,count(e.workpages_id) as work_thisnum,
                        (select count(wp.workpages_id) from app_ekidbook_work as ew,app_ekidbook_workpages as wp where ew.work_id = wp.work_id and ew.class_id = w.class_id and ew.student_id = w.student_id) as work_allnum,
                        (select count(ew.work_id) from app_ekidbook_work as ew where ew.class_id = w.class_id and ew.student_id = w.student_id) as work_dubnum,ec.class_cnname,ca.textbookcate_coverimg as work_img,w.work_type as work_class,es.week_id,es.textbook_id,'0' as audiorecords_type,'' as audiorecords_url,'0' as audiorecords_id
                    FROM app_ekidbook_work AS w
                    LEFT JOIN app_ekidbook_workpages AS e ON e.work_id = w.work_id
                    LEFT JOIN app_ekidbook_steps AS es ON es.steps_id = w.steps_id
                    LEFT JOIN app_ekidbook_textbook AS te ON te.textbook_id = es.textbook_id
                    LEFT JOIN app_student AS s ON s.student_id = w.student_id
                    LEFT JOIN app_ekidbook_textbookcate AS ca ON ca.textbookcate_id = te.textbookcate_id
                    LEFT JOIN eas_classes as ec on ec.class_id = w.class_id
                    WHERE {$datawhere}
                    GROUP BY w.work_id
                    HAVING {$having}
                    ORDER BY work_time DESC
                    LIMIT {$pagestart},{$num}";
        }
        $dataList = $this->Show_css->selectClear($sql);

        if ($dataList) {
            foreach ($dataList as &$val) {
                if ($val['work_type'] == '1') {
                    $audioList = $this->Show_css->selectClear("select w.workpages_audio,r.resource_imgurl as work_imgurl from app_audiorecords_workpages as w,app_audiorecords_resource as r where w.resource_id = r.resource_id and w.work_id = '{$val['work_id']}'");
                    $thumb = $this->Show_css->selectOne("select thumb_num,dynamic_id from ptc_dynamic_audio where awork_id = '{$val['work_id']}'");
                    $val['thumb'] = $thumb['thumb_num'];
                    $val['dynamic_id'] = $thumb['dynamic_id'];
                    $val['work_class'] = '0';


                    if ($val['audiorecords_type'] == 0) {
                        $val['work_img'] = $val['audiorecords_url'] . '?x-oss-process=video/snapshot,t_10000,m_fast,w_488,h_273,f_png';
                    } else {
                        $img = $this->Show_css->selectOne("select resource_imgurl from app_audiorecords_resource where audiorecords_id = '{$val['audiorecords_id']}'");
                        $val['work_img'] = $img['resource_imgurl'];
                    }
                } elseif ($val['work_type'] == '2') {
                    $audioList = $this->Show_css->selectClear("select w.workpages_audio,p.pages_imageurl as work_imgurl from app_picbooks_workpages as w,app_picbooks_pages as p where w.pages_id = p.pages_id and w.work_id = '{$val['work_id']}'");
                    $thumb = $this->Show_css->selectOne("select thumb_num,dynamic_id from ptc_dynamic_audio where pwork_id = '{$val['work_id']}'");
                    $val['thumb'] = $thumb['thumb_num'];
                    $val['dynamic_id'] = $thumb['dynamic_id'];
                    $val['work_class'] = '0';
                } elseif ($val['work_type'] == '3') {
                    $audioList = $this->Show_css->selectClear("select w.workpages_audio,m.medias_img as work_imgurl from app_ekidbook_workpages as w,app_ekidbook_steps_medias as m where w.medias_id = m.medias_id and w.work_id = '{$val['work_id']}'");
                    $thumb = $this->Show_css->selectOne("select thumb_num,dynamic_id from ptc_dynamic_audio where ework_id = '{$val['work_id']}'");
                    $val['thumb'] = $thumb['thumb_num'];
                    $val['dynamic_id'] = $thumb['dynamic_id'];
                    $img = $this->Show_css->getFieldOne("app_ekidbook_textbook_week", "week_img", "week_id = '{$val['week_id']}' and textbook_id = '{$val['textbook_id']}'");
                    $val['work_img'] = $img['week_img'] . '?x-oss-process=style/wmxa';
                    if ($val['work_class'] == '0') {
                        $num = $this->Show_css->selectOne("select count(*) as num from app_ekidbook_workpages where work_id = '{$val['work_id']}'");
                        $all = $this->Show_css->selectOne("select sum(workpages_score) as allscore from app_ekidbook_workpages where work_id = '{$val['work_id']}'");
                        $val['work_score'] = $all['allscore'] / $num['num'];
                    }
                }
                if ($audioList) {
                    $work = array();
                    foreach ($audioList as $key => $value) {
                        $work[$key]['workpages_audio'] = $value['workpages_audio'];
                        $work[$key]['work_imgurl'] = $value['work_imgurl'];
                    }
                    $val['work_audio'] = json_encode($work);
                } else {
                    $val['work_audio'] = '';
                }


            }
        }

        return $dataList ? $dataList : array();
    }


    //获取作品详情
    function getWorksDetailApi($paramArray)
    {
        if (!$paramArray['work_id']) {
            $paramArray['work_type'] = '3';
            if ($paramArray['type'] == '1') {
                $datawhere = "s.week_id >= '1' and s.week_id <= '5'";
            } elseif ($paramArray['type'] == '2') {
                $datawhere = "s.week_id >= '6' and s.week_id <= '10'";
            } elseif ($paramArray['type'] == '3') {
                $datawhere = "s.week_id >= '11' and s.week_id <= '15'";
            } elseif ($paramArray['type'] == '4') {
                $datawhere = "s.week_id >= '16' and s.week_id <= '20'";
            }

            $wid = $this->Show_css->selectOne("SELECT
	w.work_id,
	sum( p.workpages_score )/count( p.workpages_id ) as c
FROM
	app_ekidbook_work AS w
	LEFT JOIN app_ekidbook_workpages AS p ON w.work_id = p.work_id 
    left join app_ekidbook_steps as s on s.steps_id = w.steps_id
WHERE
	 {$datawhere} and w.student_id = '{$paramArray['student_id']}' and w.class_id in (select ss.class_id from app_student_study as ss where ss.student_id = '{$paramArray['student_id']}' and ss.study_islearning = '1')
GROUP BY
	p.work_id 
	order by c DESC,w.work_createtime DESC");
            if ($wid['work_id']) {
                $paramArray['work_id'] = $wid['work_id'];
            } else {
                $dataOne = '';
                return $dataOne;
            }

        }


        if ($paramArray['work_type'] == '1') {
            $sql = "SELECT  s.student_id,s.student_cnname,s.student_enname,s.student_branch,s.student_sex,s.student_img,'1' as work_type,
                            w.work_id,ar.audiorecords_name as work_name,tb.textbook_coverminimg as work_img,w.work_score,w.work_thumbs,
                            FROM_UNIXTIME(w.work_createtime,'%Y-%m-%d %H:%i') as work_time,count(aw.workpages_id) as work_thisnum,
                            (select count(aw.workpages_id) from app_audiorecords_work as wk,app_audiorecords_workpages as aw where wk.work_id = aw.work_id and wk.class_id = w.class_id and wk.student_id = w.student_id) as work_allnum,
                            (select count(wk.work_id) from app_audiorecords_work as wk where wk.class_id = w.class_id and wk.student_id = w.student_id) as work_dubnum,
                            (select sum(wk.work_thumbs) from app_audiorecords_work as wk where wk.class_id = w.class_id and wk.student_id = w.student_id) as thumbs_num,w.work_type as work_class,'0' as week_id,'0' as textbook_id,ar.audiorecords_type,ar.audiorecords_url,ar.audiorecords_id,w.class_id
                    FROM app_audiorecords_workpages as aw
                    LEFT JOIN app_audiorecords_work as w ON w.work_id = aw.work_id
                    LEFT JOIN app_audiorecords as ar ON ar.audiorecords_id = aw.audiorecords_id
                    LEFT JOIN app_student AS s ON s.student_id = w.student_id
                    LEFT JOIN app_textbook AS tb ON tb.textbook_id = ar.textbook_id
                    WHERE w.work_id = '{$paramArray['work_id']}'";
        } elseif ($paramArray['work_type'] == '2') {
            $sql = "SELECT s.student_id,s.student_cnname,s.student_enname,s.student_branch,s.student_sex,s.student_img,'2' as work_type,
                            w.work_id,pb.picbooks_title as work_name,pb.picbooks_imgurl as work_img,w.work_score,w.work_thumbs,
                            FROM_UNIXTIME(w.work_createtime,'%Y-%m-%d %H:%i') as work_time,IF(w.work_type = 0,count(pw.workpages_id), 1) as work_thisnum,
                            (select count(pw.workpages_id) from app_picbooks_work as wk,app_picbooks_workpages as pw where wk.work_id = pw.work_id and wk.class_id = w.class_id and wk.student_id = w.student_id) as work_allnum,
                            (select count(wk.work_id) from app_picbooks_work as wk where wk.class_id = w.class_id and wk.student_id = w.student_id) as work_dubnum,
                            (select sum(wk.work_thumbs) from app_picbooks_work as wk where wk.class_id = w.class_id and wk.student_id = w.student_id) as thumbs_num,w.work_type as work_class,'0' as week_id,'0' as textbook_id,'0' as audiorecords_type,'' as audiorecords_url,'0' as audiorecords_id,w.class_id
                    FROM app_picbooks_work as w
                    LEFT JOIN app_picbooks_workpages AS pw ON pw.work_id = w.work_id
                    LEFT JOIN app_picbooks as pb ON pb.picbooks_id = w.picbooks_id
                    LEFT JOIN app_student AS s ON s.student_id = w.student_id
                    WHERE w.work_id = '{$paramArray['work_id']}'";
        } elseif ($paramArray['work_type'] == '3') {
            $sql = "SELECT s.student_id,s.student_cnname,s.student_enname,s.student_branch,s.student_sex,s.student_img,'3' as work_type,
                        w.work_id,ca.textbookcate_name as work_name,ca.textbookcate_coverimg as work_img,w.work_score,w.work_thumbs,
                        FROM_UNIXTIME(w.work_createtime, '%Y-%m-%d %H:%i:%s') AS work_time,IF(w.work_type = 0,count(e.workpages_id), 1) as work_thisnum,
                        (select count(wp.workpages_id) from app_ekidbook_work as ew,app_ekidbook_workpages as wp where ew.work_id = wp.work_id and ew.class_id = w.class_id and ew.student_id = w.student_id) as work_allnum,
                        (select count(ew.work_id) from app_ekidbook_work as ew where ew.class_id = w.class_id and ew.student_id = w.student_id) as work_dubnum,
                        (select sum(ew.work_thumbs) from app_ekidbook_work as ew where ew.class_id = w.class_id and ew.student_id = w.student_id) as thumbs_num,w.work_type as work_class,es.week_id,es.textbook_id,'0' as audiorecords_type,'' as audiorecords_url,'0' as audiorecords_id,w.class_id
                    FROM app_ekidbook_work AS w
                    LEFT JOIN app_ekidbook_workpages AS e ON e.work_id = w.work_id
                    LEFT JOIN app_ekidbook_steps AS es ON es.steps_id = w.steps_id
                    LEFT JOIN app_ekidbook_textbook AS te ON te.textbook_id = es.textbook_id
                    LEFT JOIN app_student AS s ON s.student_id = w.student_id
                    LEFT JOIN app_ekidbook_textbookcate AS ca ON ca.textbookcate_id = te.textbookcate_id
                    WHERE w.work_id = '{$paramArray['work_id']}'";
        }
        $dataOne = $this->Show_css->selectOne($sql);
        if ($dataOne) {
            if ($dataOne['work_type'] == '1') {
                $thumb = $this->Show_css->selectOne("select thumb_num,dynamic_id from ptc_dynamic_audio where awork_id = '{$dataOne['work_id']}'");
                $dataOne['thumb_num'] = $thumb['thumb_num'];
                $dataOne['work_thumbs'] = $thumb['thumb_num'];
                if (!$thumb['thumbs_num']) {
                    $dataOne['thumbs_num'] = '0';
                    $dataOne['work_thumbs'] = '0';
                }
                $allthumb = $this->Show_css->selectOne("select sum(thumb_num) as num from ptc_dynamic_audio as a left join app_audiorecords_work as e on e.work_id = a.awork_id where student_id = '{$paramArray['student_id']}' and awork_id > '0'");
                $dataOne['thumbs_num'] = $allthumb['num'];
                if (!$dataOne['thumbs_num']) {
                    $dataOne['thumbs_num'] = '0';
                }
                $audioList = $this->Show_css->selectClear("select w.workpages_audio,r.resource_imgurl as work_imgurl from app_audiorecords_workpages as w,app_audiorecords_resource as r where w.resource_id = r.resource_id and w.work_id = '{$dataOne['work_id']}'");
                $dataOne['work_class'] = '0';

                if ($dataOne['audiorecords_type'] == 0) {
                    $dataOne['work_img'] = $dataOne['audiorecords_url'] . '?x-oss-process=video/snapshot,t_10000,m_fast,w_488,h_273,f_png';
                } else {
                    $img = $this->Show_css->selectOne("select resource_imgurl from app_audiorecords_resource where audiorecords_id = '{$dataOne['audiorecords_id']}'");
                    $dataOne['work_img'] = $img['resource_imgurl'];
                }
            } elseif ($dataOne['work_type'] == '2') {
                $thumb = $this->Show_css->selectOne("select thumb_num,dynamic_id from ptc_dynamic_audio where pwork_id = '{$dataOne['work_id']}'");
                $dataOne['thumb_num'] = $thumb['thumb_num'];
                $dataOne['work_thumbs'] = $thumb['thumb_num'];
                if (!$thumb['thumbs_num']) {
                    $dataOne['thumbs_num'] = '0';
                    $dataOne['work_thumbs'] = '0';
                }
                $allthumb = $this->Show_css->selectOne("select sum(thumb_num) as num from ptc_dynamic_audio as a left join app_picbooks_work as e on e.work_id = a.pwork_id where student_id = '{$paramArray['student_id']}' and pwork_id > '0'");
                $dataOne['thumbs_num'] = $allthumb['num'];
                if (!$dataOne['thumbs_num']) {
                    $dataOne['thumbs_num'] = '0';
                }
                $audioList = $this->Show_css->selectClear("select w.workpages_audio,p.pages_imageurl as work_imgurl from app_picbooks_workpages as w,app_picbooks_pages as p where w.pages_id = p.pages_id and w.work_id = '{$dataOne['work_id']}'");
                $dataOne['work_class'] = '0';
            } elseif ($dataOne['work_type'] == '3') {
                $thumb = $this->Show_css->selectOne("select thumb_num,dynamic_id from ptc_dynamic_audio where ework_id = '{$dataOne['work_id']}'");
                $dataOne['thumb_num'] = $thumb['thumb_num'];
                $dataOne['work_thumbs'] = $thumb['thumb_num'];
                if (!$thumb['thumbs_num']) {
                    $dataOne['thumbs_num'] = '0';
                    $dataOne['work_thumbs'] = '0';
                }
                $allthumb = $this->Show_css->selectOne("select sum(thumb_num) as num from ptc_dynamic_audio as a left join app_ekidbook_work as e on e.work_id = a.ework_id where e.student_id = '{$paramArray['student_id']}' and ework_id > '0'");
                $dataOne['thumbs_num'] = $allthumb['num'];
                if (!$dataOne['thumbs_num']) {
                    $dataOne['thumbs_num'] = '0';
                }
                $img = $this->Show_css->getFieldOne("app_ekidbook_textbook_week", "week_img", "week_id = '{$dataOne['week_id']}' and textbook_id = '{$dataOne['textbook_id']}'");
                $dataOne['work_img'] = $img['week_img'] . '?x-oss-process=style/wmxa';
                if ($dataOne['work_class'] == '0') {
                    $num = $this->Show_css->selectOne("select count(*) as num from app_ekidbook_workpages where work_id = '{$dataOne['work_id']}'");
                    $all = $this->Show_css->selectOne("select sum(workpages_score) as allscore from app_ekidbook_workpages where work_id = '{$dataOne['work_id']}'");
                    $dataOne['work_score'] = $all['allscore'] / $num['num'];
                }

                if ($dataOne['work_class'] == '1') {
                    $audioList = $this->Show_css->selectClear("
SELECT
	k.work_audio as workpages_audio,
	m.medias_img AS work_imgurl 
FROM
	app_ekidbook_work AS k,
	app_ekidbook_steps AS s,
	app_ekidbook_steps_medias AS m 
WHERE
	k.steps_id = s.steps_id 
	AND s.steps_id = m.steps_id 
	AND k.work_id = '{$dataOne['work_id']}'");
                } else {
                    $audioList = $this->Show_css->selectClear("select w.workpages_audio,m.medias_img as work_imgurl from app_ekidbook_workpages as w,app_ekidbook_steps_medias as m where w.medias_id = m.medias_id and w.work_id = '{$dataOne['work_id']}'");

                }
            }
            if ($audioList) {
                $work = array();
                foreach ($audioList as $key => $value) {
                    $work[$key]['workpages_audio'] = $value['workpages_audio'];
                    $work[$key]['work_imgurl'] = $value['work_imgurl'];
                }
                $dataOne['work_audio'] = json_encode($work);
            } else {
                $dataOne['work_audio'] = '';
            }
            $workArr = array();
            $workArr['work_id'] = $dataOne['work_id'];
            $workArr['work_type'] = $dataOne['work_type'];
            $Model = new ClassModel();
            $dataOne['reviewList'] = $Model->getCommentList('', '', '', '', '', $workArr);

            if ($dataOne['work_score'] >= '1' && $dataOne['work_score'] <= '10') {
                $dataOne['entext'] = 'A bold attempt is half success.';
                $dataOne['cntext'] = '勇敢的尝试是成功的一半。';
            } elseif ($dataOne['work_score'] >= '11' && $dataOne['work_score'] <= '20') {
                $dataOne['entext'] = 'Keep up the good work!';
                $dataOne['cntext'] = '继续努力！';
            } elseif ($dataOne['work_score'] >= '21' && $dataOne['work_score'] <= '30') {
                $dataOne['entext'] = 'Give it your best shot!';
                $dataOne['cntext'] = '你要竭尽全力唷！';
            } elseif ($dataOne['work_score'] >= '31' && $dataOne['work_score'] <= '40') {
                $dataOne['entext'] = 'Go the extra mile.';
                $dataOne['cntext'] = '多付出一份努力哦！';
            } elseif ($dataOne['work_score'] >= '41' && $dataOne['work_score'] <= '50') {
                $dataOne['entext'] = 'Practice makes perfect.';
                $dataOne['cntext'] = '熟能生巧。';
            } elseif ($dataOne['work_score'] >= '51' && $dataOne['work_score'] <= '60') {
                $dataOne['entext'] = 'As long as you are willing to work hard for yourself, the world will surprise you.';
                $dataOne['cntext'] = '只要你愿意为自己努力，世界会给你惊喜！';
            } elseif ($dataOne['work_score'] >= '61' && $dataOne['work_score'] <= '70') {
                $dataOne['entext'] = 'You will do better next time. Do your best!';
                $dataOne['cntext'] = '下次你会表现得更好。尽全力去做吧！';
            } elseif ($dataOne['work_score'] >= '71' && $dataOne['work_score'] <= '80') {
                $dataOne['entext'] = 'It was a nice try! Keep up the good work.';
                $dataOne['cntext'] = '这是一次很棒的尝试哦！继续加油吧！';
            } elseif ($dataOne['work_score'] >= '81' && $dataOne['work_score'] <= '90') {
                $dataOne['entext'] = 'Two thumbs up!';
                $dataOne['cntext'] = '给你两个赞！';
            } elseif ($dataOne['work_score'] >= '91' && $dataOne['work_score'] <= '100') {
                $dataOne['entext'] = 'Brilliant! So proud of you!';
                $dataOne['cntext'] = '太棒了！为你骄傲！';
            }
            if ($dataOne['student_sex'] == '男') {
                $dataOne['student_img'] = 'https://oss.kidcastle.cn/manage/202411211142x967451142.png';
            } elseif ($dataOne['student_sex'] == '女') {
                $dataOne['student_img'] = 'https://oss.kidcastle.cn/manage/202411211142x503617798.png';
            }

        }

        return $dataOne;
    }


    //获取园学生积分记录
    function getIntegralGain($paramArray)
    {
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        $sql = "select l.integrallog_playname,s.student_cnname,l.integrallog_playamount,FROM_UNIXTIME(l.integrallog_createtime,'%Y.%m.%d %H:%i') as integrallog_time,s.student_img,s.student_sex from sgr_student_integrallog as l left join app_student as s on s.student_id = l.student_id where l.student_id = '{$paramArray['re_student_id']}' and l.integrallog_playclass = '+' order by l.integrallog_createtime DESC LIMIT {$pagestart},{$num}";
        $list = $this->Show_css->selectClear($sql);
        if ($list) {
            foreach ($list as &$value) {
                $value['integrallog_playamount'] = sprintf("%.1f", $value['integrallog_playamount']);
            }
        }

        if ($list) {
            $this->error = 0;
            $this->errortip = "获取成功！";
            $this->result = $list;
            return true;
        } else {
            $this->error = 1;
            $this->errortip = "暂无数据！";
            $this->result = array();
            return false;
        }
    }

    //获取学生积分提现记录
    function IntegralWithdraw($paramArray)
    {
        if (isset($paramArray['p']) && $paramArray['p'] !== '') {
            $page = $paramArray['p'];
        } else {
            $page = '1';
        }
        if (isset($paramArray['num']) && $paramArray['num'] !== '') {
            $num = $paramArray['num'];
        } else {
            $num = '10';
        }
        $pagestart = ($page - 1) * $num;
        $sql = "select s.student_cnname,l.stuwithdlog_price as integrallog_playamount,FROM_UNIXTIME(l.stuwithdlog_time,'%Y.%m.%d %H:%i') as integrallog_time,s.student_img,s.student_sex from ptc_member_stuwithdlog as l left join app_student as s on s.student_id = l.student_id where l.student_id = '{$paramArray['re_student_id']}' order by l.stuwithdlog_time DESC LIMIT {$pagestart},{$num}";
        $list = $this->Show_css->selectClear($sql);

        if ($list) {
            foreach ($list as &$value) {
                $value['integrallog_playamount'] = sprintf("%.1f", $value['integrallog_playamount']);
            }
        }

        if ($list) {
            foreach ($list as &$val) {
                $val['integrallog_playname'] = '提现';
            }
        }

        if ($list) {
            $this->error = 0;
            $this->errortip = "获取成功！";
            $this->result = $list;
            return true;
        } else {
            $this->error = 1;
            $this->errortip = "暂无数据！";
            $this->result = array();
            return false;
        }
    }

    function strangeLeaveInfo($paramArray)
    {
        $data = array();
        $mobile = $this->Show_css->getFieldOne("app_member", "member_mobile", "member_id = '{$paramArray['member_id']}'");
        $data['strangeinfo_mobile'] = $mobile['member_mobile'];
        $data['strangeinfo_cnname'] = $paramArray['strangeinfo_cnname'];
        $data['strangeinfo_sex'] = $paramArray['strangeinfo_sex'];
        $data['strangeinfo_age'] = $paramArray['strangeinfo_age'];
        if ($paramArray['strangeinfo_age'] == '0') {
            $data['strangeinfo_birthday'] = "2022-01-01";
        } elseif ($paramArray['strangeinfo_age'] == '1') {
            $data['strangeinfo_birthday'] = "2019-01-01";
        } elseif ($paramArray['strangeinfo_age'] == '2') {
            $data['strangeinfo_birthday'] = "2016-01-01";
        }
        $data['strangeinfo_parents'] = $paramArray['strangeinfo_parents'];
        $data['strangeinfo_type'] = $paramArray['strangeinfo_type'];
        $data['strangeinfo_createtime'] = time();
        $this->Show_css->insertData("app_student_strangeinfo", $data);

        $Model = new \Model\Teasx\KddkiddataModel();
        $Model->kidEnterCrmApi($paramArray);

        $this->error = 0;
        $this->errortip = "成功！";
        return true;
    }

    //意见反馈--选项
    function getFeedbackOption($request)
    {
        if ($request['language_type'] == 'tw') {
            //满意文字
            $satisfied = [
                '滿意',
                '帳號相關',
                '學習內容',
                '更多建議',
                '違規舉報',
            ];
        } else {
            //满意文字
            $satisfied = [
                '满意',
                '账号相关',
                '学习内容',
                '更多建议',
                '违规举报',
            ];
        }

        $result = array();
        $result['list'] = $satisfied;

        $this->error = 0;
        $this->errortip = "信息获取成功";
        $this->result = $result;
        return true;
    }


    //意见反馈--提交问题
    function addFeedback($request)
    {
        if ($request['feedback_content'] == '') {
            $this->error = 1;
            $this->errortip = "反馈内容不能为空！";
            return false;
        }

        if ($this->Show_css->selectOne("select feedback_id from app_feedback where student_id = '{$request['student_id']}' and member_id = '{$request['member_id']}' and feedback_model = '{$request['feedback_model']}' and feedback_nature = '{$request['feedback_nature']}' and feedback_theme = '{$request['feedback_theme']}' and feedback_content = '{$request['feedback_content']}' and feedback_imglist = '{$request['feedback_imglist']}' and feedback_type = '1'")) {
            $this->error = 1;
            $this->errortip = "该内容已经提交，请耐性等待！";
            return false;
        }
        $feedback_nature = '';
        if ($request['school_nature'] == '2') {//学前
            $feedback_nature = 'kid';
        } elseif ($request['school_nature'] == '1') {//少儿
            $feedback_nature = 'school';
        }
        $this->Show_css->query("SET NAMES utf8mb4");
        $data = array();
        $data['member_id'] = $request['member_id'];
        $data['feedback_type'] = '1';
        $data['student_id'] = $request['student_id'];
        $data['feedback_model'] = $request['feedback_model'];
        $data['feedback_nature'] = $feedback_nature;
        $data['feedback_theme'] = $request['feedback_theme'];
        $data['feedback_content'] = emoji_encode($request['feedback_content']);
        $data['feedback_imglist'] = stripslashes($request['feedback_imglist']);
        $data['feedback_ip'] = real_ip();
        $data['feedback_time'] = time();
        if ($this->Show_css->insertData("app_feedback", $data)) {
            $this->error = 0;
            $this->errortip = "意见反馈成功！";
            return true;
        } else {
            $this->error = 1;
            $this->errortip = "意见反馈失败！";
            return false;
        }
    }

    /*
     * 外部小程序ID和路径
     */
    function outPathId($request)
    {
        $result = array();
        if ($request['type'] == '1') {
            $result['appId'] = 'wx5684f893a12ebf91';
            $result['originalId'] = 'gh_3411d5d3d4ce';
            $result['path'] = 'pages/home/<USER>/index';
        } elseif ($request['type'] == '2') {
            $result['appId'] = 'wx5684f893a12ebf91';
            $result['originalId'] = 'gh_3411d5d3d4ce';
            $result['path'] = 'pages/common/webview-page/index?src=https://h5.youzan.com/wscump/pointstore/pointdetails';
        }

        $this->error = 0;
        $this->errortip = "获取成功！";
        $this->result = $result;
        return true;
    }

    //预约接送
    function appointment($request)
    {
        $scbranch = $this->Show_css->getFieldOne("app_school", "school_branch", "school_id = '{$request['school_id']}'");
        $this->Show_css->query("SET NAMES utf8mb4");
        $data = array();
        $data['member_id'] = $request['re_member_id'];
        $data['student_id'] = $request['student_id'];
        $data['school_branch'] = $scbranch['school_branch'];
        $data['location_id'] = $request['location_id'];
        $data['msinfo_time'] = $request['msinfo_time'];
        $data['msinfo_day'] = $request['msinfo_day'];
        $data['msinfo_content'] = emoji_encode($request['msinfo_content']);
        $data['msinfo_relation'] = $request['msinfo_relation'];
        $data['msinfo_mobile'] = $request['msinfo_mobile'];
        $data['msinfo_createtime'] = time();
        $this->Show_css->insertData("eas_appointment_msinfo", $data);

        $this->error = 0;
        $this->errortip = "预约接送成功";
        return true;
    }

    function appointmentOne($request)
    {
        if ($request['re_student_id']) {
            $appointmentOne = $this->Show_css->selectOne("select m.* from eas_appointment_msinfo as m where m.student_id = '{$request['re_student_id']}' and msinfo_day = '{$request['msinfo_day']}' and msinfo_status > '-1'");
            $relation = $this->Show_css->getFieldOne("app_member_student", "relation", "member_id = '{$appointmentOne['member_id']}' and student_id = '{$request['re_student_id']}'");
            $student = $this->Show_css->getFieldOne("app_student", "student_cnname", "student_id = '{$request['re_student_id']}'");

        } else {
            $appointmentOne = $this->Show_css->selectOne("select m.* from eas_appointment_msinfo as m where m.student_id = '{$request['student_id']}' and msinfo_day = '{$request['msinfo_day']}' and msinfo_status > '-1'");
            $relation = $this->Show_css->getFieldOne("app_member_student", "relation", "member_id = '{$appointmentOne['member_id']}' and student_id = '{$request['student_id']}'");
            $student = $this->Show_css->getFieldOne("app_student", "student_cnname", "student_id = '{$request['student_id']}'");

        }


        if ($request['language_type'] == 'tw') {
            $content = '家長已️接走' . $student['student_cnname'];
        } else {
            $content = '家长已️接走' . $student['student_cnname'];
        }

        $log = $this->Show_css->getFieldOne("eas_appointment_log", "from_unixtime(log_createtime,'%H:%i') as log_createtime", "msinfo_id = '{$appointmentOne['msinfo_id']}' and log_content = '{$content}'");
        $img = $this->Show_css->getFieldOne("app_member", "member_imghead", "member_id = '{$appointmentOne['member_id']}'");


        $appointmentOne['log_createtime'] = $log['log_createtime'];

        if ($appointmentOne) {
            if (!$img['member_imghead']) {
                if ($relation['relation'] == '0') {
                    $appointmentOne['member_imghead'] = 'https://oss.kidcastle.cn/manage/202408281720x178089286.png';
                } elseif ($relation['relation'] == '1') {
                    $appointmentOne['member_imghead'] = 'https://oss.kidcastle.cn/manage/202408281721x374563514.png';
                } elseif ($relation['relation'] == '2') {
                    $appointmentOne['member_imghead'] = 'https://oss.kidcastle.cn/manage/202408281721x133078806.png';
                } elseif ($relation['relation'] == '3') {
                    $appointmentOne['member_imghead'] = 'https://oss.kidcastle.cn/manage/202408281721x574296349.png';
                } elseif ($relation['relation'] == '4') {
                    $appointmentOne['member_imghead'] = 'https://oss.kidcastle.cn/manage/202408281721x133078806.png';
                } elseif ($relation['relation'] == '5') {
                    $appointmentOne['member_imghead'] = 'https://oss.kidcastle.cn/manage/202408281721x574296349.png';
                } elseif ($relation['relation'] == '6') {
                    $appointmentOne['member_imghead'] = 'https://oss.kidcastle.cn/manage/202408281721x745668147.png';
                }
                if (!$relation['relation']) {
                    $appointmentOne['member_imghead'] = 'https://oss.kidcastle.cn/manage/202408281721x745668147.png';
                }
            }

            $location = $this->Show_css->getFieldOne("eas_appointment_location", "location_name", "location_id = '{$appointmentOne['location_id']}'");


            $appointmentOne['location_name'] = $location['location_name'];
            $this->error = 0;
            $this->errortip = "获取成功！";
            $this->result = $appointmentOne;
            return true;
        } else {
            $this->error = 1;
            $this->errortip = "暂无数据！";
            return false;
        }
    }

    function appointmentHistory($request)
    {
        $logList = $this->Show_css->selectClear("select l.log_id,l.msinfo_id,l.log_type,l.log_content,from_unixtime( l.log_createtime, '%H:%i' ) as log_createtime from eas_appointment_log as l left join eas_appointment_msinfo as m on m.msinfo_id = l.msinfo_id where m.student_id = '{$request['student_id']}' and msinfo_day = '{$request['msinfo_day']}' and m.msinfo_status > '-1' order by l.log_createtime DESC");

        if ($logList) {
            $this->error = 0;
            $this->errortip = "获取成功！";
            $this->result = $logList;
            return true;
        } else {
            $this->error = 1;
            $this->errortip = "暂无数据！";
            $this->result = array();
            return false;
        }
    }


    //取消预约
    function cancelAppointment($request)
    {
        $data = array();
        $data['msinfo_status'] = '-1';
        $this->Show_css->updateData("eas_appointment_msinfo", "msinfo_id = '{$request['msinfo_id']}'", $data);
        $this->error = 0;
        $this->errortip = "取消预约成功";
        return true;
    }

    //消息类型
    function messageType($request)
    {
        $typeList = $this->Show_css->selectClear("select m.category_id as message_type,m.category_name as message_type_name from ptc_student_message_category as m");

        if ($typeList) {
            $Model = new \Model\TraditionalModel();
            foreach ($typeList as &$value) {
//                $num = $this->Show_css->selectOne("select count(m.message_id) as num from ptc_student_message as m where m.student_id = '{$request['re_student_id']}' and m.is_read = '0' and message_type = '{$value['message_type']}'");
                $num = $this->Show_css->selectOne("
                    SELECT
                        count( m.message_id ) AS num 
                    FROM
                        ptc_student_message AS m 
                    WHERE
                        ( m.student_id = '{$request['re_student_id']}' OR m.student_id = '0' ) 
                        AND message_type = '{$value['message_type']}' 
                        AND NOT EXISTS (
                        SELECT 
                            r.message_id
                        FROM
                            ptc_student_message_readlog AS r 
                        WHERE
                            r.message_id = m.message_id 
                        AND r.student_id = '{$request['re_student_id']}' 
                        AND r.is_read = '1')");
                $value['unread'] = $num['num'];
                $messageOne = $this->Show_css->selectOne("select m.message_content,m.message_time from ptc_student_message as m where m.message_type = '{$value['message_type']}' and (m.student_id = '{$request['re_student_id']}' or m.student_id = '0') order by m.message_time DESC");
                if ($messageOne['message_content']) {

                    if (APPVER == 'TW') {
                        $value['message_content'] = $Model->gb2312_big5($messageOne['message_content']);
                    } else {
                        $value['message_content'] = $messageOne['message_content'];
                    }
                } else {

                    if (APPVER == 'TW') {
                        $value['message_content'] = '還沒有新消息哦～';
                    } else {
                        $value['message_content'] = '还没有新消息哦～';
                    }
                }
                $timestamp = $messageOne['message_time'];
                $year = date('Y', $timestamp);
                $month = date('m', $timestamp);
                $day = date('d', $timestamp);
                $currentYear = date('Y');
                $currentMonth = date('m');
                $currentDay = date('d');
                if ($year == $currentYear && $month == $currentMonth && $day == $currentDay) {
                    $value['message_time'] = date('H:i', $timestamp);
                } else {
                    $value['message_time'] = date('m/d', $timestamp);
                }
                if (!$messageOne) {
//                    $value['message_content'] = '还没有新消息哦～';
                    if (APPVER == 'TW') {
                        $value['message_content'] = '還沒有新消息哦～';
                    } else {
                        $value['message_content'] = '还没有新消息哦～';
                    }
                    $value['message_time'] = '';
                }
            }
            $this->error = 0;
            $this->errortip = "获取成功！";
            $this->result = $typeList;
            return true;
        } else {
            $this->error = 1;
            $this->errortip = "暂无数据！";
            $this->result = array();
            return false;
        }
    }


    //消息列表
    function messageList($request)
    {
        $typeList = $this->Show_css->selectClear("select m.* from ptc_student_message as m where message_type = '{$request['message_type']}' and (m.student_id = '{$request['re_student_id']}' or m.student_id = '0') order by m.message_time DESC");

        if ($typeList) {
            $Model = new LanguageModel('message');
            foreach ($typeList as &$value) {
                $timestamp = $value['message_time'];
                $year = date('Y', $timestamp);
                $month = date('m', $timestamp);
                $day = date('d', $timestamp);
                $currentYear = date('Y');
                $currentMonth = date('m');
                $currentDay = date('d');
                if ($year == $currentYear && $month == $currentMonth && $day == $currentDay) {
                    $value['message_time'] = date('H:i', $timestamp);
                } else {
                    $value['message_time'] = date('m/d', $timestamp);
                }
                if ($request['language_type'] == 'tw') {
                    $value['message_title'] = $Model->Language_Switch($value['message_title'], 'tw');
                    $value['message_content'] = $Model->Language_Switch($value['message_content'], 'tw');
                }
                $isread = $this->Show_css->getFieldOne("ptc_student_message_readlog", "is_read", "student_id = '{$request['re_student_id']}' and message_id = '{$value['message_id']}'");
                if ($isread['is_read'] == '1') {
                    $value['is_read'] = '1';
                } else {
                    $value['is_read'] = '0';
                }
            }
            $this->error = 0;
            $this->errortip = "获取成功！";
            $this->result = $typeList;
            return true;
        } else {
            $this->error = 1;
            $this->errortip = "暂无数据！";
            $this->result = array();
            return false;
        }
    }

    //阅读消息
    function readMessage($request)
    {
        $isset = $this->Show_css->getFieldOne("ptc_student_message_readlog", "readlog_id", "message_id = '{$request['message_id']}' and student_id = '{$request['re_student_id']}'");
        if (!$isset) {
            $data = array();
            $data['student_id'] = $request['re_student_id'];
            $data['message_id'] = $request['message_id'];
            $data['is_read'] = 1;
            $this->Show_css->insertData("ptc_student_message_readlog", $data);
            $this->error = 0;
            $this->errortip = "阅读成功";
            return true;
        } else {
            $data = array();
            $data['is_read'] = '1';
            $this->Show_css->updateData("ptc_student_message_readlog", "message_id = '{$request['message_id']}' and student_id = '{$request['re_student_id']}'", $data);
            $this->error = 0;
            $this->errortip = "阅读成功";
            return true;
        }

    }

    function locationAppointment($request)
    {
        $sbanch = $this->Show_css->getFieldOne("app_school", "school_branch", "school_id = '{$request['school_id']}'");
        $list = $this->Show_css->selectClear("select * from eas_appointment_location where school_branch = '{$sbanch['school_branch']}'");
        if ($list) {
            $this->error = 0;
            $this->errortip = "获取成功！";
            $this->result = $list;
            return true;
        } else {
            $this->error = 1;
            $this->errortip = "暂无数据！";
            $this->result = array();
            return false;
        }
    }


}