<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong <PERSON>
 * Date: 2017/4/13
 * Time: 23:25
 */

namespace Work\Controller\Teasxapi;

use Model\Teasx\SettingsModel;
use Work\Controller\Bptapi\YzapiController;
use Model\Teasx\KddscdataModel;
use Model\Teasx\KddkiddataModel;

class SettingsController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $postbeOne = array();

    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();

        $this->postbeOne = $this->Show_css->getFieldOne("eas_teacher_postbe", "organize_id", "postbe_id = '{$_POST['postbe_id']}'");
    }

    //获取校园列表
    function getSchoolView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getSchoolApi($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "school_fullname";
        $field[$k]["fieldname"] = "校园全称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校园简称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_class";
        $field[$k]["fieldname"] = "校园类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "检索代码";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "catcode_name";
        $field[$k]["fieldname"] = "所属区域";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_nature";
        $field[$k]["fieldname"] = "类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        if ($dataList['list']) {
            $result['list'] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取校园列表成功', 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无校园数据', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //课程相关设置
    function getCourseSettingsView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getCourseSettingsApi($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "course_cnname";
        $field[$k]["fieldname"] = "课程别名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_branch";
        $field[$k]["fieldname"] = "课程别编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_cnname";
        $field[$k]["fieldname"] = "所属班种";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_cnname";
        $field[$k]["fieldname"] = "所属班组";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        if (!isset($request['labeltemp_id']) && !isset($request['dynamic_id']) && !isset($request['examplan_id'])) {
            $k++;
            $field[$k]["fieldstring"] = "course_hours";
            $field[$k]["fieldname"] = "课时数";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;

            $k++;
            $field[$k]["fieldstring"] = "course_times";
            $field[$k]["fieldname"] = "教学进度次数";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;

            $k++;
            $field[$k]["fieldstring"] = "course_showeffect";
            $field[$k]["fieldname"] = "是否开启成效";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $field[$k]["switchVisible"] = 1;

            $k++;
            $field[$k]["fieldstring"] = "course_isexamassess";
            $field[$k]["fieldname"] = "是否开启考试测评（WEB）";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $field[$k]["switchVisible"] = 1;

            $k++;
            $field[$k]["fieldstring"] = "course_isminibook";
            $field[$k]["fieldname"] = "是否开启读书检核（APP）";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $field[$k]["switchVisible"] = 1;

            $k++;
            $field[$k]["fieldstring"] = "course_effectpower_name";
            $field[$k]["fieldname"] = "开启成效权限";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $field[$k]["clickVisible"] = 1;
        }

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        if ($dataList['list']) {
            $result['list'] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取课程别列表成功', 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无课程别数据', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //开启/关闭学习成效
    function openEffectView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->openEffectApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->课程相关设置", "开启/关闭学习成效", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //开启/关闭考试测评
    function updateExamAssessView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->updateExamAssessApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->课程相关设置", "开启/关闭考试测评", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //开启/关闭读书检核
    function updateReadBookView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->updateReadBookApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->课程相关设置", "开启/关闭读书检核", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //开启成效权限
    function updateEffectPowerView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->updateEffectPowerApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->课程相关设置", "开启成效权限", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //查看课次
    function getCourseTimesView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getCourseTimesApi($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "times_name";
        $field[$k]["fieldname"] = "课次数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "planhour_name";
        $field[$k]["fieldname"] = "关联课时";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        if ($dataList) {
            $result["list"] = $dataList;
            $res = array('error' => 0, 'errortip' => '获取课次信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无课次', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //一键生成课次
    function addCourseTimesView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->addCourseTimesApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->课程相关设置", "一键生成课次", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //课程别电访设置
    function getTrackSettingsView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getTrackSettingsApi($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "times_name";
        $field[$k]["fieldname"] = "电访课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "callplan_type";
        $field[$k]["fieldname"] = "电访类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "postrole_name";
        $field[$k]["fieldname"] = "执行角色";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "post_name";
        $field[$k]["fieldname"] = "执行职务";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "callplan_isforbidden";
        $field[$k]["fieldname"] = "是否启用";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取课程别电访信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无课程别电访', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //查看电访设置
    function getTrackSetView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getTrackSetApi($request);

        $result = array();
        $result["list"] = $dataList;
        $res = array('error' => 0, 'errortip' => '获取电访设置信息', 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    //添加电访设置
    function addTrackSettingsView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->addTrackSettingsApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->课程相关设置->电访设置", "添加电访设置", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //编辑电访设置
    function updateTrackSettingsView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->updateTrackSettingsApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->课程相关设置->电访设置", "编辑电访设置", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //启用/禁用电访设置
    function openTrackSettingsView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->openTrackSettingsApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->课程相关设置->电访设置", "启用/禁用电访设置", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //课程别推文设置
    function getCourseDynamicView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getCourseDynamicApi($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "times_name";
        $field[$k]["fieldname"] = "推文周次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "dynamictemp_title";
        $field[$k]["fieldname"] = "推文标题";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "dynamictemp_listimgurl";
        $field[$k]["fieldname"] = "封面图片";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["imgVisible"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "dynamictemp_tags";
        $field[$k]["fieldname"] = "主题";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "dynamictemp_type_name";
        $field[$k]["fieldname"] = "类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "is_linkdynamic_name";
        $field[$k]["fieldname"] = "是否是链接推文";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "dynamictemp_isfollowtimes_name";
        $field[$k]["fieldname"] = "发送模式";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        if (empty($request['is_quote'])) {
            $k++;
            $field[$k]["fieldstring"] = "dynamictemp_updatetime";
            $field[$k]["fieldname"] = "更新时间";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;

            $k++;
            $field[$k]["fieldstring"] = "teacher_cnname";
            $field[$k]["fieldname"] = "创建人";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
        }

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取课程别推文信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无课程别推文', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //发布推文
    function addCourseDynamicView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->addCourseDynamicApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->课程相关设置->推文设置", "发布推文", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //编辑推文
    function updateCourseDynamicView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->updateCourseDynamicApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->课程相关设置->推文设置", "编辑推文", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //启用/禁用推文
    function setCourseDynamicView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->setCourseDynamicApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->课程相关设置->推文设置", "启用/禁用推文", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //更新历史推文
    function updateHistoryDynamicView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->updateHistoryDynamicApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->课程相关设置->推文设置", "更新历史推文", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //课程别学习进度设置
    function getCourseTimesSettingsView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getCourseTimesSettingsApi($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "times_name";
        $field[$k]["fieldname"] = "课次名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "isset_times";
        $field[$k]["fieldname"] = "是否设置学习进度";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取课程别学习进度信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无课程别学习进度', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //编辑学习进度
    function updateCourseTimesView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->updateCourseTimesApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->课程相关设置", "编辑学习进度", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //成效登记
    function getResultsRegisterView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getResultsRegisterApi($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "times_name";
        $field[$k]["fieldname"] = "课次名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["fieldfixed"] = 1;
        $k++;

        if ($dataList['effect']) {
            foreach ($dataList['effect'] as $key => $value) {
                $field[$k]["fieldstring"] = "apply_status" . $key;
                $field[$k]["fieldname"] = $value['effect_name'];
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $field[$k]["switchVisible"] = 1;
                $k++;
            }
        }

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取成效登记信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无成效登记', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //开启/关闭成效登记
    function openResultsRegisterView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->openResultsRegisterApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->课程相关设置", "开启/关闭成效登记", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //设置学习成效
    function setStudyResultsView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->setStudyResultsApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->课程相关设置", "设置学习成效", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //课程奖章模板设置
    function getCourseHonorTempView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getCourseHonorTempApi($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "honortemp_title";
        $field[$k]["fieldname"] = $request['honortemp_mold'] == '1' ? "证书名称" : "勋章名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "honortype_name";
        $field[$k]["fieldname"] = "分类";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "honortemp_describe";
        $field[$k]["fieldname"] = "说明";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "teacher_cnname";
        $field[$k]["fieldname"] = "创建人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "honortemp_updatetime";
        $field[$k]["fieldname"] = "更新时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取奖章模板信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无奖章模板', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //获取引用奖章模板
    function getQuoteHonorTempView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getQuoteHonorTempApi($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "honortemp_title";
        $field[$k]["fieldname"] = $request['honortemp_mold'] == '1' ? "证书名称" : "勋章名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "honortype_name";
        $field[$k]["fieldname"] = "分类";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取引用奖章模板信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无引用奖章模板', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //查看奖章
    function getHonorTempOneView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getHonorTempOneApi($request);

        $result = array();
        $result["list"] = $dataList;
        $res = array('error' => 0, 'errortip' => '获取奖章信息', 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    //获取动态数据
    function getDynamicDataView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $data = array();
        $key = 0;
        $data[$key]['id'] = '1';
        $data[$key]['name'] = '学校名称';
        $data[$key]['value'] = 'school_cnname';
        $key++;
        $data[$key]['id'] = '2';
        $data[$key]['name'] = '学生姓名（中）';
        $data[$key]['value'] = 'student_cnname';
        $key++;
        $data[$key]['id'] = '3';
        $data[$key]['name'] = '学生姓名（英）';
        $data[$key]['value'] = 'student_enname';
        $key++;
        $data[$key]['id'] = '4';
        $data[$key]['name'] = '就读课程别';
        $data[$key]['value'] = 'course_branch';
        $key++;
        $data[$key]['id'] = '5';
        $data[$key]['name'] = '班级名称';
        $data[$key]['value'] = 'class_cnname';
        $key++;
        $data[$key]['id'] = '6';
        $data[$key]['name'] = '班级别名';
        $data[$key]['value'] = 'class_enname';
        $key++;
        $data[$key]['id'] = '7';
        $data[$key]['name'] = '班级就读天数';
        $data[$key]['value'] = 'study_days';
        $key++;
        $data[$key]['id'] = '8';
        $data[$key]['name'] = '日期（当天）';
        $data[$key]['value'] = 'date';
        $key++;
        $data[$key]['id'] = '9';
        $data[$key]['name'] = '班级主教教师';
        $data[$key]['value'] = 'teacher_cnname';

        $result = array();
        $result['list'] = $data;
        $res = array('error' => 0, 'errortip' => '获取动态数据成功', 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    //添加奖章模板
    function addHonorTempView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->addHonorTempApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->课程相关设置->奖章模板", "添加奖章模板", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //一键引用奖章模板
    function quoteHonorTempView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->quoteHonorTempApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->课程相关设置->奖章模板", "一键引用奖章模板", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //编辑奖章模板
    function updateHonorTempView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->updateHonorTempApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->课程相关设置->奖章模板", "编辑奖章模板", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //删除奖章模板
    function delHonorTempView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->delHonorTempApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->课程相关设置->奖章模板", "删除奖章模板", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //证书勋章分类设置
    function getHonorTempTypeView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getHonorTempTypeApi($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "honortype_name";
        $field[$k]["fieldname"] = "分类名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "honortype_passage_name";
        $field[$k]["fieldname"] = "所属通路";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "honortype_sort";
        $field[$k]["fieldname"] = "排序";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取证书勋章分类信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无证书勋章分类', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //添加证书勋章分类
    function addHonorTempTypeView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->addHonorTempTypeApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->家校相关设置->证书分类设置", "添加证书勋章分类", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //编辑证书勋章分类
    function updateHonorTempTypeView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->updateHonorTempTypeApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->家校相关设置->证书分类设置", "编辑证书勋章分类", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //删除证书勋章分类
    function delHonorTempTypeView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->delHonorTempTypeApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->家校相关设置->证书分类设置", "删除证书勋章分类", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //课后点评设置
    function getAfterClassCommentsView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getAfterClassCommentsApi($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "commenttemp_classname";
        $field[$k]["fieldname"] = "类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "commenttemp_passage_name";
        $field[$k]["fieldname"] = "所属通路";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "tags_nums";
        $field[$k]["fieldname"] = "快捷检索标签";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["clickVisible"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "remark_nums";
        $field[$k]["fieldname"] = "明细";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取课后点评信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无课后点评', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //快捷检索标签
    function getQuickTagsView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getQuickTagsApi($request);

        $result = array();
        $result["list"] = $dataList;
        $res = array('error' => 0, 'errortip' => '获取快捷检索标签信息', 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    //添加点评模板库
    function addCommentTempView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->addCommentTempApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->家校相关设置->点评模板设置", "添加点评模板库", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //编辑点评模板库
    function updateCommentTempView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->updateCommentTempApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->家校相关设置->点评模板设置", "编辑点评模板库", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //删除点评模板库
    function delCommentTempView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->delCommentTempApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->家校相关设置->点评模板设置", "删除点评模板库", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //评价维度设置
    function getEvalDimensSettingsView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getEvalDimensSettingsApi($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "evaldimens_name";
        $field[$k]["fieldname"] = "评价维度";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        if (empty($paramArray['is_page'])) {
            $field[$k]["fieldstring"] = "evaldimens_passage_name";
            $field[$k]["fieldname"] = "所属通路";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
        }

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取评价维度信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无评价维度', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //添加评价维度
    function addEvalDimensView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->addEvalDimensApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->家校相关设置->评价维度设置", "添加评价维度", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //删除评价维度
    function delEvalDimensView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->delEvalDimensApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->家校相关设置->评价维度设置", "删除评价维度", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //适配评价维度
    function setEvalDimensView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->setEvalDimensApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->课程相关设置->班种相关设置", "适配评价维度", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //星级说明
    function getStarLevelsView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getStarLevelsApi($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "starlevels_name";
        $field[$k]["fieldname"] = "星级";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["is_star"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "starlevels_passage_name";
        $field[$k]["fieldname"] = "所属通路";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "starlevels_explain";
        $field[$k]["fieldname"] = "星级说明";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取星级说明信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无星级说明', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //编辑星级说明
    function updateStarLevelsView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->updateStarLevelsApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->家校相关设置->星级说明", "编辑星级说明", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //班种模板设置
    function getCourseCatTempSettingsView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getCourseCatTempSettingsApi($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "coursecat_cnname";
        $field[$k]["fieldname"] = "班种名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_branch";
        $field[$k]["fieldname"] = "班种编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_cnname";
        $field[$k]["fieldname"] = "所属班组";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取班种模板信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无班种信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //点评模板
    function getCommentTempView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getCommentTempApi($request);

        $result = array();
        $result["list"] = $dataList;
        $res = array('error' => 0, 'errortip' => '获取点评模板信息', 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    //适配点评模板
    function setCommentTempView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->setCommentTempApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->课程相关设置->班种相关设置", "适配点评模板", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //适配电访类型
    function setTrackTypeApplyView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->setTrackTypeApplyApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->课程相关设置->班种相关设置", "适配电访类型", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //电访相关设置
    function getTrackTypeView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getTrackTypeApi($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "tracktype_name";
        $field[$k]["fieldname"] = "电访类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "tracktype_overview";
        $field[$k]["fieldname"] = "AI生成简要指示说明";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "tracktype_iscoreopen_name";
        $field[$k]["fieldname"] = "是否需要填写电访分数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "score_name";
        $field[$k]["fieldname"] = "电访分数结果";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "result_name";
        $field[$k]["fieldname"] = "电访结果";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        $result["apply_nums"] = $dataList['apply_nums'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取电访相关设置信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无电访相关设置', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //添加电访类型
    function addTrackTypeView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->addTrackTypeApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->电访相关设置", "添加电访类型", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //编辑电访类型
    function updateTrackTypeView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->updateTrackTypeApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->电访相关设置", "编辑电访类型", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //删除电访类型
    function delTrackTypeView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->delTrackTypeApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->电访相关设置", "删除电访类型", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //图书推荐管理
    function getBookRecommendView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getBookRecommendApi($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "month_name";
        $field[$k]["fieldname"] = "月份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "book_nums";
        $field[$k]["fieldname"] = "推荐图书";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        if ($dataList) {
            $result["list"] = $dataList;
            $res = array('error' => 0, 'errortip' => '获取图书推荐信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无图书推荐', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //图书推荐适配图书
    function getReadBooksView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getReadBooksApi($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "readbooks_title";
        $field[$k]["fieldname"] = "书籍名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_name";
        $field[$k]["fieldname"] = "图书系列";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "age_name";
        $field[$k]["fieldname"] = "适合年龄段";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "is_apply_name";
        $field[$k]["fieldname"] = "是否适配";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["book_nums"] = $dataList['book_nums'];
        $result["allnum"] = $dataList['allnum'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取图书推荐适配图书信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无适配图书', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //适配/取消适配图书
    function addApplyReadBooksView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->addApplyReadBooksApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->图书推荐", "适配/取消适配图书", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //主题标签设置
    function getLabelTempView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getLabelTempApi($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "labeltemp_name";
        $field[$k]["fieldname"] = "主题名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "labeltemp_passage_name";
        $field[$k]["fieldname"] = "所属通路";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "labeltemp_type_name";
        $field[$k]["fieldname"] = "主题类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "labeltemp_module_name";
        $field[$k]["fieldname"] = "课程模块";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "issplcourse_nums";
        $field[$k]["fieldname"] = "适用课程";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["clickVisible"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取主题标签信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无主题标签', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //新增主题标签
    function addLabelTempView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->addLabelTempApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->家校相关设置->主题标签设置", "新增主题标签", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //编辑主题标签
    function updateLabelTempView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->updateLabelTempApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->家校相关设置->主题标签设置", "编辑主题标签", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //删除主题标签
    function delLabelTempView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->delLabelTempApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->家校相关设置->主题标签设置", "删除主题标签", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //照片边框设置
    function getPhotoBorderView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getPhotoBorderApi($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "photoborder_name";
        $field[$k]["fieldname"] = "边框名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "photoborder_passage_name";
        $field[$k]["fieldname"] = "所属通路";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "photoborder_type_name";
        $field[$k]["fieldname"] = "是否通用模板";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "photoborder_img";
        $field[$k]["fieldname"] = "边框样式";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["multImgVisible"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "photoborder_updatetime";
        $field[$k]["fieldname"] = "更新时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "teacher_cnname";
        $field[$k]["fieldname"] = "创建人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取照片边框信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无照片边框', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //新增照片边框
    function addPhotoBorderView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->addPhotoBorderApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->家校相关设置->照片边框设置", "新增照片边框", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //编辑照片边框
    function updatePhotoBorderView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->updatePhotoBorderApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->家校相关设置->照片边框设置", "编辑照片边框", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //删除照片边框
    function delPhotoBorderView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->delPhotoBorderApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->家校相关设置->照片边框设置", "删除照片边框", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //课程别照片边框设置
    function getCoursePhotoBorderView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getCoursePhotoBorderApi($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "times_name";
        $field[$k]["fieldname"] = "课次名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "labeltemp_name";
        $field[$k]["fieldname"] = "主题名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "photoborder_name";
        $field[$k]["fieldname"] = "照片边框";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "photoborder_img";
        $field[$k]["fieldname"] = "边框样式";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["multImgVisible"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "apply_updatetime";
        $field[$k]["fieldname"] = "更新时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "teacher_cnname";
        $field[$k]["fieldname"] = "创建人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取课程别照片边框信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无课程别照片边框', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //新增课程别照片边框
    function addCoursePhotoBorderView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->addCoursePhotoBorderApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->课程相关设置->照片边框设置", "新增课程别照片边框", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //编辑课程别照片边框
    function updateCoursePhotoBorderView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->updateCoursePhotoBorderApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->课程相关设置->照片边框设置", "编辑课程别照片边框", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //删除课程别照片边框
    function delCoursePhotoBorderView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->delCoursePhotoBorderApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->课程相关设置->照片边框设置", "删除课程别照片边框", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //任务类型管理
    function getTaskTypeView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getTaskTypeApi($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "tasktype_name";
        $field[$k]["fieldname"] = "任务类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "tasktype_passage_name";
        $field[$k]["fieldname"] = "所属通路";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取任务类型信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无任务类型', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //添加任务类型
    function addTaskTypeView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->addTaskTypeApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->家校相关设置->任务类型管理", "添加任务类型", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //删除任务类型
    function delTaskTypeView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->delTaskTypeApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->家校相关设置->任务类型管理", "删除任务类型", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //课程任务模板
    function getTaskTempView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getTaskTempApi($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "tasktemp_title";
        $field[$k]["fieldname"] = "模板名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "tasktemp_passage_name";
        $field[$k]["fieldname"] = "所属通路";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "tasktype_name";
        $field[$k]["fieldname"] = "任务类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "tasktemp_endday_name";
        $field[$k]["fieldname"] = "结束日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "tasktemp_giveintegral_name";
        $field[$k]["fieldname"] = "赠送积分";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "tasktemp_mold_name";
        $field[$k]["fieldname"] = "任务模式";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "tasktemp_name";
        $field[$k]["fieldname"] = "任务名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "tasktemp_describe";
        $field[$k]["fieldname"] = "任务描述";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "tasktemp_updatetime";
        $field[$k]["fieldname"] = "更新时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "teacher_cnname";
        $field[$k]["fieldname"] = "创建人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取课程任务模板信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无课程任务模板', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //获取任务模板题目明细
    function getTaskTempQuestionView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getTaskTempQuestionApi($request);

        $result = array();
        if ($dataList) {
            $result["list"] = $dataList;
            $res = array('error' => 0, 'errortip' => '获取题目明细信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无题目明细', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //新增任务模板
    function addTaskTempView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->addTaskTempApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->家校相关设置->课程任务模板", "新增任务模板", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //编辑任务模板
    function updateTaskTempView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->updateTaskTempApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->家校相关设置->课程任务模板", "编辑任务模板", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //删除任务模板
    function delTaskTempView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->delTaskTempApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->家校相关设置->课程任务模板", "删除任务模板", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //课程别任务模板设置
    function getCourseTaskTempView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getCourseTaskTempApi($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "times_name";
        $field[$k]["fieldname"] = "课次名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["fieldwidth"] = 100;
        $k++;

        $field[$k]["fieldstring"] = "tasktemp_title";
        $field[$k]["fieldname"] = "模板名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "tasktemp_name";
        $field[$k]["fieldname"] = "任务名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "tasktemp_mold_name";
        $field[$k]["fieldname"] = "任务模式";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "apply_issubmitrating_name";
        $field[$k]["fieldname"] = "是否需家长提交评分";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "tasktype_name";
        $field[$k]["fieldname"] = "任务类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "apply_isfollowtimes_name";
        $field[$k]["fieldname"] = "发送模式";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "honortemp_type_name";
        $field[$k]["fieldname"] = "荣誉设置";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "tasktemp_endday";
        $field[$k]["fieldname"] = "任务结束天数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "tasktemp_giveintegral";
        $field[$k]["fieldname"] = "赠送积分";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "tasktemp_describe";
        $field[$k]["fieldname"] = "任务描述";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "apply_updatetime";
        $field[$k]["fieldname"] = "更新时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "teacher_cnname";
        $field[$k]["fieldname"] = "创建人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "apply_status";
        $field[$k]["fieldname"] = "是否启用";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["switchVisible"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取课程别任务模板信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无课程别任务模板', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //新增课程别任务模板
    function addCourseTaskTempView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->addCourseTaskTempApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->课程相关设置->课程任务模板设置", "新增课程别任务模板", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //编辑课程别任务模板
    function updateCourseTaskTempView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->updateCourseTaskTempApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->课程相关设置->课程任务模板设置", "编辑课程别任务模板", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //启用/禁用课程别任务模板
    function usesCourseTaskTempView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->usesCourseTaskTempApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->课程相关设置->课程任务", "启用/禁用课程别任务模板", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //删除课程别任务模板
    function delCourseTaskTempView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->delCourseTaskTempApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->课程相关设置->班级任务模板设置", "删除课程别任务模板", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //巡检类型设置
    function getInspecTypeView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getInspecTypeApi($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "inspectype_name";
        $field[$k]["fieldname"] = "巡检类型名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "inspectype_module";
        $field[$k]["fieldname"] = "应用模块";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "inspectype_passage_name";
        $field[$k]["fieldname"] = "所属通路";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "inspectype_classnature_name";
        $field[$k]["fieldname"] = "适配班级属性";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "inspectype_status";
        $field[$k]["fieldname"] = "是否启用";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["switchVisible"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取巡检类型信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无巡检类型', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //添加巡检类型
    function addInspecTypeView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->addInspecTypeApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->巡检相关设置", "添加巡检类型", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //编辑巡检类型
    function updateInspecTypeView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->updateInspecTypeApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->巡检相关设置", "编辑巡检类型", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //删除巡检类型
    function delInspecTypeView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->delInspecTypeApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->巡检相关设置", "删除巡检类型", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //开启/关闭巡检类型
    function openInspecTypeView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->openInspecTypeApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->巡检相关设置", "开启/关闭巡检类型", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //巡班项目模板设置
    function getInspecItemView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getInspecItemApi($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "inspecitem_name";
        $field[$k]["fieldname"] = "项目名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "inspecitem_content";
        $field[$k]["fieldname"] = "执行标准";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "inspectype_name";
        $field[$k]["fieldname"] = "巡检类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "inspectype_module";
        $field[$k]["fieldname"] = "应用模块";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "splpost_nums";
        $field[$k]["fieldname"] = "适配职务";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["clickVisible"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "inspecitem_sort";
        $field[$k]["fieldname"] = "排序";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "inspectype_passage_name";
        $field[$k]["fieldname"] = "所属通路";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "inspecitem_status";
        $field[$k]["fieldname"] = "是否启用";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["switchVisible"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => "获取巡班项目模板信息", 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => "暂无巡班项目模板", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //获取巡班项目职务
    function getInspecItemPostView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getInspecItemPostApi($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "post_name";
        $field[$k]["fieldname"] = "职务名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "post_code";
        $field[$k]["fieldname"] = "职务编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取职务信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无职务信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //添加巡班项目模板
    function addInspecItemView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->addInspecItemApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->巡检相关设置->巡班项目模板设置", "添加巡班项目模板", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //编辑巡班项目模板
    function updateInspecItemView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->updateInspecItemApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->巡检相关设置->巡班项目模板设置", "编辑巡班项目模板", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //删除巡班项目模板
    function delInspecItemView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->delInspecItemApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->巡检相关设置->巡班项目模板设置", "删除巡班项目模板", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //开启/关闭巡班项目模板
    function openInspecItemView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->openInspecItemApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->巡检相关设置->巡班项目模板设置", "开启/关闭巡班项目模板", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //听课评分表设置
    function getInspecLsscoreView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getInspecLsscoreApi($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "lsscore_name";
        $field[$k]["fieldname"] = "评分表名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "inspectype_name";
        $field[$k]["fieldname"] = "巡检类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "inspectype_passage_name";
        $field[$k]["fieldname"] = "所属通路";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "lsscore_status";
        $field[$k]["fieldname"] = "是否启用";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["switchVisible"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => "获取听课评分表信息", 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => "暂无听课评分表", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //添加听课评分表
    function addInspecLsscoreView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->addInspecLsscoreApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->巡检相关设置->听课评分表设置", "添加听课评分表", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //编辑听课评分表
    function updateInspecLsscoreView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->updateInspecLsscoreApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->巡检相关设置->听课评分表设置", "编辑听课评分表", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //删除听课评分表
    function delInspecLsscoreView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->delInspecLsscoreApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->巡检相关设置->听课评分表设置", "删除听课评分表", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //开启/关闭听课评分表
    function openInspecLsscoreView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->openInspecLsscoreApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->巡检相关设置->听课评分表设置", "开启/关闭听课评分表", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //课程别巡检/听课设置
    function getCourseInspecTasksView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getCourseInspecTasksApi($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "inspecitem_name";
        $field[$k]["fieldname"] = "项目名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "inspectype_name";
        $field[$k]["fieldname"] = "巡检类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        if ($request['type'] == '1') {
            $tip = "巡检";
            $field[$k]["fieldstring"] = "units_name";
            $field[$k]["fieldname"] = "执行区间";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
        } else {
            $tip = "听课";
            $field[$k]["fieldstring"] = "lsscore_name";
            $field[$k]["fieldname"] = "听课评分表";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
        }

        $field[$k]["fieldstring"] = "inspectasks_status";
        $field[$k]["fieldname"] = "是否启用";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["switchVisible"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => "获取课程别{$tip}信息", 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => "暂无课程别{$tip}信息", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //添加课程别巡检/听课
    function addCourseInspecTasksView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        if ($request['type'] == '1') {
            $module = "巡检";
            $Model->addCourseInspecTasksApi($request);
        } else {
            $module = "听课";
            $Model->addCourseInspecTasksBakApi($request);
        }

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->课程相关设置->{$module}设置", "添加课程别{$module}", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //编辑课程别巡检/听课
    function updateCourseInspecTasksView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        if ($request['type'] == '1') {
            $module = "巡检";
            $Model->updateCourseInspecTasksApi($request);
        } else {
            $module = "听课";
            $Model->updateCourseInspecTasksBakApi($request);
        }

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->课程相关设置->{$module}设置", "编辑课程别{$module}", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //删除课程别巡检/听课
    function delCourseInspecTasksView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->delCourseInspecTasksApi($request);

        $adapter = $this->Show_css->getFieldOne("eas_course_inspectasks", "inspectasks_type", "inspectasks_id = '{$request['inspectasks_id']}'");
        if ($adapter['inspectasks_type'] == '1') {
            $module = "巡检";
        } else {
            $module = "听课";
        }

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->{$module}设置", "删除课程别{$module}", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //开启/关闭课程别巡检/听课
    function openCourseInspecTasksView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->openCourseInspecTasksApi($request);

        $adapter = $this->Show_css->getFieldOne("eas_course_inspectasks", "inspectasks_type", "inspectasks_id = '{$request['inspectasks_id']}'");
        if ($adapter['inspectasks_type'] == '1') {
            $module = "巡检";
        } else {
            $module = "听课";
        }

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->{$module}设置", "开启/关闭课程别{$module}", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //主题/单元名称设置
    function getCourseUnitsView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getCourseUnitsApi($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "units_name";
        $field[$k]["fieldname"] = "主题/单元名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "units_sort";
        $field[$k]["fieldname"] = "排序";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "times_name";
        $field[$k]["fieldname"] = "关联课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => "获取主题/单元名称信息", 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => "暂无主题/单元名称", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //添加课程别主题/单元名称
    function addCourseUnitsView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->addCourseUnitsApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->主题/单元名称设置", "添加主题/单元名称", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //编辑课程别主题/单元名称
    function updateCourseUnitsView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->updateCourseUnitsApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->主题/单元名称设置", "编辑主题/单元名称", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //删除主题/单元名称
    function delCourseUnitsView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->delCourseUnitsApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->主题/单元名称设置", "删除主题/单元名称", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //广告位设置
    function getAdsenseView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getAdsenseApi($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "adsense_title";
        $field[$k]["fieldname"] = "广告位名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "adsense_passage_name";
        $field[$k]["fieldname"] = "所属通路";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "adsense_class_name";
        $field[$k]["fieldname"] = "所属用户类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_cnname";
        $field[$k]["fieldname"] = "所属班组";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "adsense_type_name";
        $field[$k]["fieldname"] = "位置";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "adsense_imgurl";
        $field[$k]["fieldname"] = "广告位图片";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["imgVisible"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "adsense_linktype_name";
        $field[$k]["fieldname"] = "跳转类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "adsense_sort";
        $field[$k]["fieldname"] = "排序";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取广告位信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无广告位', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //新增广告位
    function addAdsenseView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->addAdsenseApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "参数设置->广告位推荐", "新增广告位", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //编辑广告位
    function updateAdsenseView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->updateAdsenseApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "参数设置->广告位推荐", "编辑广告位", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //删除广告位
    function delAdsenseView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->delAdsenseApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "参数设置->广告位推荐", "删除广告位", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //打卡模板设置
    function getClockTempView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getClockTempApi($request);

        $field = array();
        $k = 0;
        if (!empty($request['course_branch'])) {
            $field[$k]["fieldstring"] = "clocktemp_title";
            $field[$k]["fieldname"] = "模板名称";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "clocktemp_name";
            $field[$k]["fieldname"] = "打卡名称";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "clocktemp_mode_name";
            $field[$k]["fieldname"] = "打卡模式";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "clocktemp_content";
            $field[$k]["fieldname"] = "打卡内容";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
        } else {
            $field[$k]["fieldstring"] = "clocktemp_title";
            $field[$k]["fieldname"] = "模板名称";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "clocktemp_name";
            $field[$k]["fieldname"] = "打卡名称";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "clocktemp_mode_name";
            $field[$k]["fieldname"] = "打卡模式";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "clocktemp_sustainday_name";
            $field[$k]["fieldname"] = "打卡次数";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "clocktemp_repairnums_name";
            $field[$k]["fieldname"] = "可补卡次数";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "clocktemp_remindtime_name";
            $field[$k]["fieldname"] = "提醒时间";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "clocktemp_mutuallook_name";
            $field[$k]["fieldname"] = "作品权限";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "clocktemp_passage_name";
            $field[$k]["fieldname"] = "所属通路";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "clocktemp_allschool_name";
            $field[$k]["fieldname"] = "是否全校园";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
        }

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        if (!empty($request['course_branch'])) {
            $result["clocktemp_list"] = $dataList['clocktemp_list'];
        }
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取打卡模板信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无打卡模板', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //新增打卡模板
    function addClockTempView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->addClockTempApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->打卡任务设置", "新增打卡模板", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result);
        ajax_return($res, $request['language_type']);
    }

    //编辑打卡模板
    function updateClockTempView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->updateClockTempApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->打卡任务设置", "编辑打卡模板", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //获取绘本挑战模式信息
    function getReadBookModeView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getReadBookModeApi($request);

        $result = array();
        $result["list"] = $dataList;
        $res = array('error' => 0, 'errortip' => '获取绘本挑战模式信息', 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    //查看每次打卡内容
    function getFrequencyView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getFrequencyApi($request);

        $result = array();
        $result["list"] = $dataList;
        $res = array('error' => 0, 'errortip' => '获取每次打卡内容信息', 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    //设置每次打卡内容
    function setFrequencyView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->setFrequencyApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->打卡任务设置", "设置每次打卡内容", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //删除打卡模板
    function delClockTempView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->delClockTempApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->打卡任务设置", "删除打卡模板", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //适配打卡模板
    function applyClockTempView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->applyClockTempApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->课程相关设置", "适配打卡模板", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //打卡任务发布记录
    function getClockTasksRecordView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getClockTasksRecordApi($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "dynamic_title";
        $field[$k]["fieldname"] = "打卡标题";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["fieldwidth"] = 150;
        $k++;

        $field[$k]["fieldstring"] = "clockcycle_date";
        $field[$k]["fieldname"] = "打卡周期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["fieldwidth"] = 180;
        $k++;

        $field[$k]["fieldstring"] = "clocktemp_mode_name";
        $field[$k]["fieldname"] = "打卡模式";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "schoolsql_nums";
        $field[$k]["fieldname"] = "发布校园";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["clickVisible"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "studentspl_nums";
        $field[$k]["fieldname"] = "接收学生";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "readbookmode_num";
        $field[$k]["fieldname"] = "挑战模式";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "clockcycle_week";
        $field[$k]["fieldname"] = "打卡频次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "clockcycle_repairnums";
        $field[$k]["fieldname"] = "补卡设置";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "clockcycle_remindtime";
        $field[$k]["fieldname"] = "提醒打卡时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "clockcycle_mutuallook";
        $field[$k]["fieldname"] = "打卡是否相互可见";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "clockcycle_modality";
        $field[$k]["fieldname"] = "家长打卡形式";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "dynamic_issuername";
        $field[$k]["fieldname"] = "发布人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "dynamic_updatetime";
        $field[$k]["fieldname"] = "发布时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["fieldwidth"] = 150;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取打卡任务发布记录信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无打卡任务发布记录', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //发布打卡任务
    function sendClockTempTasksView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->sendClockTempTasksApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->打卡任务设置", "发布打卡任务", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //根据有赞接口  获取购买某个商品的用户信息(吉的堡上海校外教育展会订金抵用券)
    function getYzGoodToMemberTwoView(){
        $request = Input('post.', '', 'trim,addslashes');

//        $stime = "2023-06-01 00:00:00";
//        $etime = "2023-06-27 00:00:00";

        $stime = date('Y-m-d 00:00:00', strtotime('-1 day'));
        $etime = date('Y-m-d 23:59:59', strtotime('-1 day'));

        $yztradeOne = $this->Show_css->selectOne(" select yztrade_created from ptc_yz_trade where yztrade_created >= '{$stime}' and yztrade_created <= '{$etime}' and yztrade_goodtitle = '吉的堡上海校外教育展会订金抵用券' order by yztrade_created ASC limit 0,1 ");
        if($yztradeOne['yztrade_created']){
            $etime = $yztradeOne['yztrade_created'];
        }

        $title = "吉的堡上海校外教育展会订金抵用券";//检索商品的名称
        $crmlabel = "";//修改课叮铛的名单 标签
        $Model = new YzapiController();
        $list = $Model->getTrade(1,100,$stime,$etime,$title);

        $item = $list['data']['full_order_info_list'];
        if ($item) {
            foreach ($item as $val) {
                //根据有赞用胡的 open_id 获取用户手机号
                $Model = new YzapiController();
                $info = $Model->getUserInfo($val['full_order_info']['buyer_info']['yz_open_id']);
                if($info['success'] == '1') {
                    $mobile = $info['data']['user_list'][0]['mobile_info']['mobile'];
                }else{
                    $mobile = '';
                }

                if(!$this->Show_css->selectOne(" select open_id from ptc_yz_trade where open_id = '{$val['full_order_info']['buyer_info']['yz_open_id']}' and yztrade_status = '{$val['full_order_info']['order_info']['status_str']}' and yztrade_goodtitle = '{$title}' and yztrade_mobile = '{$mobile}'  ")){
                    $data = array();
                    $data['open_id'] = $val['full_order_info']['buyer_info']['yz_open_id'];
                    $data['yztrade_status'] = $val['full_order_info']['order_info']['status_str'];
                    $data['yztrade_goodtitle'] = $title;
                    $data['yztrade_created'] = $val['full_order_info']['order_info']['created'];
                    $data['yztrade_crmlabel'] = $crmlabel;
                    $data['yztrade_mobile'] = $mobile;
                    $data['yztrade_createtime'] = time();
                    $this->Show_css->insertData("ptc_yz_trade",$data);
                }
            }
        }
    }

    //测试 根据购买虚拟商品获得的码，获取该码的相关信息
    function getTradeVirtualcodeView(){
        $request = Input('post.', '', 'trim,addslashes');
        $request['tid'] = $request['tid']?$request['tid']:'E20250616213441092906155';

        $Model = new YzapiController();
        $list = $Model->getTradeVirtualcode($request['tid']);

        print_r($list);die;

    }

    //测试获取指定日期订单数据 -- 测试查看数据结构
    function getYzGoodToMemberSomeView()
    {
        $request = Input('post.', '', 'trim,addslashes');
echo "数据已处理";die;
        $stime = "2025-06-16 00:00:00";
        $etime = "2025-06-17 00:00:00";

//        $stime = date('Y-m-d 00:00:00', strtotime('-1 day'));
//        $etime = date('Y-m-d 23:59:59', strtotime('-1 day'));

//        $yztradeOne = $this->Show_css->selectOne(" select yztrade_created from ptc_yztrade where yztrade_created >= '{$stime}' and yztrade_created <= '{$etime}' order by yztrade_created ASC limit 0,1 ");
//        if ($yztradeOne['yztrade_created']) {
//            $etime = $yztradeOne['yztrade_created'];
//        }

        $title = "堡贝约课";//检索商品的名称
        $crmlabel = "引流课包";//修改课叮铛的名单 标签
        $Model = new YzapiController();
        $list = $Model->getTrade(1, 100, $stime, $etime, $title);

        $item = $list['data']['full_order_info_list'];
        if ($item) {
            foreach ($item as $val) {
                //根据有赞用胡的 open_id 获取用户手机号
                $Model = new YzapiController();
                $info = $Model->getUserInfo($val['full_order_info']['buyer_info']['yz_open_id']);
                if($info['success'] == '1') {
                    $mobile = $info['data']['user_list'][0]['mobile_info']['mobile'];
                }else{
                    $mobile = '';
                }
                echo "++".$mobile."++";

                $yztradeOne = $this->Show_css->selectOne(" select open_id,yztrade_id from ptc_yztrade where open_id = '{$val['full_order_info']['buyer_info']['yz_open_id']}' and yztrade_tid = '{$val['full_order_info']['order_info']['tid']}' and yztrade_goodtitle = '{$title}' and yztrade_mobile = '{$mobile}'  ");
                if(!$yztradeOne){
                    $data = array();
                    $data['open_id'] = $val['full_order_info']['buyer_info']['yz_open_id'];
                    $data['yztrade_status'] = $val['full_order_info']['order_info']['status_str'];
                    $data['yztrade_tid'] = $val['full_order_info']['order_info']['tid'];
                    $data['yztrade_goodtitle'] = $title;
                    $data['yztrade_created'] = $val['full_order_info']['order_info']['created'];
                    $data['yztrade_crmlabel'] = $crmlabel;
                    $data['yztrade_mobile'] = $mobile;
                    $data['yztrade_createtime'] = time();
                    $yztradeId = $this->Show_css->insertData("ptc_yztrade",$data);

                    if($val['full_order_info']['orders']){
                        foreach ($val['full_order_info']['orders'] as $orderVar){
                            $good = array();
                            $good['yztrade_id'] = $yztradeId;
                            $good['yzgoods_outid'] = $orderVar['item_id'];
                            $this->Show_css->insertData("ptc_yztrade_goods",$good);
                        }
                    }

                }else{

                    //解密的字符串
                    $sarr = [$val['full_order_info']['address_info']['receiver_name'],$val['full_order_info']['address_info']['receiver_tel'],$val['full_order_info']['address_info']['delivery_address']];
                    $earr = array_filter($sarr, function($value) {
                        return $value !== null && $value !== '';
                    });
                    $decrypArr = $Model->getDecryptBatch($earr);

//                    echo '<pre>';
//                    print_r($decrypArr);
//                    echo '111';
//                    echo $decrypArr[$val['full_order_info']['address_info']['receiver_name']];
//                    echo '222';
//                    echo $decrypArr[$val['full_order_info']['address_info']['receiver_tel']];
//                    echo '333';

                    $dataOne = array();
                    $dataOne['yztrade_receiver_name'] = $val['full_order_info']['address_info']['receiver_name']?$decrypArr['data'][$val['full_order_info']['address_info']['receiver_name']]:'';
                    $dataOne['yztrade_receiver_tel'] = $val['full_order_info']['address_info']['receiver_tel']?$decrypArr['data'][$val['full_order_info']['address_info']['receiver_tel']]:'';
                    $dataOne['yztrade_delivery_address'] = $val['full_order_info']['address_info']['delivery_address']?$decrypArr['data'][$val['full_order_info']['address_info']['delivery_address']]:'';
//                    print_r($dataOne);
//                    die;
                    $this->Show_css->updateData("ptc_yztrade"," yztrade_id = '{$yztradeOne['yztrade_id']}' ",$dataOne);

//                    if($val['full_order_info']['orders']){
//                        foreach ($val['full_order_info']['orders'] as $orderVar){
//                            $good = array();
//                            $good['yztrade_id'] = $yztradeOne['yztrade_id'];
//                            $good['yzgoods_outid'] = $orderVar['item_id'];
//                            $good['yztrade_tid'] = $val['full_order_info']['order_info']['tid'];
//                            $this->Show_css->insertData("ptc_yztrade_goods",$good);
//                        }
//                    }
                }
            }
        }
    }

    //根据有赞接口  获取购买某个商品的用户信息(堡贝约课)
    function getYzGoodToMemberView(){
        $request = Input('post.', '', 'trim,addslashes');

//        $stime = "2023-06-01 00:00:00";
//        $etime = "2023-06-27 00:00:00";

        $stime = date('Y-m-d 00:00:00', strtotime('-1 day'));
        $etime = date('Y-m-d 23:59:59', strtotime('-1 day'));

        $yztradeOne = $this->Show_css->selectOne(" select yztrade_created from ptc_yztrade where yztrade_created >= '{$stime}' and yztrade_created <= '{$etime}' order by yztrade_created ASC limit 0,1 ");
        if($yztradeOne['yztrade_created']){
            $etime = $yztradeOne['yztrade_created'];
        }

        $title = "堡贝约课";//检索商品的名称
        $crmlabel = "引流课包";//修改课叮铛的名单 标签
        $Model = new YzapiController();
        $list = $Model->getTrade(1,100,$stime,$etime,$title);

        $item = $list['data']['full_order_info_list'];
        if ($item) {
            foreach ($item as $val) {
                //根据有赞用胡的 open_id 获取用户手机号
                $Model = new YzapiController();
                $info = $Model->getUserInfo($val['full_order_info']['buyer_info']['yz_open_id']);
                if($info['success'] == '1') {
                    $mobile = $info['data']['user_list'][0]['mobile_info']['mobile'];
                }else{
                    $mobile = '';
                }

                if(!$this->Show_css->selectOne(" select open_id from ptc_yztrade where open_id = '{$val['full_order_info']['buyer_info']['yz_open_id']}' and yztrade_tid = '{$val['full_order_info']['order_info']['tid']}' and yztrade_goodtitle = '{$title}' and yztrade_mobile = '{$mobile}'  ")){

                    //解密的字符串
                    $sarr = [$val['full_order_info']['address_info']['receiver_name'],$val['full_order_info']['address_info']['receiver_tel'],$val['full_order_info']['address_info']['delivery_address']];
                    $earr = array_filter($sarr, function($value) {
                        return $value !== null && $value !== '';
                    });
                    $decrypArr = $Model->getDecryptBatch($earr);

                    $data = array();
                    $data['open_id'] = $val['full_order_info']['buyer_info']['yz_open_id'];
                    $data['yztrade_status'] = $val['full_order_info']['order_info']['status_str'];
                    $data['yztrade_tid'] = $val['full_order_info']['order_info']['tid'];
                    $data['yztrade_goodtitle'] = $title;
                    $data['yztrade_created'] = $val['full_order_info']['order_info']['created'];
                    $data['yztrade_crmlabel'] = $crmlabel;
                    $data['yztrade_mobile'] = $mobile;
                    if($decrypArr['success'] == '1' || $decrypArr['success'] === true ){
                        $data['yztrade_receiver_name'] = $val['full_order_info']['address_info']['receiver_name']?$decrypArr['data'][$val['full_order_info']['address_info']['receiver_name']]:'';
                        $data['yztrade_receiver_tel'] = $val['full_order_info']['address_info']['receiver_tel']?$decrypArr['data'][$val['full_order_info']['address_info']['receiver_tel']]:'';
                        $data['yztrade_delivery_address'] = $val['full_order_info']['address_info']['delivery_address']?$decrypArr['data'][$val['full_order_info']['address_info']['delivery_address']]:'';
                    }

                    $data['yztrade_createtime'] = time();
                    $yztradeId = $this->Show_css->insertData("ptc_yztrade",$data);
                    if($val['full_order_info']['orders']){
                        foreach ($val['full_order_info']['orders'] as $orderVar){
                            $good = array();
                            $good['yztrade_id'] = $yztradeId;
                            $good['yzgoods_outid'] = $orderVar['item_id'];
                            $good['yztrade_tid'] = $val['full_order_info']['order_info']['tid'];
                            $this->Show_css->insertData("ptc_yztrade_goods",$good);
                        }
                    }

                }
            }
        }
    }

    function YzTagView(){
        $request = Input('post.', '', 'trim,addslashes');

        $list = $this->Show_css->selectClear("
            SELECT
                l.member_id,
                m.member_mobile,
                sum(l.stuwithdlog_price) as '提现总积分',
                count(l.stuwithdlog_id) as '提现次数',
                sum(l.stuwithdlog_price)/count(l.stuwithdlog_id) as '应该提现积分'
            FROM
                ptc_member_stuwithdlog AS l 
                left join app_member as m on l.member_id = m.member_id
            WHERE
                l.integrallog_id = '0'
                GROUP BY l.member_id
                HAVING count(l.stuwithdlog_id) > 1");

        var_dump($list);die;

        if ($list) {
            foreach ($list as $val) {
                $Model = new YzapiController();
                $Model->adduserstag($val['member_mobile'],"多提现会员");
            }
        }
        $res = array('error' => 1, 'errortip' => '成功');
        ajax_return($res);
    }

    function YzTestView(){
        $this->Bipark = new \Dbsqlplay("rm-bp1t24ym8tb3b798mlo.mysql.rds.aliyuncs.com", "appuser", "Jbd2022!new", "bak_bi_dw_data");


        $list = $this->Bipark->selectClear("select * from ads_act_yz_action_detail_back where  event_type = '2004' and is_push = '1' order by push_date DESC limit 0,1");

        if ($list) {
            foreach($list as &$val){
                $mobile = $this->Bipark->selectOne("select NEW_DECRYPTP('{$val['account_id']}') as mobile");

                $Model = new YzapiController();

                $user = $Model->getUserByMobile($mobile['mobile']);


                if (!$user['data'][0]['yz_open_id']) {
                    $Model->createUser($mobile['mobile']);
                }

                $back = $Model->usersEvent($mobile['mobile'],$val['event_timestamp']*1000,$val['event_type'],json_decode($val['event_detail']));
                var_dump($user);die;

//                var_dump($back);

                if($back['code'] == 200){
                    $data = array();
                    $data['is_push'] = '2';
                    $this->Bipark->updateData("ads_act_yz_action_detail_back","account_id = '{$val['account_id']}' and event_id = '{$val['event_id']}'",$data);
                }else{
                    $data = array();
                    $data['back_code'] = $back['code'];
                    $this->Bipark->updateData("ads_act_yz_action_detail_back","account_id = '{$val['account_id']}' and event_id = '{$val['event_id']}'",$data);
                }
            }
        }







        $request = Input('post.', '', 'trim,addslashes');

        $list = $this->Show_css->selectClear("
            SELECT
                l.member_id,
                m.member_mobile,
                sum(l.stuwithdlog_price) as '提现总积分',
                count(l.stuwithdlog_id) as '提现次数',
                sum(l.stuwithdlog_price)/count(l.stuwithdlog_id) as '应该提现积分'
            FROM
                ptc_member_stuwithdlog AS l 
                left join app_member as m on l.member_id = m.member_id
            WHERE
                l.integrallog_id = '0'
                GROUP BY l.member_id
                HAVING count(l.stuwithdlog_id) > 1");

        var_dump($list);die;

        if ($list) {
            foreach ($list as $val) {
                $Model = new YzapiController();
                $Model->adduserstag($val['member_mobile'],"多提现会员");
            }
        }
        $res = array('error' => 1, 'errortip' => '成功');
        ajax_return($res);
    }

    function reduceYzIntegralView(){
        $list = $this->Show_css->selectClear("
            SELECT
                *
            FROM
                temp_yz where status = 0");

        if ($list) {
            foreach ($list as $val) {
                $reduce = $val['提现总积分'] - $val['应该提现积分'];
//                $allreduce = $reduce*2;
//                var_dump($allreduce);
                if($reduce < $val['有赞现有积分']){
                    $Model = new YzapiController();
                    $Model->decUserPoint($val['member_mobile'], $reduce, '积分发放数据修正补充');

                    $data = array();
                    $data['status'] = 1;

                    $this->Show_css->updateData("temp_yz","member_mobile = '{$val['member_mobile']}'",$data);
                }

            }
        }
        $res = array('error' => 1, 'errortip' => '成功');
        ajax_return($res);
    }

    function YzUserIntegralView(){
        $request = Input('post.', '', 'trim,addslashes');
//        var_dump(1111);

        $list = $this->Show_css->selectClear("
            SELECT
                *
            FROM
                temp_yz where status = '0'");

        if ($list) {
            foreach ($list as $val) {
                $Model = new YzapiController();
                $integral = $Model->userPointGet($val['member_mobile']);

                $data = array();
                $data['有赞现有积分'] = $integral['data']['point'];

                $this->Show_css->updateData("temp_yz","member_mobile = '{$val['member_mobile']}'",$data);
            }
        }
        $res = array('error' => 1, 'errortip' => '成功');
        ajax_return($res);
    }
    
    //这是一个处理老数据打标签的接口
    function addYzClientLabelTestView(){
//        echo "处理老数据";die;
        $all = $this->Show_css->selectClear(" select * from ptc_yztrade where yztrade_id = '13308' and yztrade_goodtitle = '堡贝约课' and yztrade_mobile <> ''  ");
//print_r($all);die;
        foreach ($all as $allMember) {
//        $allMember = $this->Show_css->selectOne(" select * from ptc_yztrade where yztrade_id = '13264' and yztrade_goodtitle = '堡贝约课' and yztrade_mobile <> '' limit 0,1 ");

            //给园务的名单打标签 -- 20250609 的要求，要求两个系统的都跑一下
            $KidkddAuthprivModel = new KddkiddataModel();
            $restwo = $KidkddAuthprivModel->addYzClientLabel('', $allMember);
            if ($restwo['error'] == '0') {
                $data = array();
                $data['yztrade_istoesc'] = 1;
                $data['yztrade_updatetime'] = time();
                $this->Show_css->updateData('ptc_yztrade', "open_id='{$restwo['result']['open_id']}' and yztrade_goodtitle = '堡贝约课' and yztrade_mobile='{$restwo['result']['client_mobile']}' ", $data);
            } else {
                $data = array();
                $data['yztrade_istoesc'] = -1;
                $data['yztrade_updatetime'] = time();
                $this->Show_css->updateData('ptc_yztrade', "open_id='{$restwo['result']['open_id']}' and yztrade_goodtitle = '堡贝约课' and yztrade_mobile='{$restwo['result']['client_mobile']}' ", $data);
            }
        }
    }

    //根据瞿神 查出的购买某课程的名单，对crm名单进行打标签操作
    function addYzClientLabelView(){
        $allMember = $this->Show_css->selectOne(" select * from ptc_yztrade where yztrade_istocrm = '0' and yztrade_goodtitle = '堡贝约课' and yztrade_mobile <> '' limit 0,1 ");
        if($allMember['yztrade_mobile'] == ''){
            ajax_return(array('error' => 1, 'errortip' => '手机号不能为空','result' => array()));
        }
//print_r($allMember);die;
        //给校务的名单打标签
        $kddAuthprivModel = new KddscdataModel();
        $res = $kddAuthprivModel->addYzClientLabel('',$allMember);

        if($res['error'] == '0') {
            $data = array();
            $data['yztrade_istocrm'] = 1;
            $data['yztrade_updatetime'] = time();
            $this->Show_css->updateData('ptc_yztrade', "open_id='{$res['result']['open_id']}' and yztrade_goodtitle = '堡贝约课' and yztrade_mobile='{$res['result']['client_mobile']}' and yztrade_receiver_tel='{$res['result']['receiver_tel']}' ", $data);
        }else{
            $data = array();
            $data['yztrade_istocrm'] = -1;
            $data['yztrade_updatetime'] = time();
            $this->Show_css->updateData('ptc_yztrade', "open_id='{$res['result']['open_id']}' and yztrade_goodtitle = '堡贝约课' and yztrade_mobile='{$res['result']['client_mobile']}' and yztrade_receiver_tel='{$res['result']['receiver_tel']}' ", $data);
        }

        //给园务的名单打标签 -- 20250609 的要求，要求两个系统的都跑一下
        $KidkddAuthprivModel = new KddkiddataModel();
        $restwo = $KidkddAuthprivModel->addYzClientLabel('',$allMember);
        if($restwo['error'] == '0') {
            $data = array();
            $data['yztrade_istoesc'] = 1;
            $data['yztrade_updatetime'] = time();
            $this->Show_css->updateData('ptc_yztrade', "open_id='{$restwo['result']['open_id']}' and yztrade_goodtitle = '堡贝约课' and yztrade_mobile='{$restwo['result']['client_mobile']}' and yztrade_receiver_tel='{$res['result']['receiver_tel']}' ", $data);
        }else{
            $data = array();
            $data['yztrade_istoesc'] = -1;
            $data['yztrade_updatetime'] = time();
            $this->Show_css->updateData('ptc_yztrade', "open_id='{$restwo['result']['open_id']}' and yztrade_goodtitle = '堡贝约课' and yztrade_mobile='{$restwo['result']['client_mobile']}' and yztrade_receiver_tel='{$res['result']['receiver_tel']}' ", $data);
        }

        ajax_return(array('error' => $res['error'], 'errortip' => $res['errortip'],'result' => $res['result']));
    }
    //根据瞿神 查出的购买某课程的名单，同步到课叮铛里面
    function addYztradeToKddScView(){
        $allMember = $this->Show_css->selectOne(" select * from ptc_yz_trade where yztrade_istokdd = '0' and yztrade_goodtitle = '吉的堡上海校外教育展会订金抵用券' and yztrade_mobile <> '' limit 0,1 ");
        if($allMember['yztrade_mobile'] == ''){
            ajax_return(array('error' => 1, 'errortip' => '手机号不能为空','result' => array()));
        }

        $kddAuthprivModel = new KddscdataModel();
        $res = $kddAuthprivModel->addYztradeToKddSc('',$allMember);

        if($res['error'] == '0') {
            $data = array();
            $data['yztrade_istokdd'] = 1;
            $data['yztrade_updatetime'] = time();
            $this->Show_css->updateData('ptc_yz_trade', "open_id='{$res['result']['open_id']}' and yztrade_goodtitle = '吉的堡上海校外教育展会订金抵用券' and yztrade_mobile='{$res['result']['client_mobile']}' ", $data);
        }else{
            $data = array();
            $data['yztrade_istokdd'] = -1;
            $data['yztrade_updatetime'] = time();
            $this->Show_css->updateData('ptc_yz_trade', "open_id='{$res['result']['open_id']}' and yztrade_goodtitle = '吉的堡上海校外教育展会订金抵用券' and yztrade_mobile='{$res['result']['client_mobile']}' ", $data);
        }
        ajax_return(array('error' => $res['error'], 'errortip' => $res['errortip'],'result' => $res['result']));
    }

    //商品管理
    function getYzgoodsListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getYzgoodsList($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "yzgoods_name";
        $field[$k]["fieldname"] = "商品名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "yzgoods_img";
        $field[$k]["fieldname"] = "商品图片";
        $field[$k]["imgVisible"] = 1;
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "yzgoods_vipprice";
        $field[$k]["fieldname"] = "商品价格";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "yzgoods_originalprice";
        $field[$k]["fieldname"] = "划线价格";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "notice";
        $field[$k]["fieldname"] = "积分价格";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "yzgoods_sort";
        $field[$k]["fieldname"] = "排序";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "goodscat_name";
        $field[$k]["fieldname"] = "商品位置";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;


        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取有赞商品息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无有赞商品', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }


    //商品位置管理
    function getGoodscatListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getGoodscatList($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "goodscat_name";
        $field[$k]["fieldname"] = "位置";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "goodscat_note";
        $field[$k]["fieldname"] = "备注";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取商品位置信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无商品位置信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }


    //编辑商品
    function updateYzgoodsView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->updateYzgoods($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "参数设置->商品管理", "编辑商品", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //修改推荐状态
    function updateRecommendView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->updateRecommend($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "参数设置->商品管理", "修改推荐状态", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    function getYzActivtiyView(){
        $Model = new YzapiController();
        $list = $Model->getYzActivtiy(1,50);
        var_dump($list);
    }

    function getCouponDetailView(){
        $Model = new YzapiController();
        $list = $Model->coupondetail();
        var_dump($list);
    }

    function sendcouponView(){
        $request = Input('get.', '', 'trim,addslashes');
        $Model = new YzapiController();
        $a=$Model->sendcoupon(31596986,$request['mobile']);
        var_dump($a);
    }

    function kidRecomView(){
        $request = Input('post.', '', 'trim,addslashes');
        $sdate = "2023-11-20";
        $edate = date("Y-m-d");
        $param = array(
            'company_id' => 8888,
            'start_time' => $sdate,
            'end_time' => $edate
        );
        $getBackurl = request_by_curl("https://escapi.".KDD_RUL."/Api/getEffectiveClient", dataEncode($param), "GET");
        $telist = json_decode($getBackurl, true);

//        var_dump($telist);die;
        if($telist['result']['list']){
            foreach($telist['result']['list'] as &$value){
                $sid = $this->Show_css->getFieldOne("app_student","student_id","student_branch = '{$value['client_stubranch']}'");
                $mobile = $this->Show_css->selectOne("select ms.member_id,m.member_mobile from app_member_student as ms left join app_member as m on m.member_id = ms.member_id where ms.student_id = '{$sid['student_id']}' order by ms.isdefault DESC limit 0,1");
                $data = array();
                $data['student_branch'] = $value['client_stubranch'];
                $data['student_mobile'] = $mobile['member_mobile'];
                $data['client_id'] = $value['client_id'];
                $data['client_cnname'] = $value['client_cnname'];
                $data['client_mobile'] = $value['client_mobile'];
                $data['recomintegral_day'] = $value['client_createtimedate'];
                $data['recomintegral_bookday'] = $value['enroll_date'];
                $data['recomintegral_integral'] = 1000;
                $data['recomintegral_createtime'] = time();
                $this->Show_css->insertData("ptc_student_recomintegral",$data);
            }
        }

    }

    function RecomIntegralAccountView(){
        $list = $this->Show_css->selectClear("select * from ptc_student_recomintegral as r where r.recomintegral_isintegral = '0'");
        if($list){
            foreach ($list as $value){
                $isstu = $this->Show_css->selectOne("select ms.student_id,s.student_cnname from app_member as m left join app_member_student as ms on ms.member_id = m.member_id left join app_student as s on s.student_id = ms.student_id where m.member_mobile = '{$value['client_mobile']}' and s.student_cnname = '{$value['client_cnname']}'");
                if($isstu){
                    $request = Input('post.', '', 'trim,addslashes');

                    $Model = new \Model\Bptapi\PersonalCenterModel($request);
                    $Model->addStudentIntegral('', $isstu['student_id'], 1000, '', '呼朋唤友福利积分', '呼朋唤友福利积分', time(), '', 'kid');
                    $data = array();
                    $data['dynamic_type'] = '其他';
                    $data['dynamic_models'] = '中心优惠通知';
                    $data['dynamic_title'] = "恭喜".$value['client_cnname']."堡贝被推荐成为吉的堡学员！<br/>为你送上1000积分，可在奇趣商城兑换好物哦～";
                    $data['dynamic_listimgurl'] = 'https://oss.kidcastle.cn/manage/202311241438x285319302.png';
                    $data['dynamic_issuername'] = '吉的堡教育';
                    $data['dynamic_createtime'] = time();
                    $data['dynamic_updatetime'] = time();
                    $data['codemodels_id'] = '17';
                    $did = $this->Show_css->insertData("ptc_dynamic",$data);
                    $data = array();
                    $data['dynamic_id'] = $did;
                    $data['student_id'] = $isstu['student_id'];
                    $this->Show_css->insertData("ptc_dynamic_student",$data);
                    $data = array();
                    $data['recomintegral_isintegral'] = '1';
                    $data['recomintegral_integraltime'] = time();
                    $this->Show_css->updateData("ptc_student_recomintegral","recomintegral_id = '{$value['recomintegral_id']}'",$data);
                }

            }
        }

    }

//    function RecomCouponAccountView(){
//        $request = Input('post.', '', 'trim,addslashes');
//
//        $list = $this->Show_css->selectClear("select * from ptc_student_recomintegral as r where r.recomintegral_iscoupon = '0'");
////        var_dump($list);die;
//        if($list){
//            foreach ($list as $value){
//                $sid = $this->Show_css->getFieldOne("app_student","student_id","student_branch = '{$value['student_branch']}'");
//
//                $data = array();
//                $data['dynamic_type'] = '其他';
//                $data['dynamic_models'] = '中心优惠通知';
//                $data['dynamic_title'] = $value['client_cnname']."堡贝转介绍推荐成功！<br/>恭喜你获得「优选好礼」，可前往奇趣商城兑换～";
//                $data['dynamic_listimgurl'] = 'https://oss.kidcastle.cn/manage/202311241453x847790690.png';
//                $data['dynamic_issuername'] = '吉的堡教育';
//                $data['dynamic_createtime'] = time();
//                $data['dynamic_updatetime'] = time();
//                $data['dynamic_content_type'] = 1;
//                $data['codemodels_id'] = '17';
//                $did = $this->Show_css->insertData("ptc_dynamic",$data);
//                $data = array();
//                $data['dynamic_id'] = $did;
//                $data['student_id'] = $sid['student_id'];
//                $this->Show_css->insertData("ptc_dynamic_student",$data);
//                $Model = new YzapiController();
//                $Model->createUser($value['student_mobile']);
//                $Model->sendcoupon(28658092,$value['student_mobile']);
//                $data = array();
//                $data['recomintegral_iscoupon'] = '1';
//                $data['recomintegral_coupontime'] = time();
//                $this->Show_css->updateData("ptc_student_recomintegral","recomintegral_id = '{$value['recomintegral_id']}'",$data);
//
//            }
//        }
//
//    }




    //更新有赞
    function updateGoodsView()
    {
        $request = Input('post.', '', 'trim,addslashes');
//        $this->ThisVerify($request);//验证账户

//        ajax_return(array('error' => 1, 'errortip' => "调试中"));

        $Model = new YzapiController();
        $list = $Model->goodsList(1, 50);

        $item = $list['data']['items'];

//        var_dump($list);die;

        if ($item) {
            foreach ($item as &$val) {
                $data = array();
                $isset = $this->Show_css->getFieldOne("ptc_yzgoods", "yzgoods_id", "yzgoods_outid = '{$val['item_id']}'");
                if ($isset) {
                    $data['yzgoods_outid'] = $val['item_id'];
                    $data['yzgoods_name'] = addslashes($val['title']);
                    $data['yzgoods_isdisplay'] = $val['is_display'];
                    $data['yzgoods_desc'] = $val['summary'];
                    $data['yzgoods_yzupdate'] = substr($val['update_time'], 0, -3);
                    $data['yzgoods_vipprice'] = $val['price'] / 100;
                    if ($val['origin']) {
                        $data['yzgoods_originalprice'] = $val['origin'];
                    } else {
                        $data['yzgoods_originalprice'] = '0';
                    }
                    $data['yzgoods_img'] = $val['images'][0]['image_url'];
                    $oid = $Model->goodsDetail($val['item_id']);
                    $data['yzgoods_tourl'] = str_replace("https://h5.youzan.com/v2/showcase/goods", "packages/goods/detail/index", $oid['data']['detail_url']);
                    $data['yzgoods_updatetime'] = time();

                    $this->Show_css->updateData("ptc_yzgoods", "yzgoods_outid = '{$val['item_id']}'", $data);
                } else {
                    $data['yzgoods_outid'] = $val['item_id'];
                    $data['yzgoods_name'] = addslashes($val['title']);
                    $data['yzgoods_isdisplay'] = $val['is_display'];
                    $data['yzgoods_desc'] = $val['summary'];
                    $data['yzgoods_yzupdate'] = substr($val['update_time'], 0, -3);
                    $data['yzgoods_vipprice'] = $val['price'] / 100;
                    if ($val['origin']) {
                        $data['yzgoods_originalprice'] = $val['origin'];
                    } else {
                        $data['yzgoods_originalprice'] = '0';
                    }
                    $data['yzgoods_img'] = $val['images'][0]['image_url'];
                    $oid = $Model->goodsDetail($val['item_id']);
                    $data['yzgoods_tourl'] = str_replace("https://h5.youzan.com/v2/showcase/goods", "packages/goods/detail/index", $oid['data']['detail_url']);
                    $data['yzgoods_createtime'] = time();

                    $this->Show_css->insertData("ptc_yzgoods", $data);
                }

            }
        }

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "参数设置->商品管理", "同步有赞商品", dataEncode($request));
        ajax_return(array('error' => 0, 'errortip' => "导入成功"));
    }

    function getYzUsersListView()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $Model = new YzapiController();


        for ($i = 150; $i < 201; $i++) {
            $list = $Model->usersList($i, 50);
            $item = $list['data']['record_list'];

            if ($item) {
                foreach ($item as &$val) {
                    $data = array();
                    $data['yzusers_sex'] = $val['gender'];
                    $data['yzusers_ismember'] = $val['is_member'];
                    $data['yzusers_mobile'] = $val['mobile'];
                    $data['yzusersr_createtime'] = $val['created_at'];
                    $data['yzusersr_creatememtime'] = $val['created_member_at'];
                    $data['yz_open_id'] = $val['yz_open_id'];
                    $this->Show_css->insertData("ptc_yzusers", $data);
                }
            }
        }


        ajax_return(array('error' => 0, 'errortip' => "导入成功"));

    }

    function addUserEventView(){
        $request = Input('get.', '', 'trim,addslashes');
        $now= time();
        $time = $now * 1000;
        $Model = new YzapiController();
        $list = $Model->usersEvent($request['openid'],$time,$request['type']);

        var_dump($list);
    }

    function getYzTagsListView()
    {
        $request = Input('get.', '', 'trim,addslashes');

        $Model = new YzapiController();



        $member = $this->Show_css->selectClear("select yzusers_id,yzusers_mobile from ptc_yzusers where yzusers_ismember = '1' and yzusers_status = '0' limit 0,500");




        if($member){
            foreach($member as &$value){
                $list = $Model->usersTag($value['yzusers_mobile']);
                $item = $list['data'];
                $data = array();
                $data['yzusers_status'] = '1';
                $this->Show_css->updateData("ptc_yzusers","yzusers_id = '{$value['yzusers_id']}'", $data);
                if ($item) {
                    foreach ($item as &$val) {
                        $data = array();
                        $data['tag_id'] = $val['tag_id'];
                        $data['tag_name'] = $val['tag_name'];
                        $data['yzusers_id'] = $value['yzusers_id'];
                        $this->Show_css->insertData("ptc_yzusers_tags", $data);

                    }
                }
            }
        }



        ajax_return(array('error' => 0, 'errortip' => "导入成功"));

    }

    function getYzNoticeView(){
        $request = Input('get.', '', 'trim,addslashes');
        $Model = new YzapiController();
        $list = $Model->getNotice();
        var_dump($list);

    }

    //动态推文
    function getDynamicView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getDynamicApi($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "dynamic_title";
        $field[$k]["fieldname"] = "标题";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["fieldwidth"] = 150;
        $k++;

        $field[$k]["fieldstring"] = "dynamic_listimgurl";
        $field[$k]["fieldname"] = "封面图片";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["fieldwidth"] = 60;
        $k++;

        if ($request['dynamic_type'] < '3') {
            $field[$k]["fieldstring"] = "dynamic_tags";
            $field[$k]["fieldname"] = "主题";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $field[$k]["fieldwidth"] = 50;
            $k++;

            $field[$k]["fieldstring"] = "dynamic_models";
            $field[$k]["fieldname"] = "类型";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $field[$k]["fieldwidth"] = 50;
            $k++;
        }

        $field[$k]["fieldstring"] = "read_number";
        $field[$k]["fieldname"] = "浏览人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["fieldwidth"] = 60;
        $k++;

        $field[$k]["fieldstring"] = "read_times";
        $field[$k]["fieldname"] = "浏览人次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["fieldwidth"] = 60;
        $k++;

        $field[$k]["fieldstring"] = "thumb_number";
        $field[$k]["fieldname"] = "点赞数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["fieldwidth"] = 60;
        $k++;

        $field[$k]["fieldstring"] = "comment_number";
        $field[$k]["fieldname"] = "评论数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["clickVisible"] = 1;
        $field[$k]["fieldwidth"] = 60;
        $k++;

        $field[$k]["fieldstring"] = "dynamic_status_name";
        $field[$k]["fieldname"] = "状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["fieldwidth"] = 80;
        $k++;

        $field[$k]["fieldstring"] = "schoolsql_nums";
        $field[$k]["fieldname"] = "发布校园";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["clickVisible"] = 1;
        $field[$k]["fieldwidth"] = 60;
        $k++;

        if ($request['dynamic_type'] != '2' && $request['dynamic_type'] != '6') {
            $field[$k]["fieldstring"] = "coursesql_nums";
            $field[$k]["fieldname"] = "发布课程";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $field[$k]["clickVisible"] = 1;
            $field[$k]["fieldwidth"] = 60;
            $k++;
        }

        $field[$k]["fieldstring"] = "dynamic_updatetime";
        $field[$k]["fieldname"] = "发布日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["fieldwidth"] = 60;
        $k++;

        $field[$k]["fieldstring"] = "organize_cnname";
        $field[$k]["fieldname"] = "发布组织";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["fieldwidth"] = 60;
        $k++;

        $field[$k]["fieldstring"] = "dynamic_passage";
        $field[$k]["fieldname"] = "发布通路";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["fieldwidth"] = 60;
        $k++;

        $field[$k]["fieldstring"] = "dynamic_issuername";
        $field[$k]["fieldname"] = "发布人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["fieldwidth"] = 50;
        $k++;

        $field[$k]["fieldstring"] = "app_url";
        $field[$k]["fieldname"] = "跳转链接";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["fieldwidth"] = 60;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        if ($dataList['list']) {
            $result['list'] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取动态推文成功', 'result' => $result);
        } else {
            $result['list'] = array();
            $res = array('error' => 1, 'errortip' => '暂无动态推文', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //获取推文详情
    function getDynamicDetailView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getDynamicDetailApi($request);

        $result = array();
        $result["list"] = $dataList;
        $res = array('error' => 0, 'errortip' => '获取推文详情信息', 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    //发布推文
    function addDynamicView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->addDynamicApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "校园动态管理->推文发布", "发布推文", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //编辑推文
    function updateDynamicView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->updateDynamicApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "校园动态管理->推文发布", "编辑推文", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //撤回推文
    function setRetractDynamicView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->delDynamicApi($request, '-1');

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "校园动态管理->推文发布", "撤回推文", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //删除推文
    function delDynamicView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->delDynamicApi($request, '-2');

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "校园动态管理->推文发布", "删除推文", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //查看评论
    function getReviewView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getReviewApi($request);

        $result = array();
        $result["list"] = $dataList;
        $res = array('error' => 0, 'errortip' => '获取评论信息', 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    //隐藏/显示评论
    function setReviewView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->setReviewApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "校园动态管理->推文发布", "操作评论状态", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //回复评论
    function addReplyReviewView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $result = $Model->addReplyReviewApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "校园动态管理->推文发布", "回复评论", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    //生日祝福
    function getBirthdayWishesView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getBirthdayWishesApi($request);

        $result = array();
        $result["list"] = $dataList;
        $res = array('error' => 0, 'errortip' => '获取生日祝福信息', 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    //使用/取消使用模板
    function setUseTempView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->setUseTempApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "校园动态管理->生日祝福", "使用/取消使用模板", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //获取行销主题
    function getMarketingThemesView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $datalist = $Model->getMarketingThemesApi($request);

        $result = array();
        $result['list'] = $datalist;
        $res = array('error' => 0, 'errortip' => '获取行销主题成功', 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    //获取预排课计划管理
    function getLessonPlanView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getLessonPlanApi($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "学校名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "month";
        $field[$k]["fieldname"] = "月份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

//        $field[$k]["fieldstring"] = "goodscat_note";
//        $field[$k]["fieldname"] = "预排课进度";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;

        $field[$k]["fieldstring"] = "lessonplan_islock_name";
        $field[$k]["fieldname"] = "排课计划是否锁定进度";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取预排课计划信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无预排课计划信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //批量操作预排课
    function batchLessonPlanView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->batchLessonPlanApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "排课管理->预排课计划设置", "一键锁定/开启预排课计划", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //读书检核设置
    function getReadCheckView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getReadCheckApi($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "times_name";
        $field[$k]["fieldname"] = "课次名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "picbook_num";
        $field[$k]["fieldname"] = "图册数量";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "paragraph_num";
        $field[$k]["fieldname"] = "分段数量";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "readparts_status";
        $field[$k]["fieldname"] = "是否启用";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["switchVisible"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取读书检核信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无读书检核信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //获取教材列表
    function getTextBookView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getTextBookApi($request);

        $result = array();
        if ($dataList) {
            $result["list"] = $dataList;
            $res = array('error' => 0, 'errortip' => '获取教材列表信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无教材列表信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //获取教材图册列表
    function getPicBookView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getPicBookApi($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "minibookpage_img";
        $field[$k]["fieldname"] = "图册图片";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["imgVisible"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "minibookpage_sort";
        $field[$k]["fieldname"] = "图片排序";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "paragraph_num";
        $field[$k]["fieldname"] = "分段数量";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        if ($dataList) {
            $result["list"] = $dataList;
            $res = array('error' => 0, 'errortip' => '获取教材图册信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无教材图册信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //添加读书检核
    function addReadCheckView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->addReadCheckApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->课程相关设置->读书检核", "添加读书检核", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //编辑读书检核
    function updateReadCheckView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->updateReadCheckApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->课程相关设置->读书检核", "编辑读书检核", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //删除读书检核
    function delReadCheckView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->delReadCheckApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->课程相关设置->读书检核", "删除读书检核", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //启用/禁用读书检核
    function usesReadCheckView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->usesReadCheckApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->课程相关设置->读书检核", "启用/禁用读书检核", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //设置家长端展示封面
    function setSowcCverView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->setShowCoverApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->课程相关设置->读书检核", "设置家长端展示封面", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //推送报告设置
    function getPushReportView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getPushReportApi($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "pushreport_name";
        $field[$k]["fieldname"] = "推送课次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        if ($dataList) {
            $result["list"] = $dataList;
            $res = array('error' => 0, 'errortip' => '获取推送报告信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无推送报告信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //添加推送报告
    function addPushReportView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->addPushReportApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->课程相关设置->读书检核", "添加推送报告", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //编辑推送报告
    function updatePushReportView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->updatePushReportApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->课程相关设置->读书检核", "编辑推送报告", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //删除推送报告
    function delPushReportView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->delPushReportApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->课程相关设置->读书检核", "删除推送报告", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //成效登记设置
    function getEffectView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getEffect($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "effect_name";
        $field[$k]["fieldname"] = "成效名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "effect_passage_name";
        $field[$k]["fieldname"] = "所属通路";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "effect_mold_name";
        $field[$k]["fieldname"] = "登记类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "effect_cate";
        $field[$k]["fieldname"] = "分类";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "effect_ismember_name";
        $field[$k]["fieldname"] = "是否在家长端显示";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "effect_type_name";
        $field[$k]["fieldname"] = "录入类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "effect_describe";
        $field[$k]["fieldname"] = "类型描述";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "effect_status";
        $field[$k]["fieldname"] = "是否启用";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["switchVisible"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "effect_updatetime";
        $field[$k]["fieldname"] = "更新时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "name";
        $field[$k]["fieldname"] = "创建人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取成效登记信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无成效登记信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //改变成效启用状态
    function changeEffectStatusView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->changeEffectStatus($request);

        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //新增成效
    function addEffectView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->addEffect($request);

        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //编辑成效
    function updateEffectView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->updateEffect($request);

        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //删除成效
    function delEffectStatusView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->delEffectStatus($request);

        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //分类下拉
    function effectCateListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->effectCateList($request);

        $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $dataList['list']);
        ajax_return($res, $request['language_type']);
    }

    //项目下拉
    function projectListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->projectList($request);

        $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $dataList['list']);
        ajax_return($res, $request['language_type']);
    }

    //项目列表
    function getProjectView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getProject($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "project_name";
        $field[$k]["fieldname"] = "项目名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "num";
        $field[$k]["fieldname"] = "子项目";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "enum";
        $field[$k]["fieldname"] = "适配成效";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "project_sort";
        $field[$k]["fieldname"] = "排序";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "project_status";
        $field[$k]["fieldname"] = "是否启用";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["switchVisible"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $result["max"] = $dataList['max'];
            $res = array('error' => 0, 'errortip' => '获取成效登记信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $result["max"] = $dataList['max'];
            $res = array('error' => 1, 'errortip' => '暂无成效登记信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //新增项目
    function addProjectView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->addProject($request);

        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //编辑项目
    function editProjectView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->editProject($request);

        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //删除项目
    function delProjectView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->delProject($request);

        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //改变项目启用状态
    function changeProjectStatusView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->changeProjectStatus($request);

        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //主题成效列表
    function getThemeEffectView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getThemeEffect($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "effect_name";
        $field[$k]["fieldname"] = "成效名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "effect_cate";
        $field[$k]["fieldname"] = "分类";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "effect_type";
        $field[$k]["fieldname"] = "录入类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "apply_status";
        $field[$k]["fieldname"] = "是否启用";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["switchVisible"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取主题成效登记信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无主题成效登记信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //开启/关闭主题成效登记
    function openEffectApplyView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->openEffectApplyApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->课程相关设置->成效登记设置", "开启/关闭主题成效登记", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //资源类型设置
    function getResourceTypeView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getResourceTypeApi($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "resourcetype_name";
        $field[$k]["fieldname"] = "资源类型名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "resourcetype_passage_name";
        $field[$k]["fieldname"] = "所属通路";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "resourcetype_sort";
        $field[$k]["fieldname"] = "排序";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "resourcetype_classnature_name";
        $field[$k]["fieldname"] = "适配班级属性";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "resourcetype_status";
        $field[$k]["fieldname"] = "是否启用";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["switchVisible"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取资源类型信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无资源类型信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //新增资源类型
    function addResourceTypeView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->addResourceTypeApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->备课相关设置", "新增资源类型", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //编辑资源类型
    function updateResourceTypeView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->updateResourceTypeApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->备课相关设置", "编辑资源类型", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //删除资源类型
    function delResourceTypeView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->delResourceTypeApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->备课相关设置", "删除资源类型", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //开启/关闭资源类型
    function openResourceTypeView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->openResourceTypeApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->备课相关设置", "开启/关闭资源类型", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //获取教学资源
    function getTeachPlanView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getTeachPlanApi($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "teachplan_name";
        $field[$k]["fieldname"] = "教学计划名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "teachplan_passage_name";
        $field[$k]["fieldname"] = "通路";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "resourcetype_name";
        $field[$k]["fieldname"] = "资源类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursetype_cnname";
        $field[$k]["fieldname"] = "班组";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coursecat_cnname";
        $field[$k]["fieldname"] = "班种";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_cnname";
        $field[$k]["fieldname"] = "班别";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_times";
        $field[$k]["fieldname"] = "教学进度";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "teachplan_ratenumber";
        $field[$k]["fieldname"] = "教学计划次数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "teachplan_status";
        $field[$k]["fieldname"] = "是否启用";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["switchVisible"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => "获取教学计划信息", 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => "暂无教学计划", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //添加教学计划
    function addTeachPlanView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->addTeachPlanApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->备课相关设置->教学资源管理", "添加教学计划", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result);
        ajax_return($res, $request['language_type']);
    }

    //编辑教学计划
    function updateTeachPlanView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->updateTeachPlanApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->备课相关设置->教学资源管理", "编辑教学计划", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //删除教学计划
    function delTeachPlanView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->delTeachPlanApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->备课相关设置->教学资源管理", "删除教学计划", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //开启/关闭教学计划
    function openTeachPlanView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->openTeachPlanApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->备课相关设置->教学资源管理", "开启/关闭教学计划", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //获取教学计划进度明细
    function getTeachPlanTimesView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getTeachPlanTimesApi($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "teachplantimes_name";
        $field[$k]["fieldname"] = "教学计划名称明细";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "times_id";
        $field[$k]["fieldname"] = "关联教学进度";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["isSelect"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "teachplantimes_alias";
        $field[$k]["fieldname"] = "教学计划明细别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "content";
        $field[$k]["fieldname"] = "教案内容";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "file_one";
        $field[$k]["fieldname"] = "逐课影片";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "file_two";
        $field[$k]["fieldname"] = "OFFICE档案";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "file_three";
        $field[$k]["fieldname"] = "影音档案";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "file_four";
        $field[$k]["fieldname"] = "图档";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["teachplan"] = $dataList['teachplan'];
        $result["allnum"] = $dataList['allnum'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => "获取教学计划进度明细信息", 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => "暂无教学计划进度明细", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //获取教学计划进度明细内容/资源附件
    function getTeachPlanTimesOneView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getTeachPlanTimesOneApi($request);

        $result = array();
        if ($dataList) {
            $result["list"] = $dataList;
            $res = array('error' => 0, 'errortip' => '获取教学计划进度明细内容信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无教学计划进度明细内容', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //编辑教学计划进度明细内容/资源附件
    function updateTeachPlanTimesView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->updateTeachPlanTimesApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->备课相关设置->教学资源管理", "编辑教学计划进度明细内容/资源附件", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //删除教学计划进度资源附件
    function delTeachPlanTimesFileView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->delTeachPlanTimesFileApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->备课相关设置->教学资源管理", "删除教学计划进度资源附件", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //模组类型设置
    function getModuleTypeView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getModuleTypeApi($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "moduletype_name";
        $field[$k]["fieldname"] = "模组类型名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "moduletype_passage_name";
        $field[$k]["fieldname"] = "所属通路";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "moduletype_status";
        $field[$k]["fieldname"] = "是否启用";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["switchVisible"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取模组类型信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无模组类型信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //新增模组类型
    function addModuleTypeView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->addModuleTypeApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->备课相关设置", "新增模组类型", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //编辑模组类型
    function updateModuleTypeView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->updateModuleTypeApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->备课相关设置", "编辑模组类型", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //删除模组类型
    function delModuleTypeView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->delModuleTypeApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->备课相关设置", "删除模组类型", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //开启/关闭模组类型
    function openModuleTypeView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->openModuleTypeApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->备课相关设置", "开启/关闭模组类型", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //获取备课任务
    function getScheduleTasksView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getScheduleTasksApi($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "scheduletasks_name";
        $field[$k]["fieldname"] = "备课任务名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "scheduletasks_passage_name";
        $field[$k]["fieldname"] = "通路";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "course_cnname";
        $field[$k]["fieldname"] = "班别";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "teachplan_name";
        $field[$k]["fieldname"] = "教学计划名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "moduletype_name";
        $field[$k]["fieldname"] = "所属模组";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "scheduletasks_stdate";
        $field[$k]["fieldname"] = "任务开始时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "scheduletasks_enddate";
        $field[$k]["fieldname"] = "任务结束时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "teachplan_number";
        $field[$k]["fieldname"] = "教学计划数量";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "splschool_nums";
        $field[$k]["fieldname"] = "适配校园";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["clickVisible"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "splpost_nums";
        $field[$k]["fieldname"] = "适配职务";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["clickVisible"] = 1;

        if ($request['type']) {
            $k++;
            $field[$k]["fieldstring"] = "scheduletasks_status";
            $field[$k]["fieldname"] = "是否启用";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $field[$k]["switchVisible"] = 1;

            $k++;
            $field[$k]["fieldstring"] = "scheduletasks_updatetime";
            $field[$k]["fieldname"] = "更新时间";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
        }

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => "获取备课任务信息", 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => "暂无备课任务", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //获取备课任务自由排课
    function getScheduleTasksPlanView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getScheduleTasksPlanApi($request);

        if ($dataList) {
            $result["list"] = $dataList;
            $res = array('error' => 0, 'errortip' => '获取备课任务自由排课信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无备课任务自由排课信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //设置排课计划
    function setScheduleTasksPlanView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->addScheduleTasksPlanApi($request, 1);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "week_name";
        $field[$k]["fieldname"] = "上课周次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "week_num";
        $field[$k]["fieldname"] = "每天排课次数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "week_number";
        $field[$k]["fieldname"] = "排课次数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        if ($dataList) {
            $result["list"] = $dataList;
            $res = array('error' => 0, 'errortip' => '获取排课信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无排课信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //添加备课任务
    function addScheduleTasksView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $result = $Model->addScheduleTasksApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->备课相关设置->教学资源管理", "添加备课任务", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    //编辑备课任务
    function updateScheduleTasksView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->updateScheduleTasksApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->备课相关设置->教学资源管理", "编辑备课任务", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //创建备课任务计划
    function addScheduleTasksPlanView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->addScheduleTasksPlanApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->备课相关设置->教学资源管理", "创建备课任务计划", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //备课任务清除排课
    function clearScheduleTasksPlanView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->clearScheduleTasksPlanApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->备课相关设置->教学资源管理", "备课任务清除排课", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //删除备课任务
    function delScheduleTasksView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->delScheduleTasksApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->备课相关设置->教学资源管理", "删除备课任务", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //开启/关闭备课任务
    function openScheduleTasksView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->openScheduleTasksApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->备课相关设置->教学资源管理", "开启/关闭备课任务", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //获取备课任务计划明细
    function getSchedulePlanView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getSchedulePlanApi($request);

        $result = array();
        $result["scheduletasks"] = $dataList['scheduletasks'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取备课任务计划明细信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无备课任务计划明细', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //调整备课任务排课日期
    function updateScheduleTasksDateView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->updateScheduleTasksDateApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->备课相关设置->教学资源管理", "调整备课任务排课日期", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //获取备课任务安排
    function getSchoolEduleTasksPlanView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getSchoolEduleTasksPlanApi($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "学校名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "学校编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "schooleduletasks_date";
        $field[$k]["fieldname"] = "排课时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "schooleduletasks_number";
        $field[$k]["fieldname"] = "排课次数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "splschool_islock";
        $field[$k]["fieldname"] = "备课是否锁定";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "teacher_number";
        $field[$k]["fieldname"] = "备课老师明细";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["clickVisible"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["scheduletasks"] = $dataList['scheduletasks'];
        $result["allnum"] = $dataList['allnum'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取备课任务安排信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无备课任务安排信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //获取备课任务职务
    function getScheduleTasksPostView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getScheduleTasksPostApi($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "post_name";
        $field[$k]["fieldname"] = "职务名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "post_code";
        $field[$k]["fieldname"] = "职务编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取职务信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无职务信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //获取备课任务教师
    function getScheduleTasksTeacherView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getScheduleTasksTeacherApi($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "teacher_cnname";
        $field[$k]["fieldname"] = "教师姓名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "带班班级";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "post_name";
        $field[$k]["fieldname"] = "职务";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["classList"] = $dataList['classList'];
        $result["allnum"] = $dataList['allnum'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取备课任务教师信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无备课任务教师信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //一键锁定/开启备课任务
    function batchSchoolEdulePlanView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->batchSchoolEdulePlanApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "备课管理->备课任务安排", "一键锁定/开启备课任务", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //教师备课任务查看
    function getTeacherSchoolEdulePlanView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getTeacherSchoolEdulePlanApi($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "schooleduleplan_month";
        $field[$k]["fieldname"] = "月份";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "schooleduleplan_date";
        $field[$k]["fieldname"] = "排课日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "times_name";
        $field[$k]["fieldname"] = "所属进度";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "teachplantimes_name";
        $field[$k]["fieldname"] = "计划明细名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "teachplantimes_number";
        $field[$k]["fieldname"] = "教学计划次数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "teacher_cnname";
        $field[$k]["fieldname"] = "教师姓名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "带班班级";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "workexecute_status_name";
        $field[$k]["fieldname"] = "是否完成备课";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "workexecute_createtime";
        $field[$k]["fieldname"] = "备课教案首次提交时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "workexecute_updatetime";
        $field[$k]["fieldname"] = "备课教案更新时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["scheduletasks"] = $dataList['scheduletasks'];
        $result["classList"] = $dataList['classList'];
        $result["allnum"] = $dataList['allnum'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取备课任务查看信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无备课任务查看信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //获取主管备课任务计划明细
    function getSchoolEdulePlanView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getSchoolEdulePlanApi($request);

        $result = array();
        $result["scheduletasks"] = $dataList['scheduletasks'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取主管备课任务计划明细信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无主管备课任务计划明细', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //考试类型设置
    function getExamTypeView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getExamTypeApi($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "examtype_name";
        $field[$k]["fieldname"] = "考试类型名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "examtype_passage_name";
        $field[$k]["fieldname"] = "所属通路";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "examtype_status";
        $field[$k]["fieldname"] = "是否启用";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["switchVisible"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取考试类型信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无考试类型信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //新增考试类型
    function addExamTypeView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->addExamTypeApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->成绩相关设置", "新增考试类型", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //编辑考试类型
    function updateExamTypeView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->updateExamTypeApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->成绩相关设置", "编辑考试类型", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //删除考试类型
    function delExamTypeView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->delExamTypeApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->成绩相关设置", "删除考试类型", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //开启/关闭考试类型
    function openExamTypeView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->openExamTypeApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->成绩相关设置", "开启/关闭考试类型", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //获取考试计划
    function getExamPlanView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getExamPlanApi($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "examplan_name";
        $field[$k]["fieldname"] = "考试计划名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "examtype_name";
        $field[$k]["fieldname"] = "考试类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "examplan_passage_name";
        $field[$k]["fieldname"] = "通路";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "examplan_enrollrole_name";
        $field[$k]["fieldname"] = "登记角色";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "examplan_from_name";
        $field[$k]["fieldname"] = "来源";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "examplan_stdate";
        $field[$k]["fieldname"] = "考试开始时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "examplan_enddate";
        $field[$k]["fieldname"] = "考试结束时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "evaluatact_name";
        $field[$k]["fieldname"] = "考试活动";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "splschool_nums";
        $field[$k]["fieldname"] = "适用校园";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["clickVisible"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_typenums";
        $field[$k]["fieldname"] = "适用学生类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["clickVisible"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "examplan_status";
        $field[$k]["fieldname"] = "是否启用";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["switchVisible"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "centre_score";
        $field[$k]["fieldname"] = "中心成绩";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["clickVisible"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => "获取考试计划信息", 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => "暂无考试计划", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //添加考试计划
    function addExamPlanView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->addExamPlanApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->成绩相关设置", "添加考试计划", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //编辑考试计划
    function updateExamPlanView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->updateExamPlanApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->成绩相关设置", "编辑考试计划", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //删除考试计划
    function delExamPlanView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->delExamPlanApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->成绩相关设置", "删除考试计划", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //开启/关闭考试计划
    function openExamPlanView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->openExamPlanApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->成绩相关设置", "开启/关闭考试计划", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //获取考试计划适配班级
    function getExamSplClassView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getExamSplClassApi($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "所属校园";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校园编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取考试计划适配班级信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无考试计划适配班级信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //获取考试计划适配学员
    function getExamSplStudentView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getExamSplStudentApi($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "所属校园";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校园编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        $result["classList"] = $dataList['classList'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取考试计划适配学员信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无考试计划适配学员信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //考试计划适配班别
    function applySplCourseView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->applySplCourseApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->成绩相关设置", "考试计划适配班别", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //获取考试活动
    function getEvaluatActView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getEvaluatActApi($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "evaluatact_name";
        $field[$k]["fieldname"] = "活动名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "evaluatact_date";
        $field[$k]["fieldname"] = "活动时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "evaluatact_passage_name";
        $field[$k]["fieldname"] = "通路";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "examtype_name";
        $field[$k]["fieldname"] = "活动类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "evaluatact_bannerurl";
        $field[$k]["fieldname"] = "活动banner";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "evaluatact_type_name";
        $field[$k]["fieldname"] = "测评模式";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "evaluatact_scope_name";
        $field[$k]["fieldname"] = "测评范围";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "evaluatact_tothestu";
        $field[$k]["fieldname"] = "考生须知";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "evaluatact_status";
        $field[$k]["fieldname"] = "是否启用";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["switchVisible"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => "获取考试活动信息", 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => "暂无考试活动", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //添加考试活动
    function addEvaluatActView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->addEvaluatActApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "考试活动设置->考试活动管理", "添加考试活动", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //编辑考试活动
    function updateEvaluatActView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->updateEvaluatActApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "考试活动设置->考试活动管理", "编辑考试活动", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //删除考试活动
    function delEvaluatActView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->delEvaluatActApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "考试活动设置->考试活动管理", "删除考试活动", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //开启/关闭考试活动
    function openEvaluatActView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->openEvaluatActApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "考试活动设置->考试活动管理", "开启/关闭考试活动", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //获取考试级别
    function getEvaluatLevelView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getEvaluatLevelApi($request);

        $evaluatact = $this->Show_css->getFieldOne("staes_evaluatact", "evaluatact_passage,evaluatact_name,evaluatact_type,evaluatact_mode,evaluatact_isupdate", "evaluatact_id = '{$request['evaluatact_id']}'");

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "evaluatlevel_name";
        $field[$k]["fieldname"] = "级别名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["fieldwidth"] = 180;
        $k++;

        $field[$k]["fieldstring"] = "evaluatlevel_content";
        $field[$k]["fieldname"] = "级别说明";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        if ($evaluatact['evaluatact_type'] > 1) {
            $field[$k]["fieldstring"] = "evaluatlevel_isshowscore_name";
            $field[$k]["fieldname"] = "是否显示成绩";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "evaluatlevel_status";
            $field[$k]["fieldname"] = "是否启用";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $field[$k]["switchVisible"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "evaluatlevel_release";
            $field[$k]["fieldname"] = "正式发布";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $field[$k]["switchVisible"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "times_name";
            $field[$k]["fieldname"] = "周次";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
        }

        $field[$k]["fieldstring"] = "splcourse_nums";
        $field[$k]["fieldname"] = "适配班别";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "evaluatpaper_name";
        $field[$k]["fieldname"] = "适配试卷";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        $result['evaluatact'] = $evaluatact;
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => "获取考试级别信息", 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => "暂无考试级别", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //获取考试试卷
    function getEvaluatPaperView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getEvaluatPaperApi($request);

        $result = array();
        if ($dataList) {
            $result["list"] = $dataList;
            $res = array('error' => 0, 'errortip' => "获取考试试卷信息", 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => "暂无考试试卷", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //添加考试级别
    function addEvaluatLevelView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->addEvaluatLevelApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "考试活动设置->考试活动管理->考试级别管理", "添加考试级别", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //编辑考试级别
    function updateEvaluatLevelView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->updateEvaluatLevelApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "考试活动设置->考试活动管理->考试级别管理", "编辑考试级别", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //删除考试级别
    function delEvaluatLevelView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->delEvaluatLevelApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "考试活动设置->考试活动管理->考试级别管理", "删除考试级别", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //启用/禁用考试级别
    function openEvaluatLevelView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->openEvaluatLevelApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "考试活动设置->考试活动管理->考试级别管理", "启用/禁用考试级别", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //正式发布考试级别
    function releaseEvaluatLevelView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->releaseEvaluatLevelApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "考试活动设置->考试活动管理->考试级别管理", "正式发布考试级别", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //获取考试考场
    function getEvaluatRoomView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getEvaluatRoomApi($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "evaluatroom_cnname";
        $field[$k]["fieldname"] = "考场名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "evaluatroom_address";
        $field[$k]["fieldname"] = "考场地址";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "evaluatroom_username";
        $field[$k]["fieldname"] = "考场登录账号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "evaluatroom_userpasswd";
        $field[$k]["fieldname"] = "考场登录密码";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "splschool_nums";
        $field[$k]["fieldname"] = "考场校区";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["clickVisible"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "splstudent_nums";
        $field[$k]["fieldname"] = "考场学员";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["clickVisible"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        $result['evaluatact'] = $dataList['evaluatact'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => "获取考试考场信息", 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => "暂无考试考场", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //添加考试考场
    function addEvaluatRoomView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->addEvaluatRoomApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "考试活动设置->考试活动管理->考场设置", "添加考试考场", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //编辑考试考场
    function updateEvaluatRoomView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->updateEvaluatRoomApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "考试活动设置->考试活动管理->考场设置", "编辑考试考场", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //删除考试考场
    function delEvaluatRoomView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->delEvaluatRoomApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "考试活动设置->考试活动管理->考场设置", "删除考试考场", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //获取考场学员
    function getEvaluatStuView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        if (empty($request['is_download'])) {
            $Model = new SettingsModel($request);
            $dataList = $Model->getEvaluatStuApi($request);

            $field = array();
            $k = 0;
            $field[$k]["fieldstring"] = "student_cnname";
            $field[$k]["fieldname"] = "学员姓名";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "attend_status";
            $field[$k]["fieldname"] = "是否签到";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "evaluatexam_score";
            $field[$k]["fieldname"] = "考场得分";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "feelog_num";
            $field[$k]["fieldname"] = "疑题数量";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "evaluatlevel_name";
            $field[$k]["fieldname"] = "报名级别";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "school_cnname";
            $field[$k]["fieldname"] = "校区名称";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "school_branch";
            $field[$k]["fieldname"] = "校区编号";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "class_branch";
            $field[$k]["fieldname"] = "班级编号";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "student_branch";
            $field[$k]["fieldname"] = "学员编号";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "student_examcard";
            $field[$k]["fieldname"] = "闯关编号";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "student_reachtime";
            $field[$k]["fieldname"] = "报到时间";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "student_evaltime";
            $field[$k]["fieldname"] = "评鉴时间";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;

            $result = array();
            $result["field"] = $field;
            $result["allnum"] = $dataList['allnum'];
            $result["classList"] = $dataList['classList'];
            $result["levelList"] = $dataList['levelList'];
            if ($dataList['list']) {
                $result["list"] = $dataList['list'];
                $res = array('error' => 0, 'errortip' => "获取考场学员信息", 'result' => $result);
            } else {
                $result["list"] = array();
                $res = array('error' => 1, 'errortip' => "暂无考场学员", 'result' => $result);
            }
            ajax_return($res, $request['language_type']);
        } else {
            $excelheader = array("校区名称", "校区编号", "报名级别", "班级编号", "学员编号", "学员姓名", "考试日期", "考试时间", "考试教室", "报到时间");
            $excelfileds = array('school_cnname', 'school_branch', 'bmlevel_name', 'class_branch', 'student_branch', 'student_cnname', 'exam_date', 'exam_time', 'exam_room', 'student_reachtime');

            $tem_name = "考场导入学员表.xlsx";
            query_to_excel($excelheader, array(), $excelfileds, $tem_name);
            exit;
        }
    }

    //添加考场学员
    function addEvaluatStuView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->addEvaluatStuApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "考试活动设置->考试活动管理->考场设置", "添加考场学员", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //编辑考场学员
    function updateEvaluatStuView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->updateEvaluatStuApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "考试活动设置->考试活动管理->考场设置", "编辑考场学员", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //删除考场学员
    function delEvaluatStuView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $student_list = json_decode(stripslashes($request['student_list']), true);
        if ($student_list) {
            foreach ($student_list as $value) {
                $Model->delEvaluatStuApi($value);
            }
        } else {
            $Model->error = 1;
            $Model->errortip = "请选择需要删除的考场学员";
        }

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "考试活动设置->考试活动管理->考场设置", "删除考场学员", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //导入考场学员信息
    function ImportEvaluatStuView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $url = $request['url'];

        $array = array('校区名称' => 'school_cnname', '校区编号' => 'school_branch', '报名级别' => 'bmlevel_name', '班级编号' => 'class_branch', '学员编号' => 'student_branch', '学员姓名' => 'student_cnname', '考试日期' => 'exam_date', '考试时间' => 'exam_time', '考试教室' => 'exam_room', '报到时间' => 'student_reachtime');

        $options = array(
            "ssl" => array(
                "verify_peer" => false,
                "verify_peer_name" => false,
            ),
        );
        file_put_contents('analysis.xlsx', file_get_contents($url, false, stream_context_create($options)));
        $sqlarray = execl_to_array("analysis.xlsx", $array);
        array_shift($sqlarray);

        $falarray = array();
        $scharray = array();
        if ($sqlarray) {
            foreach ($sqlarray as $key => &$item) {
                $item = array_map('trim', $item);
                if ($item['school_branch'] && $item['student_cnname'] && $item['student_branch'] && $item['class_branch']) {
                    if (!in_array($item['school_branch'], array_column($scharray, 'school_branch'))) {
                        $scharray[$key]['school_branch'] = $item['school_branch'];
                    }
                    $item['student_evaltime'] = $item['exam_date'].' '.$item['exam_time'].' '.$item['exam_room'];
                    $falarray[$key]['school_cnname'] = $item['school_cnname'];
                    $falarray[$key]['school_branch'] = $item['school_branch'];
                    $falarray[$key]['bmlevel_name'] = $item['bmlevel_name'];
                    $falarray[$key]['class_branch'] = $item['class_branch'];
                    $falarray[$key]['student_branch'] = $item['student_branch'];
                    $falarray[$key]['student_cnname'] = $item['student_cnname'];
                    $falarray[$key]['student_evaltime'] = $item['student_evaltime'];
                    $falarray[$key]['student_reachtime'] = $item['student_reachtime'];
                    $reason = '';
                    if (!$this->Show_css->getOne("app_school", "school_branch = '{$item['school_branch']}'")) {
                        $reason .= '校园编号错误';
                    }
                    if (!$this->Show_css->getOne("eas_classes", "class_branch = '{$item['class_branch']}'")) {
                        $reason .= '班级编号错误，';
                    } else {
                        $class = $this->Show_css->getOne("eas_classes", "class_branch = '{$item['class_branch']}'");
                        $item['class_cnname'] = $class['class_cnname'];
                        $item['class_enname'] = $class['class_enname'];
                    }
                    if (!$this->Show_css->getOne("app_student", "student_branch = '{$item['student_branch']}'")) {
                        $reason .= '学员编号错误，';
                    }
                    if (!$this->Show_css->getOne("staes_evaluatact_evaluatlevel", "evaluatact_id = '{$request['evaluatact_id']}' and evaluatlevel_name = '{$item['bmlevel_name']}'")) {
                        $reason .= '报名级别错误，';
                    }
                    $falarray[$key]['reason'] = rtrim($reason, '，');
                    if ($reason == '') {
                        unset($falarray[$key]);
                    } else {
                        unset($sqlarray[$key]);
                        unset($scharray[$key]);
                    }
                } else {
                    unset($sqlarray[$key]);
                }
            }

            if ($request['is_export']) {
                if ($falarray) {
                    $outexceldate = array();
                    foreach ($falarray as $falvar) {
                        $datearray = array();
                        $datearray['school_cnname'] = $falvar['school_cnname'];
                        $datearray['school_branch'] = $falvar['school_branch'];
                        $datearray['bmlevel_name'] = $falvar['bmlevel_name'];
                        $datearray['class_branch'] = $falvar['class_branch'];
                        $datearray['student_branch'] = $falvar['student_branch'];
                        $datearray['student_cnname'] = $falvar['student_cnname'];
                        $datearray['student_evaltime'] = $falvar['student_evaltime'];
                        $datearray['student_reachtime'] = $falvar['student_reachtime'];
                        $datearray['reason'] = $falvar['reason'];
                        $outexceldate[] = $datearray;
                    }

                    $excelheader = array("校区名称", "校区编号", "报名级别", "班级编号", "学员编号", "学员姓名", "评鉴时间", "报到时间", "失败原因");
                    $excelfileds = array('school_cnname', 'school_branch', 'bmlevel_name', 'class_branch', 'student_branch', 'student_cnname', 'student_evaltime', 'student_reachtime', 'reason');

                    $tem_name = "考场导入学员失败记录表.xlsx";
                    query_to_excel($excelheader, $outexceldate, $excelfileds, $tem_name);
                    exit;
                }
            }

            if ($request['is_import']) {
                if ($sqlarray) {
                    foreach ($sqlarray as $val) {
                        if ($request['evaluatroom_id']) {
                            $this->Show_css->delData("staes_evaluatact_evaluatlevel_splstudent", "evaluatroom_id = '{$request['evaluatroom_id']}' and student_branch = '{$val['student_branch']}'");
                        }
                        if ($request['evaluatschool_id']) {
                            $this->Show_css->delData("staes_evaluatact_evaluatlevel_splstudent", "evaluatschool_id = '{$request['evaluatschool_id']}' and student_branch = '{$val['student_branch']}'");
                        }
                        $levelOne = $this->Show_css->getFieldOne("staes_evaluatact_evaluatlevel", "evaluatlevel_id", "evaluatact_id = '{$request['evaluatact_id']}' and evaluatlevel_name = '{$val['bmlevel_name']}'");
                        $data = array();
                        $data['evaluatlevel_id'] = $levelOne['evaluatlevel_id'];
                        if ($request['evaluatroom_id']) {
                            $data['evaluatroom_id'] = $request['evaluatroom_id'];
                        }
                        if ($request['evaluatschool_id']) {
                            $data['evaluatschool_id'] = $request['evaluatschool_id'];
                        }
                        $data['school_branch'] = $val['school_branch'];
                        $data['class_branch'] = $val['class_branch'];
                        $data['student_branch'] = $val['student_branch'];
                        $data['student_examcard'] = $val['student_branch'] . '-' . rand(10000, 99999);
                        $data['student_evaltime'] = $val['student_evaltime'];
                        $data['student_reachtime'] = $val['student_reachtime'];
                        $data['splstudent_createtime'] = time();
                        $this->Show_css->insertData("staes_evaluatact_evaluatlevel_splstudent", $data);
                    }
                    if ($scharray && $request['evaluatroom_id']) {
                        foreach ($scharray as $v) {
                            $school = $this->Show_css->getFieldOne("app_school", "school_id", "school_branch = '{$v['school_branch']}'");
                            if ($school && !$this->Show_css->getOne("staes_evaluatact_evaluatroom_splschool", "evaluatroom_id = '{$request['evaluatroom_id']}' and school_id = '{$school['school_id']}'")) {
                                $apply = array();
                                $apply['evaluatroom_id'] = $request['evaluatroom_id'];
                                $apply['school_id'] = $school['school_id'];
                                $this->Show_css->insertData("staes_evaluatact_evaluatroom_splschool", $apply);
                            }
                        }
                    }
                }
            }
        }

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = 'student_cnname';
        $field[$k]["fieldname"] = "学员姓名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = 'student_branch';
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = 'class_cnname';
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = 'class_enname';
        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = 'class_branch';
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "所属校园";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校园编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $falfield = $field;

        $k++;
        $falfield[$k]["fieldstring"] = "reason";
        $falfield[$k]["fieldname"] = "失败原因";
        $falfield[$k]["show"] = 1;
        $falfield[$k]["custom"] = 1;

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['falfield'] = $falfield;
        $result['suclist'] = $sqlarray;
        $result['fallist'] = $falarray;

        $res = array('error' => 0, 'errortip' => '导入成功', 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    //获取考试校点
    function getEvaluatSchoolView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getEvaluatSchoolApi($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "evaluatschool_cnname";
        $field[$k]["fieldname"] = "校点名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "evaluatschool_address";
        $field[$k]["fieldname"] = "校点地址";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "evaluatschool_username";
        $field[$k]["fieldname"] = "校点登录账号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "evaluatschool_userpasswd";
        $field[$k]["fieldname"] = "校点登录密码";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "splclass_nums";
        $field[$k]["fieldname"] = "校点班级";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["clickVisible"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "splstudent_nums";
        $field[$k]["fieldname"] = "校点学员";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["clickVisible"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        $result['evaluatact'] = $dataList['evaluatact'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => "获取考试校点信息", 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => "暂无考试校点", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //添加考试校点
    function addEvaluatSchoolView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->addEvaluatSchoolApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "考试活动设置->考试活动管理->校点设置", "添加考试校点", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //编辑考试校点
    function updateEvaluatSchoolView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->updateEvaluatSchoolApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "考试活动设置->考试活动管理->校点设置", "编辑考试校点", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //删除考试校点
    function delEvaluatSchoolView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->delEvaluatSchoolApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "考试活动设置->考试活动管理->校点设置", "删除考试校点", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //获取校点班级
    function getEvaluatClassView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getEvaluatClassApi($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "is_select";
        $field[$k]["fieldname"] = "状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级全称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_enname";
        $field[$k]["fieldname"] = "班级简称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "evaluatlevel_id";
        $field[$k]["fieldname"] = "考试级别";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["customType"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => "获取校点班级信息", 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => "暂无校点班级", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //添加校点班级
    function addEvaluatClassView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->addEvaluatClassApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "考试活动设置->考试活动管理->校点设置", "添加校点班级", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //获取校点学员
    function getEvaluatScStuView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getEvaluatScStuApi($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "is_select";
        $field[$k]["fieldname"] = "状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "study_status";
        $field[$k]["fieldname"] = "在读状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "evaluatexam_score";
        $field[$k]["fieldname"] = "学员成绩";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "evaluatlevel_id";
        $field[$k]["fieldname"] = "考试级别";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["customType"] = 1;
        $field[$k]["fieldwidth"] = 200;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => "获取校点学员信息", 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => "暂无校点学员", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //添加校点学员
    function addEvaluatScStuView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->addEvaluatScStuApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "考试活动设置->考试活动管理->校点设置", "添加校点学员", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //疑题改分
    function getCheckPaperView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getCheckPaperApi($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = 'student_cnname';
        $field[$k]["fieldname"] = "学员名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = 'student_branch';
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "number_id";
        $field[$k]["fieldname"] = "考题序号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = 'question_pid';
        $field[$k]["fieldname"] = "题目编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = 'category_name';
        $field[$k]["fieldname"] = "题目类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        if (!empty($request['evaluatact_id'])) {
            $field[$k]["fieldstring"] = 'exam_venue';
            $field[$k]["fieldname"] = "所属考场";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
        } else {
            $field[$k]["fieldstring"] = 'school_cnname';
            $field[$k]["fieldname"] = "所属学校";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
            $field[$k]["fieldstring"] = 'evaluatlevel_type_name';
            $field[$k]["fieldname"] = "所属考试类型";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
        }

        $field[$k]["fieldstring"] = 'evaluatpaper_name';
        $field[$k]["fieldname"] = "所属试卷";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = 'question_content';
        $field[$k]["fieldname"] = "配音答案";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = 'answers_url';
        $field[$k]["fieldname"] = "用户录音";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["questionAudioVisible"] = 1;
        $k++;

        $field[$k]["fieldstring"] = 'feelog_type';
        $field[$k]["fieldname"] = "反馈类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = 'evalitem_weighting';
        $field[$k]["fieldname"] = "云知声打分";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = 'topic_score';
        $field[$k]["fieldname"] = "题目得分";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = 'feelog_createtime';
        $field[$k]["fieldname"] = "提交时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        $result["levelList"] = $dataList['levelList'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无记录', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //获取操作记录
    function getChangeLogView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        if (!empty($request['evaluatexam_id'])) {
            $examOne = $this->Show_css->getFieldOne("staes_evaluatexam", "evaluatpaper_id", "evaluatexam_id = '{$request['evaluatexam_id']}'");
            $request['evaluatpaper_id'] = $examOne['evaluatpaper_id'];
        }

        $Model = new SettingsModel($request);
        $dataList = $Model->getChangeLogApi($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "number_id";
        $field[$k]["fieldname"] = "序号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        if (!empty($request['feelog_id'])) {
            $field[$k]["fieldstring"] = 'old_topicscore';
            $field[$k]["fieldname"] = "修改前分值";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
            $field[$k]["fieldstring"] = 'new_topicscore';
            $field[$k]["fieldname"] = "修改后分值";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
        } else {
            $enteritems = $this->Show_css->selectClear("SELECT b.section_id,concat(b.section_name,'（', 0+cast(a.section_score as char), '%）') as section_name FROM staes_evaluatpaper_offlineset a,staes_evaluatpaper_section b WHERE a.section_id = b.section_id and a.evaluatpaper_id = '{$request['evaluatpaper_id']}' ORDER BY a.section_sort");
            if ($enteritems) {
                foreach ($enteritems as $key => $value) {
                    $field[$k]["fieldstring"] = "section_value".$key;
                    $field[$k]["fieldname"] = $value['section_name'];
                    $field[$k]["show"] = 1;
                    $field[$k]["custom"] = 1;
                    $k++;
                }
            }
            $field[$k]["fieldstring"] = 'old_examscores';
            $field[$k]["fieldname"] = "修改前成绩";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
            $field[$k]["fieldstring"] = "new_examscores";
            $field[$k]["fieldname"] = "修改后成绩";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
        }

        $field[$k]["fieldstring"] = "teacher_cnname";
        $field[$k]["fieldname"] = "修改人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = 'changelog_createtime';
        $field[$k]["fieldname"] = "修改时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        if ($dataList) {
            $result["list"] = $dataList;
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无记录', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //禁止修改成绩
    function banUpdateScoreView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->banUpdateScoreApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "考试活动设置->考试活动管理->疑题改分", "禁止修改成绩", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //修改考试成绩
    function updateScoreView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $feelog_list = json_decode(stripslashes($request['feelog_list']), true);
        if ($feelog_list) {
            foreach ($feelog_list as $value) {
                $value['teacher_id'] = $request['teacher_id'];
                $Model->updateScoreApi($value);
            }
        } else {
            $Model->error = 1;
            $Model->errortip = "请选择需要修改的反馈记录";
        }

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "考试活动设置->考试活动管理->疑题改分", "修改考试成绩", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //添加音频播放记录
    function addPlayLogView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->addPlayLogApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "考试活动设置->考试活动管理->疑题改分", "添加音频播放记录", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //获取考场活动成绩登记
    function getStuScoreEnrollView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getStuScoreEnrollApi($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "number_id";
        $field[$k]["fieldname"] = "序号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["fieldfixed"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员姓名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["fieldfixed"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "evaluatroom_cnname";
        $field[$k]["fieldname"] = "所属考场";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "所属学校";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "学校编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "所属班级";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "evaluatlevel_name";
        $field[$k]["fieldname"] = "考试级别";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "evaluatexam_isattend";
        $field[$k]["fieldname"] = "是否出勤";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["switchVisible"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "evaluatexam_score";
        $field[$k]["fieldname"] = "总分";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $enteritems = $this->Show_css->selectClear("SELECT b.section_id,concat(b.section_name,'（', 0+cast(a.section_score as char), '%）') as section_name FROM staes_evaluatpaper_offlineset a,staes_evaluatpaper_section b WHERE a.section_id = b.section_id and a.evaluatpaper_id = '{$dataList['examplan']['evaluatpaper_oid']}' ORDER BY a.section_sort");
        if ($enteritems) {
            foreach ($enteritems as $key => $value) {
                $k++;
                $field[$k]["fieldstring"] = "section_value".$key;
                $field[$k]["fieldname"] = $value['section_name'];
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
                $field[$k]["customType"] = 1;
                $field[$k]["section_id"] = $value['section_id'];
            }
        }

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        $result["examplan"] = $dataList['examplan'];
        $result['roomList'] = $dataList['roomList'];
        $result['schoolList'] = $dataList['schoolList'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取学员成绩登记信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无学员成绩登记', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //录入学员成绩
    function enterStuScoreView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->enterStuScoreApi($request, 1);

        $this->addWorkLog($this->postbeOne['organize_id'], $request['school_id'], $request['teacher_id'], "考试活动设置->考试活动级别管理->阅卷", "录入学员成绩", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //修改出勤状态
    function editAttendExamStatusView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->enterStuScoreApi($request, 0);

        $this->addWorkLog($this->postbeOne['organize_id'], $request['school_id'], $request['teacher_id'], "考试活动设置->考试活动级别管理->阅卷", "修改出勤状态", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //下载学员导入模板
    function getImportStuRoomTempView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $request['is_export'] = 1;
        $request['is_import'] = 1;
        $Model = new SettingsModel($request);
        $Model->getStuScoreEnrollApi($request);

        $res = array('error' => 0, 'errortip' => '下载导入模版成功', 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //导入学员成绩信息
    function ImportStuRoomEnrollView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $url = $request['url'];

        $stu_array = array('序号' => 'number_id', '学员姓名' => 'student_cnname', '学员编号' => 'student_branch', '所属学校' => 'school_cnname', '学校编号' => 'school_branch', '所属班级' => 'class_cnname', '班级编号' => 'class_branch', '考试级别' => 'evaluatlevel_name', '是否出勤' => 'is_attendexam');

        $enteritems = $this->Show_css->selectClear("SELECT concat(b.section_name,'（', 0+cast(a.section_score as char), '%）') as section_name FROM staes_evaluatpaper_offlineset a,staes_evaluatpaper_section b WHERE a.section_id = b.section_id and a.evaluatpaper_id = '{$request['evaluatpaper_oid']}' ORDER BY a.section_sort");
        if ($enteritems) {
            foreach ($enteritems as $key => $value) {
                $stu_array[$value['section_name']] = "section_value{$key}";
            }
        }

        $options = array(
            "ssl" => array(
                "verify_peer" => false,
                "verify_peer_name" => false,
            ),
        );
        file_put_contents('analysis.xlsx', file_get_contents($url, false, stream_context_create($options)));
        $sqlarray = execl_to_array("analysis.xlsx", $stu_array);
        array_shift($sqlarray);

        $array_stu = array_flip($stu_array);
        $falarray = array();
        $comarray = array();
        if ($sqlarray) {
            foreach ($sqlarray as $key => &$item) {
                $item = array_map('trim', $item);
                $item['total_score'] = 0;
                if ($item['student_cnname'] && $item['student_branch'] && $item['is_attendexam']) {
                    $falarray[$key]['number_id'] = $item['number_id'];
                    $falarray[$key]['student_cnname'] = $item['student_cnname'];
                    $falarray[$key]['student_branch'] = $item['student_branch'];
                    $falarray[$key]['school_cnname'] = $item['school_cnname'];
                    $falarray[$key]['school_branch'] = $item['school_branch'];
                    $falarray[$key]['class_cnname'] = $item['class_cnname'];
                    $falarray[$key]['class_branch'] = $item['class_branch'];
                    $falarray[$key]['evaluatlevel_name'] = $item['evaluatlevel_name'];
                    $falarray[$key]['is_attendexam'] = $item['is_attendexam'];
                    if (in_array($item['is_attendexam'], array("是", "否"))) {
                        $item['evaluatexam_isattend'] = $item['is_attendexam'] == '是' ? 1 : 0;
                        $reason = '';
                    } else {
                        $reason = '出勤有误，';
                    }
                    $classOne = $this->Show_css->getFieldOne("eas_classes", "class_id", "class_branch = '{$item['class_branch']}'");
                    $item['class_id'] = $classOne['class_id'];
                    $studentOne = $this->Show_css->getFieldOne("app_student", "student_id", "student_branch = '{$item['student_branch']}'");
                    $item['student_id'] = $studentOne['student_id'];
                    foreach ($enteritems as $k => $v) {
                        $falarray[$key]['section_value' . $k] = $item['section_value' . $k];
                        $section_name = $array_stu['section_value' . $k];
                        $itemOne = $this->Show_css->getOne("staes_evaluatpaper_offlineset a,staes_evaluatpaper_section b", "a.section_id = b.section_id and a.evaluatpaper_id = '{$request['evaluatpaper_oid']}' and b.section_name = '{$section_name}'");
                        if (!$itemOne) {
                            $res = array('error' => 1, 'errortip' => '导出模板发生改变，请重新下载模板', 'result' => array());
                            ajax_return($res, $request['language_type']);
                        }
                        $comarray[$key][$k]['section_id'] = $itemOne['section_id'];
                        if (!is_numeric($item['section_value'.$k])) {
                            $reason .= '成绩必须为数值，';
                        }
                        if ($itemOne['section_score'] < $item['section_value'.$k]) {
                            $reason .= '成绩大于满分，';
                        } else {
                            $examOne = $this->Show_css->selectOne("SELECT evaluatexam_id FROM staes_evaluatexam WHERE evaluatlevel_id = '{$request['evaluatlevel_id']}' and evaluatpaper_id = '{$request['evaluatpaper_oid']}' and class_id = '{$item['class_id']}' and student_id = '{$item['student_id']}' and evaluatexam_class = 1 and evaluatexam_papertime > 0 LIMIT 1");
                            if ($examOne) {
                                $item['evaluatexam_id'] = $examOne['evaluatexam_id'];
                                $offline = $this->Show_css->getFieldOne("staes_evaluatexam_offline", "offline_id,section_score", "evaluatexam_id = '{$examOne['evaluatexam_id']}' and section_id = '{$itemOne['section_id']}'");
                                $comarray[$key][$k]['old_score'] = $offline['section_score'] ?: 0;
                            } else {
                                $comarray[$key][$k]['old_score'] = 0;
                            }
                            $comarray[$key][$k]['new_score'] = $item['section_value'.$k];
                        }
                    }

                    $falarray[$key]['reason'] = rtrim($reason, '，');
                    if ($reason == '') {
                        unset($falarray[$key]);
                    } else {
                        unset($sqlarray[$key]);
                    }
                } else {
                    unset($sqlarray[$key]);
                }
            }

            if ($request['is_export']) {
                if ($falarray) {
                    $outexceldate = array();
                    foreach ($falarray as $falvar) {
                        $datearray = array();
                        $datearray['number_id'] = $falvar['number_id'];
                        $datearray['student_cnname'] = $falvar['student_cnname'];
                        $datearray['student_branch'] = $falvar['student_branch'];
                        $datearray['school_cnname'] = $falvar['school_cnname'];
                        $datearray['school_branch'] = $falvar['school_branch'];
                        $datearray['class_cnname'] = $falvar['class_cnname'];
                        $datearray['class_branch'] = $falvar['class_branch'];
                        $datearray['evaluatlevel_name'] = $falvar['evaluatlevel_name'];
                        $datearray['is_attendexam'] = $falvar['is_attendexam'];
                        $itemnum = $this->Show_css->getCount("staes_evaluatpaper_offlineset a,staes_evaluatpaper_section b", "a.section_id = b.section_id and a.evaluatpaper_id = '{$request['evaluatpaper_oid']}'");
                        for ($i = 0; $i < $itemnum; $i++) {
                            $datearray['section_value' . $i] = $falvar['section_value' . $i];
                        }
                        $datearray['reason'] = $falvar['reason'];
                        $outexceldate[] = $datearray;
                    }

                    $k = 0;
                    $excelheader[$k] = "序号";
                    $excelfileds[$k] = 'number_id';
                    $k++;
                    $excelheader[$k] = "学员姓名";
                    $excelfileds[$k] = 'student_cnname';
                    $k++;
                    $excelheader[$k] = "学员编号";
                    $excelfileds[$k] = 'student_branch';
                    $k++;
                    $excelheader[$k] = "所属学校";
                    $excelfileds[$k] = 'school_cnname';
                    $k++;
                    $excelheader[$k] = "学校编号";
                    $excelfileds[$k] = 'school_branch';
                    $k++;
                    $excelheader[$k] = "所属班级";
                    $excelfileds[$k] = 'class_cnname';
                    $k++;
                    $excelheader[$k] = "班级编号";
                    $excelfileds[$k] = 'class_branch';
                    $k++;
                    $excelheader[$k] = "考试级别";
                    $excelfileds[$k] = 'evaluatlevel_name';
                    $k++;
                    $excelheader[$k] = "是否出勤";
                    $excelfileds[$k] = 'is_attendexam';
                    $k++;
                    if ($enteritems) {
                        foreach ($enteritems as $key => $val) {
                            $excelheader[$k] = $val['section_name'];
                            $excelfileds[$k] = "section_value" . $key;
                            $k++;
                        }
                    }
                    $excelheader[$k] = "失败原因";
                    $excelfileds[$k] = 'reason';

                    $tem_name = "学生成绩登记失败记录表.xlsx";
                    query_to_excel($excelheader, $outexceldate, $excelfileds, $tem_name);
                    exit;
                }
            }

            if ($request['is_import']) {
                if ($sqlarray && $comarray) {
                    foreach ($sqlarray as $key => $val) {
                        $request['class_id'] = $val['class_id'];
                        $request['student_id'] = $val['student_id'];
                        $request['evaluatexam_isattend'] = $val['evaluatexam_isattend'];
                        foreach ($comarray[$key] as $v) {
                            $request['section_id'] = $v['section_id'];
                            $request['old_score'] = $v['old_score'];
                            $request['new_score'] = $v['new_score'];
                            $Model = new SettingsModel($request);
                            $Model->enterStuScoreApi($request, $val['evaluatexam_isattend']);
                        }
                    }
                }
            }
        }

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = 'number_id';
        $field[$k]["fieldname"] = "序号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员姓名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = 'school_cnname';
        $field[$k]["fieldname"] = "所属学校";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = 'school_branch';
        $field[$k]["fieldname"] = "学校编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = 'class_cnname';
        $field[$k]["fieldname"] = "所属班级";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = 'class_branch';
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = 'evaluatlevel_name';
        $field[$k]["fieldname"] = "考试级别";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "is_attendexam";
        $field[$k]["fieldname"] = "是否出勤";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        if ($enteritems) {
            foreach ($enteritems as $key => $value) {
                $k++;
                $field[$k]["fieldstring"] = "section_value".$key;
                $field[$k]["fieldname"] = $value['enteritems_name'];
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
            }
        }
        $falfield = $field;

        $k++;
        $falfield[$k]["fieldstring"] = "reason";
        $falfield[$k]["fieldname"] = "失败原因";
        $falfield[$k]["show"] = 1;
        $falfield[$k]["custom"] = 1;

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['falfield'] = $falfield;
        $result['suclist'] = $sqlarray;
        $result['fallist'] = $falarray;

        $res = array('error' => 0, 'errortip' => '导入成功', 'result' => $result);
        ajax_return($res, $request['language_type']);
    }


    //获取检核交叉账号
    function getIntersectTeacherView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getIntersectTeacher($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "teacher_cnname";
        $field[$k]["fieldname"] = "教师姓名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "teacher_branch";
        $field[$k]["fieldname"] = "教师编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "teacher_mobile";
        $field[$k]["fieldname"] = "手机号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;
//
//        $field[$k]["fieldstring"] = "examplan_enrollrole_name";
//        $field[$k]["fieldname"] = "在职状态";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;

        $field[$k]["fieldstring"] = "postnum";
        $field[$k]["fieldname"] = "职务";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "schoolnum";
        $field[$k]["fieldname"] = "交叉检核校园数量";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school";
        $field[$k]["fieldname"] = "交叉检核校园";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["clickVisible"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => "暂无信息", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //获取教师任职
    function getTeacherPostbeView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getTeacherPostbe($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "post_id";
        $field[$k]["fieldname"] = "职务ID";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "type";
        $field[$k]["fieldname"] = "职务类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "校园名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "organize_cnname";
        $field[$k]["fieldname"] = "所属组织";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "post_name";
        $field[$k]["fieldname"] = "职务名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "postrole_name";
        $field[$k]["fieldname"] = "角色名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "postbe_status";
        $field[$k]["fieldname"] = "任职状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "postbe_ismianjob";
        $field[$k]["fieldname"] = "是否主职";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["clickVisible"] = 1;

        $result = array();
        $result["field"] = $field;
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => "获取成功", 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => "暂无信息", 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }


    //查询教师信息
    function queryTeacherInfoView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->queryTeacherInfo($request);

        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => $Model->result);
        ajax_return($res, $request['language_type']);
    }

    //添加学校
    function addSchoolView(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new SettingsModel($request);
        $res = $Model->addSchool($request);
        ajax_return($res, $request['language_type']);
    }

    //移除交叉教师
    function delIntersectTeacherView(){
        $request = Input('post.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $Model = new SettingsModel($request);
        $res = $Model->delIntersectTeacher($request);
        ajax_return($res, $request['language_type']);
    }

    //下载导入模板
    function dowExportTempView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        if ($request['type'] == 'class') {
            $excelheader = array("班级名称", "班级别名", "班级编号");
            $excelfileds = array('class_cnname', 'class_enname', 'class_branch');

            $tem_name = "考试计划适配班级表.xlsx";
        } elseif ($request['type'] == 'student') {
            $excelheader = array("学员姓名", "学员编号", "班级名称", "班级别名", "班级编号");
            $excelfileds = array('student_cnname', 'student_branch', 'class_cnname', 'class_enname', 'class_branch');

            $tem_name = "考试计划适配学员表.xlsx";
        }
        query_to_excel($excelheader, array(), $excelfileds, $tem_name);
        exit;
    }

    //下载学员成绩导入模板
    function dowStuScoreTempView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $request['is_export'] = 1;
        $request['is_import'] = 1;
        $Model = new SettingsModel($request);
        $Model->getExamSplStudentApi($request);

        $res = array('error' => 0, 'errortip' => '下载学员成绩导入模板成功', 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //导入适配班级信息
    function ImportSplClassView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $url = $request['url'];

        $array = array('班级名称' => 'class_cnname', '班级别名' => 'class_enname', '班级编号' => 'class_branch');

        $options = array(
            "ssl" => array(
                "verify_peer" => false,
                "verify_peer_name" => false,
            ),
        );
        file_put_contents('analysis.xlsx', file_get_contents($url, false, stream_context_create($options)));
        $sqlarray = execl_to_array("analysis.xlsx", $array);
        array_shift($sqlarray);

        $examplan = $this->Show_css->getFieldOne("eas_examplan", "examplan_isallschool", "examplan_id = '{$request['examplan_id']}'");

        $falarray = array();
        if ($sqlarray) {
            foreach ($sqlarray as $key => &$item) {
                $item = array_map('trim', $item);
                if ($item['class_cnname'] && $item['class_branch']) {
                    $falarray[$key]['class_cnname'] = $item['class_cnname'];
                    $falarray[$key]['class_enname'] = $item['class_enname'];
                    $falarray[$key]['class_branch'] = $item['class_branch'];
                    $school = $this->Show_css->selectOne("SELECT s.school_id,s.school_cnname,s.school_branch FROM app_school as s,eas_classes as c WHERE s.school_branch = c.school_branch AND c.class_branch = '{$item['class_branch']}'");
                    $falarray[$key]['school_cnname'] = $item['school_cnname'] = $school['school_cnname'];
                    $falarray[$key]['school_branch'] = $item['school_branch'] = $school['school_branch'];
                    $reason = '';
                    if (!$this->Show_css->getOne("eas_classes", "class_branch = '{$item['class_branch']}'")) {
                        $reason .= '班级编号错误，';
                    }
                    if ($examplan['examplan_isallschool'] == '1' && !$this->Show_css->getOne("eas_examplan_splschool", "examplan_id = '{$request['examplan_id']}' and school_id = '{$school['school_id']}'")) {
                        $reason .= '校园编号错误';
                    }
                    $falarray[$key]['reason'] = rtrim($reason, '，');
                    if ($reason == '') {
                        unset($falarray[$key]);
                    } else {
                        unset($sqlarray[$key]);
                    }
                } else {
                    unset($sqlarray[$key]);
                }
            }

            if ($request['is_export']) {
                if ($falarray) {
                    $outexceldate = array();
                    foreach ($falarray as $falvar) {
                        $datearray = array();
                        $datearray['class_cnname'] = $falvar['class_cnname'];
                        $datearray['class_enname'] = $falvar['class_enname'];
                        $datearray['class_branch'] = $falvar['class_branch'];
                        $datearray['school_cnname'] = $falvar['school_cnname'];
                        $datearray['school_branch'] = $falvar['school_branch'];
                        $datearray['reason'] = $falvar['reason'];
                        $outexceldate[] = $datearray;
                    }

                    $excelheader = array("班级名称", "班级别名", "班级编号", "所属校园", "校园编号", "失败原因");
                    $excelfileds = array('class_cnname', 'class_enname', 'class_branch', 'school_cnname', 'school_branch', 'reason');

                    $tem_name = "考试计划适配班级失败记录表.xlsx";
                    query_to_excel($excelheader, $outexceldate, $excelfileds, $tem_name);
                    exit;
                }
            }

            if ($request['is_import']) {
                if ($sqlarray) {
                    $this->Show_css->delData("eas_examplan_splclass", "examplan_id = '{$request['examplan_id']}'");
                    foreach ($sqlarray as $val) {
                        $data = array();
                        $data['examplan_id'] = $request['examplan_id'];
                        $data['class_branch'] = $val['class_branch'];
                        $this->Show_css->insertData("eas_examplan_splclass", $data);
                    }
                }
            }
        }

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = 'class_cnname';
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = 'class_enname';
        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = 'class_branch';
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "所属校园";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校园编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $falfield = $field;

        $k++;
        $falfield[$k]["fieldstring"] = "reason";
        $falfield[$k]["fieldname"] = "失败原因";
        $falfield[$k]["show"] = 1;
        $falfield[$k]["custom"] = 1;

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['falfield'] = $falfield;
        $result['suclist'] = $sqlarray;
        $result['fallist'] = $falarray;

        $res = array('error' => 0, 'errortip' => '导入成功', 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    //导入适配学员信息
    function ImportSplStudentView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $url = $request['url'];

        $array = array('学员姓名' => 'student_cnname', '学员编号' => 'student_branch', '班级名称' => 'class_cnname', '班级别名' => 'class_enname', '班级编号' => 'class_branch');

        $options = array(
            "ssl" => array(
                "verify_peer" => false,
                "verify_peer_name" => false,
            ),
        );
        file_put_contents('analysis.xlsx', file_get_contents($url, false, stream_context_create($options)));
        $sqlarray = execl_to_array("analysis.xlsx", $array);
        array_shift($sqlarray);

        $examplan = $this->Show_css->getFieldOne("eas_examplan", "examplan_enrollrole,examplan_isallschool", "examplan_id = '{$request['examplan_id']}'");

        $falarray = array();
        if ($sqlarray) {
            foreach ($sqlarray as $key => &$item) {
                $item = array_map('trim', $item);
                if ($item['student_cnname'] && $item['student_branch'] && $item['class_cnname'] && $item['class_branch']) {
                    $falarray[$key]['student_cnname'] = $item['student_cnname'];
                    $falarray[$key]['student_branch'] = $item['student_branch'];
                    $falarray[$key]['class_cnname'] = $item['class_cnname'];
                    $falarray[$key]['class_enname'] = $item['class_enname'];
                    $falarray[$key]['class_branch'] = $item['class_branch'];
                    $school = $this->Show_css->selectOne("SELECT s.school_id,s.school_cnname,s.school_branch FROM app_school as s,eas_classes as c WHERE s.school_branch = c.school_branch AND c.class_branch = '{$item['class_branch']}'");
                    $falarray[$key]['school_cnname'] = $item['school_cnname'] = $school['school_cnname'];
                    $falarray[$key]['school_branch'] = $item['school_branch'] = $school['school_branch'];
                    $reason = '';
                    if (!$this->Show_css->getOne("app_student", "student_branch = '{$item['student_branch']}'")) {
                        $reason .= '学员编号错误，';
                    }
                    if (!$this->Show_css->getOne("eas_classes", "class_branch = '{$item['class_branch']}'")) {
                        $reason .= '班级编号错误，';
                    }
                    if ($examplan['examplan_enrollrole'] == '1' && $examplan['examplan_isallschool'] == '1' && !$this->Show_css->getOne("eas_examplan_splschool", "examplan_id = '{$request['examplan_id']}' and school_id = '{$school['school_id']}'")) {
                        $reason .= '校园编号错误';
                    }
                    $falarray[$key]['reason'] = rtrim($reason, '，');
                    if ($reason == '') {
                        unset($falarray[$key]);
                    } else {
                        unset($sqlarray[$key]);
                    }
                } else {
                    unset($sqlarray[$key]);
                }
            }

            if ($request['is_export']) {
                if ($falarray) {
                    $outexceldate = array();
                    foreach ($falarray as $falvar) {
                        $datearray = array();
                        $datearray['student_cnname'] = $falvar['student_cnname'];
                        $datearray['student_branch'] = $falvar['student_branch'];
                        $datearray['class_cnname'] = $falvar['class_cnname'];
                        $datearray['class_enname'] = $falvar['class_enname'];
                        $datearray['class_branch'] = $falvar['class_branch'];
                        $datearray['school_cnname'] = $falvar['school_cnname'];
                        $datearray['school_branch'] = $falvar['school_branch'];
                        $datearray['reason'] = $falvar['reason'];
                        $outexceldate[] = $datearray;
                    }

                    $excelheader = array("学员姓名", "学员编号", "班级名称", "班级别名", "班级编号", "所属校园", "校园编号", "失败原因");
                    $excelfileds = array('student_cnname', 'student_branch', 'class_cnname', 'class_enname', 'class_branch', 'school_cnname', 'school_branch', 'reason');

                    $tem_name = "考试计划适配学员失败记录表.xlsx";
                    query_to_excel($excelheader, $outexceldate, $excelfileds, $tem_name);
                    exit;
                }
            }

            if ($request['is_import']) {
                if ($sqlarray) {
                    $this->Show_css->delData("eas_examplan_splstudent", "examplan_id = '{$request['examplan_id']}'");
                    foreach ($sqlarray as $val) {
                        $data = array();
                        $data['examplan_id'] = $request['examplan_id'];
                        $data['student_branch'] = $val['student_branch'];
                        $this->Show_css->insertData("eas_examplan_splstudent", $data);
                    }
                }
            }
        }

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = 'student_cnname';
        $field[$k]["fieldname"] = "学员姓名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = 'student_branch';
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = 'class_cnname';
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = 'class_enname';
        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = 'class_branch';
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "所属校园";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "school_branch";
        $field[$k]["fieldname"] = "校园编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $falfield = $field;

        $k++;
        $falfield[$k]["fieldstring"] = "reason";
        $falfield[$k]["fieldname"] = "失败原因";
        $falfield[$k]["show"] = 1;
        $falfield[$k]["custom"] = 1;

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['falfield'] = $falfield;
        $result['suclist'] = $sqlarray;
        $result['fallist'] = $falarray;

        $res = array('error' => 0, 'errortip' => '导入成功', 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    //导入学员成绩信息
    function ImportStuScoEnrollView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $url = $request['url'];

        $stu_array = array('学员姓名' => 'student_cnname', '学员编号' => 'student_branch', '班级名称' => 'class_cnname', '班级别名' => 'class_enname', '班级编号' => 'class_branch', '所属校园' => 'school_cnname', '校园编号' => 'school_branch', '是否缺考' => 'is_lackexam');

        $enteritems = $this->Show_css->selectClear("SELECT enteritems_name FROM eas_examplan_enteritems WHERE examplan_id = '{$request['examplan_id']}' ORDER BY enteritems_sort");
        if ($enteritems) {
            foreach ($enteritems as $key => $value) {
                $stu_array[$value['enteritems_name']] = "scoenrollitem_value{$key}";
            }
        }
        $examplan = $this->Show_css->getFieldOne("eas_examplan", "examplan_studenttype,examplan_enrollrole,examplan_isallschool,examplan_lackisresit,examplan_isresit,examplan_minpassscore", "examplan_id = '{$request['examplan_id']}'");
        if (!$examplan['examplan_lackisresit'] && !$examplan['examplan_isresit']) {
            $request['is_needresit'] = 0;
        } else {
            $request['is_needresit'] = 1;
        }

        $options = array(
            "ssl" => array(
                "verify_peer" => false,
                "verify_peer_name" => false,
            ),
        );
        file_put_contents('analysis.xlsx', file_get_contents($url, false, stream_context_create($options)));
        $sqlarray = execl_to_array("analysis.xlsx", $stu_array);
        array_shift($sqlarray);

        $array_stu = array_flip($stu_array);
        $falarray = array();
        $comarray = array();
        if ($sqlarray) {
            foreach ($sqlarray as $key => &$item) {
                $item = array_map('trim', $item);
                $item['total_score'] = 0;
                if ($item['student_cnname'] && $item['student_branch'] && $item['class_cnname'] && $item['class_branch']) {
                    $falarray[$key]['student_cnname'] = $item['student_cnname'];
                    $falarray[$key]['student_branch'] = $item['student_branch'];
                    $falarray[$key]['class_cnname'] = $item['class_cnname'];
                    $falarray[$key]['class_enname'] = $item['class_enname'];
                    $falarray[$key]['class_branch'] = $item['class_branch'];
                    $falarray[$key]['school_cnname'] = $item['school_cnname'];
                    $falarray[$key]['school_branch'] = $item['school_branch'];
                    $reason = '';
                    if (!$this->Show_css->getOne("app_student", "student_branch = '{$item['student_branch']}'")) {
                        $reason .= '学员编号错误，';
                    }
                    if (!$this->Show_css->getOne("eas_classes", "class_branch = '{$item['class_branch']}'")) {
                        $reason .= '班级编号错误，';
                    }
                    $school = $this->Show_css->getFieldOne("app_school", "school_id", "school_branch = '{$item['school_branch']}'");
                    if ($examplan['examplan_enrollrole'] == '1' && $examplan['examplan_isallschool'] == '1' && !$this->Show_css->getOne("eas_examplan_splschool", "examplan_id = '{$request['examplan_id']}' and school_id = '{$school['school_id']}'")) {
                        $reason .= '校园编号错误，';
                    }
                    if ($examplan['examplan_studenttype'] == 0 && !$this->Show_css->selectOne("select 1 from eas_examplan_splcourse as co,eas_classes as c where co.course_branch = c.course_branch and co.examplan_id = '{$request['examplan_id']}' and c.class_branch = '{$item['class_branch']}'")) {
                        $reason .= '未适配的班别，';
                    }
                    if ($examplan['examplan_studenttype'] == 1 && !$this->Show_css->getOne("eas_examplan_splclass", "examplan_id = '{$request['examplan_id']}' and class_branch = '{$item['class_branch']}'")) {
                        $reason .= '未适配的班级，';
                    }
                    if ($examplan['examplan_studenttype'] == 2 && !$this->Show_css->getOne("eas_examplan_splstudent", "examplan_id = '{$request['examplan_id']}' and student_branch = '{$item['student_branch']}'")) {
                        $reason .= '未适配的学员，';
                    }
                    if ($item['is_lackexam'] == '否') {
                        foreach ($enteritems as $k => $v) {
                            $falarray[$key]['scoenrollitem_value' . $k] = $item['scoenrollitem_value' . $k];
                            $enteritems_name = $array_stu['scoenrollitem_value' . $k];
                            $itemOne = $this->Show_css->getOne("eas_examplan_enteritems", "examplan_id = '{$request['examplan_id']}' and enteritems_name = '{$enteritems_name}'");
                            if (!$itemOne) {
                                $res = array('error' => 1, 'errortip' => '导出模板发生改变，请重新下载模板', 'result' => array());
                                ajax_return($res, $request['language_type']);
                            }
                            $comarray[$key][$k]['enteritems_id'] = $itemOne['enteritems_id'];
                            $comarray[$key][$k]['enteritems_class'] = $itemOne['enteritems_class'];
                            $comarray[$key][$k]['enteritems_type'] = $itemOne['enteritems_type'];
                            if ($itemOne['enteritems_type'] == 0) {
                                if (!is_numeric($item['scoenrollitem_value'.$k])) {
                                    $reason .= '成绩必须为数值，';
                                }
                                if ($itemOne['enteritems_maxscore'] < $item['scoenrollitem_value'.$k]) {
                                    $reason .= '成绩大于满分，';
                                } else {
                                    $item['total_score'] += $item['scoenrollitem_value'.$k];
                                    $comarray[$key][$k]['score'] = $item['scoenrollitem_value'.$k];
                                }
                            } elseif ($itemOne['enteritems_type'] == 1) {
                                $optionList = json_decode($itemOne['enteritems_jsontext'], true);
                                if ($optionList) {
                                    if (in_array($item['scoenrollitem_value'.$k], array_column($optionList, 'value'))) {
                                        foreach ($optionList as $val) {
                                            if ($val['value'] == $item['scoenrollitem_value'.$k]) {
                                                $item['total_score'] += $val['score'];
                                                $comarray[$key][$k]['score'] = $val['score'];
                                                $comarray[$key][$k]['value'] = $val['value'];
                                            }
                                        }
                                    } else {
                                        $reason .= '该选项不存在，';
                                    }
                                }
                            } elseif ($itemOne['enteritems_type'] == 2) {
                                if (strlen($item['scoenrollitem_value'.$k]) > 40) {
                                    $reason .= '文本过长，';
                                } else {
                                    $comarray[$key][$k]['value'] = $item['scoenrollitem_value'.$k];
                                }
                            } elseif ($itemOne['enteritems_type'] == 3) {
                                if (strpos($item['scoenrollitem_value'.$k], "t") !== false) {
                                    $time = intval((str_replace("t", "", $item['scoenrollitem_value'.$k]) - 25569) * 24*60*60);
                                    $date = date("Y-m-d", $time);
                                    if (!isDateValid($date)) {
                                        $reason .= '日期格式错误，';
                                        $falarray[$key]['scoenrollitem_value' . $k] = $date;
                                    } else {
                                        $comarray[$key][$k]['value'] = $date;
                                        $item['scoenrollitem_value'.$k] = $date;
                                    }
                                } else {
                                    $time = str_replace(['年','月','日','-','.'],'/', $item['scoenrollitem_value'.$k]);
                                    $times = strtotime($time);
                                    if ($times) {
                                        $date = date("Y-m-d", $times);
                                        $comarray[$key][$k]['value'] = $date;
                                        $item['scoenrollitem_value'.$k] = $date;
                                    } else {
                                        $time = intval(($item['scoenrollitem_value'.$k] - 25569) * 24*60*60);
                                        $date = date("Y-m-d", $time);
                                        if (!isDateValid($date)) {
                                            $reason .= '日期格式错误，';
                                            $falarray[$key]['scoenrollitem_value' . $k] = $date;
                                        } else {
                                            $comarray[$key][$k]['value'] = $date;
                                            $item['scoenrollitem_value'.$k] = $date;
                                        }
                                    }
                                }
                            }
                        }
                    }

                    $falarray[$key]['reason'] = rtrim($reason, '，');
                    if ($reason == '') {
                        unset($falarray[$key]);
                    } else {
                        unset($sqlarray[$key]);
                    }
                } else {
                    unset($sqlarray[$key]);
                }
            }

            if ($request['is_export']) {
                if ($falarray) {
                    $outexceldate = array();
                    foreach ($falarray as $falvar) {
                        $datearray = array();
                        $datearray['student_cnname'] = $falvar['student_cnname'];
                        $datearray['student_branch'] = $falvar['student_branch'];
                        $datearray['class_cnname'] = $falvar['class_cnname'];
                        $datearray['class_enname'] = $falvar['class_enname'];
                        $datearray['class_branch'] = $falvar['class_branch'];
                        $datearray['school_cnname'] = $falvar['school_cnname'];
                        $datearray['school_branch'] = $falvar['school_branch'];
                        $itemnum = $this->Show_css->getCount("eas_examplan_enteritems", "examplan_id = '{$request['examplan_id']}'");
                        for ($i = 0; $i < $itemnum; $i++) {
                            $datearray['scoenrollitem_value' . $i] = $falvar['scoenrollitem_value' . $i];
                        }
                        $datearray['reason'] = $falvar['reason'];
                        $outexceldate[] = $datearray;
                    }

                    $k = 0;
                    $excelheader[$k] = "学员姓名";
                    $excelfileds[$k] = 'student_cnname';
                    $k++;
                    $excelheader[$k] = "学员编号";
                    $excelfileds[$k] = 'student_branch';
                    $k++;
                    $excelheader[$k] = "班级名称";
                    $excelfileds[$k] = 'class_cnname';
                    $k++;
                    $excelheader[$k] = "班级别名";
                    $excelfileds[$k] = 'class_enname';
                    $k++;
                    $excelheader[$k] = "班级编号";
                    $excelfileds[$k] = 'class_branch';
                    $k++;
                    $excelheader[$k] = "所属校园";
                    $excelfileds[$k] = 'school_cnname';
                    $k++;
                    $excelheader[$k] = "校园编号";
                    $excelfileds[$k] = 'school_branch';
                    $k++;
                    if ($enteritems) {
                        foreach ($enteritems as $key => $val) {
                            $excelheader[$k] = $val['enteritems_name'];
                            $excelfileds[$k] = "scoenrollitem_value" . $key;
                            $k++;
                        }
                    }
                    $excelheader[$k] = "失败原因";
                    $excelfileds[$k] = 'reason';

                    $tem_name = "学生成绩登记失败记录表.xlsx";
                    query_to_excel($excelheader, $outexceldate, $excelfileds, $tem_name);
                    exit;
                }
            }

            if ($request['is_import']) {
                if ($sqlarray) {
                    foreach ($sqlarray as $key => $val) {
                        $school = $this->Show_css->getFieldOne("app_school", "school_id", "school_branch = '{$val['school_branch']}'");
                        $class = $this->Show_css->getFieldOne("eas_classes", "class_id", "class_branch = '{$val['class_branch']}'");
                        $student = $this->Show_css->getFieldOne("app_student", "student_id", "student_branch = '{$val['student_branch']}'");
                        $request['school_id'] = $school['school_id'] ?: 3393;
                        $request['class_id'] = $class['class_id'];
                        $request['student_id'] = $student['student_id'];
                        $request['scoenroll_score'] = $val['total_score'];
                        if ($val['is_lackexam'] == '否') {
                            $request['enteritems_list'] = $comarray[$key];
                        }
                        $Model = new \Model\Teasx\ClassHourModel($request);
                        $Model->operateScoEnroll($request);
                    }
                }
            }
        }

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学员姓名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = 'class_cnname';
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = 'class_enname';
        $field[$k]["fieldname"] = "班级别名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = 'class_branch';
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = 'school_cnname';
        $field[$k]["fieldname"] = "所属校园";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = 'school_branch';
        $field[$k]["fieldname"] = "校园编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        if ($enteritems) {
            foreach ($enteritems as $key => $value) {
                $k++;
                $field[$k]["fieldstring"] = "scoenrollitem_value".$key;
                $field[$k]["fieldname"] = $value['enteritems_name'];
                $field[$k]["show"] = 1;
                $field[$k]["custom"] = 1;
            }
        }
        $falfield = $field;

        $k++;
        $falfield[$k]["fieldstring"] = "reason";
        $falfield[$k]["fieldname"] = "失败原因";
        $falfield[$k]["show"] = 1;
        $falfield[$k]["custom"] = 1;

        $result = array();
        $result['fieldcustom'] = 0;
        $result['field'] = $field;
        $result['falfield'] = $falfield;
        $result['suclist'] = $sqlarray;
        $result['fallist'] = $falarray;

        $res = array('error' => 0, 'errortip' => '导入成功', 'result' => $result);
        ajax_return($res, $request['language_type']);
    }



    //单元主题检测
    function getUnitthemeView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getUnitthemeApi($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "unittheme_cnname";
        $field[$k]["fieldname"] = "单元/主题检测名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "range";
        $field[$k]["fieldname"] = "检测区间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "unittheme_num";
        $field[$k]["fieldname"] = "检测题目数量";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取单元主题检测信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无单元主题检测信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }


    //添加单元主题检测
    function addUnitthemeView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->addUnitthemeApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->课程相关设置->读书检核", "添加单元主题检测", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //编辑单元主题检测
    function updateUnitthemeView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->updateUnitthemeApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->课程相关设置->读书检核", "编辑单元主题检测", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //删除单元主题检测
    function delUnitthemeView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->delUnitthemeApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "教务设置->课程相关设置->读书检核", "删除单元主题检测", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    function testView(){
        $data = $this->Show_css->selectClear("select r.paragraph_id,r.minibookpage_id from eas_course_times_minibookpage_readparts as r where r.course_branch = 'FC1'");

        // 获取需要抽样的数量
        $sampleSize = 10;

        // 创建一个空数组存放抽样结果
        $samples = [];

        while (count($samples) < $sampleSize) {
            // 生成一个在[0, count($data)-1]之间的随机索引值
            $index = rand(0, count($data) - 1);

            // 将对应位置上的元素添加到抽样结果数组中
            if (!in_array($data[$index], $samples)) {
                $samples[] = $data[$index];
            }
        }

        if($samples){
            foreach($samples as $val){
                $data = array();
                $data['course_branch'] = 'FC1';
                $data['minibookpage_id'] = $val['minibookpage_id'];
                $data['paragraph_id'] = $val['paragraph_id'];
                $this->Show_css->insertData("eas_course_times_minibookpage_themeparts",$data);
            }
        }

        print_r($samples);
    }

    //类型下拉
    function getMiniTypeView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getMiniType($request);

        $result = array();
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无数据', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }


    //堡贝通app功能区配置
    function getNavigationListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getNavigationList($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "navigation_title";
        $field[$k]["fieldname"] = "功能名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "navigation_class_name";
        $field[$k]["fieldname"] = "所属用户类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "navigation_passage";
        $field[$k]["fieldname"] = "所属通路";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "navigation_imgurl";
        $field[$k]["fieldname"] = "图标";
        $field[$k]["imgVisible"] = 1;
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "navigation_link";
        $field[$k]["fieldname"] = "链接";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "navigation_sort";
        $field[$k]["fieldname"] = "排序";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "navigation_linktype_name";
        $field[$k]["fieldname"] = "跳转类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "navigation_islogin";
        $field[$k]["fieldname"] = "仅登录查详";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["switchVisible"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "navigation_istest";
        $field[$k]["fieldname"] = "仅测试可见";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["switchVisible"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "navigation_status";
        $field[$k]["fieldname"] = "是否启用";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["switchVisible"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无数据', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }


    //新增金刚区
    function addNavigationView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->addNavigation($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "堡贝通APP配置", "新增金刚区", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //编辑金刚区
    function updateNavigationView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->updateNavigation($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "堡贝通APP配置", "编辑金刚区", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //编辑金刚区状态
    function updateNavigationStatusView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->updateNavigationStatus($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "堡贝通APP配置", "编辑金刚区状态", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }


    //删除金刚区
    function delNavigationStatusView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->delNavigationStatus($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "堡贝通APP配置", "删除金刚区", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }


    //新人福利相关配置
    function getCouponListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getCouponList($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "coupon_imgurl";
        $field[$k]["fieldname"] = "奖励图片";
        $field[$k]["imgVisible"] = 1;
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coupon_name";
        $field[$k]["fieldname"] = "标题";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coupon_describe";
        $field[$k]["fieldname"] = "描述";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coupon_linktype_name";
        $field[$k]["fieldname"] = "跳转类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coupon_sort";
        $field[$k]["fieldname"] = "排序";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "coupon_status";
        $field[$k]["fieldname"] = "是否开启";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["switchVisible"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无数据', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }



    //新增福利相关配置
    function addCouponView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->addCoupon($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "新增福利相关配置", "新增", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //编辑福利相关配置
    function updateCouponView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->updateCoupon($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "新增福利相关配置", "编辑", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //编辑福利相关配置状态
    function updateCouponStatusView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->updateCouponStatus($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "新增福利相关配置", "编辑状态", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }


    //删除福利相关配置
    function delCouponStatusView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->delCouponStatus($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "新增福利相关配置", "删除", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }


    //帮助分类设置列表
    function getHelpcateListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getHelpcateList($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "helpcate_name";
        $field[$k]["fieldname"] = "分类名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "helpcate_class_name";
        $field[$k]["fieldname"] = "所属用户类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "helpcate_passage";
        $field[$k]["fieldname"] = "所属通路";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "helpcate_sort";
        $field[$k]["fieldname"] = "排序";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无数据', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //新增帮助分类设置
    function addHelpcateView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->addHelpcate($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "新增帮助分类设置", "新增", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //编辑帮助分类设置
    function updateHelpcateView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->updateHelpcate($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "编辑帮助分类设置", "编辑", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //删除帮助分类设置
    function delHelpcateView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->delHelpcate($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "删除帮助分类设置", "删除", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }


    //帮助中心列表
    function getHelpListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getHelpList($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "help_title";
        $field[$k]["fieldname"] = "标题";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "help_class_name";
        $field[$k]["fieldname"] = "所属用户类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "help_passage";
        $field[$k]["fieldname"] = "所属通路";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "helpcate_name";
        $field[$k]["fieldname"] = "分类";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "help_sort";
        $field[$k]["fieldname"] = "排序";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "teacher_cnname";
        $field[$k]["fieldname"] = "创建人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "help_addtime";
        $field[$k]["fieldname"] = "更新时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "help_status";
        $field[$k]["fieldname"] = "是否启用";
        $field[$k]["show"] = 1;
        $field[$k]["switchVisible"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无数据', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //新增帮助中心
    function addHelpView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->addHelp($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "新增帮助设置", "新增", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //编辑帮助中心
    function updateHelpView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->updateHelp($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "编辑帮助设置", "编辑", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //删除帮助中心
    function delHelpView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->delHelp($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "删除帮助设置", "删除", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //编辑帮助中心状态
    function updateHelpStatusView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->updateHelpStatus($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "帮助配置", "编辑状态", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //预约接送相关设置
    function updateAppointmentView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->updateAppointment($request);

        $this->addWorkLog($this->postbeOne['organize_id'], 0, $request['teacher_id'], "预约接送相关设置", "编辑", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }


    //学校替换文字列表
    function getReplacewordView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getReplaceword($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "replaceword_old";
        $field[$k]["fieldname"] = "原字符";
        $field[$k]["fieldwidth"] = "180px";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "replaceword_new";
        $field[$k]["fieldname"] = "替代字符";
        $field[$k]["fieldwidth"] = "180px";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无数据', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }


    //新增替换文字
    function addReplacewordView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->addReplaceword($request);

        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //编辑替换文字
    function updateReplacewordView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->updateReplaceword($request);

        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //删除替换文字
    function delReplacewordView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->delReplaceword($request);

        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //单个学校预约接送参数信息
    function getAppointmentOneView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataOne = $Model->getAppointmentOne($request);

        if ($dataOne) {
            $result = $dataOne;
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result = '';
            $res = array('error' => 1, 'errortip' => '暂无数据', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //新增接送点
    function addLocationView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->addLocation($request);

        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //删除接送点
    function delLocationView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->delLocation($request);

        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }


    //接送点列表
    function locationListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->locationList($request);
        $result = array();
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无数据', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }


    //待播放列表
    function getTobeplayedListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getTobeplayedList($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "msinfo_id";
        $field[$k]["fieldname"] = "NO";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        if ($request['msinfo_isplay'] == '0') {
            $field[$k]["fieldstring"] = "msinfo_playtime";
            $field[$k]["fieldname"] = "广播时间";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
        } else {
            $field[$k]["fieldstring"] = "msinfo_playtime";
            $field[$k]["fieldname"] = "语音播放时间";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
        }

        $field[$k]["fieldstring"] = "msinfo_time";
        $field[$k]["fieldname"] = "预约接送时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "msinfo_createtime";
        $field[$k]["fieldname"] = "预约提交时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "msinfo_type_name";
        $field[$k]["fieldname"] = "播放类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "msinfo_text";
        $field[$k]["fieldname"] = "播放内容";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        if ($request['msinfo_isplay'] == '1') {
            $field[$k]["fieldstring"] = "msinfo_time";
            $field[$k]["fieldname"] = "播放完成时间";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
        } else {
            $field[$k]["fieldstring"] = "msinfo_isplay_name";
            $field[$k]["fieldname"] = "状态";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
        }

        $result = array();
        $result["field"] = $field;
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无数据', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //接人列表
    function getPickupListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getPickupList($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "msinfo_id";
        $field[$k]["fieldname"] = "NO";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        if ($request['msinfo_status'] == '1') {
            $field[$k]["fieldstring"] = "msinfo_suretime";
            $field[$k]["fieldname"] = "确认时间";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "teacher_cnname";
            $field[$k]["fieldname"] = "确认人";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
        } else {
            $field[$k]["fieldstring"] = "msinfo_time";
            $field[$k]["fieldname"] = "广播时间";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
        }

        $field[$k]["fieldstring"] = "msinfo_time";
        $field[$k]["fieldname"] = "预约时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "msinfo_createtime";
        $field[$k]["fieldname"] = "预约提交时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "姓名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_enname";
        $field[$k]["fieldname"] = "英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "msinfo_relation";
        $field[$k]["fieldname"] = "接送人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "msinfo_mobile";
        $field[$k]["fieldname"] = "电话";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "location_name";
        $field[$k]["fieldname"] = "接送点";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "msinfo_content";
        $field[$k]["fieldname"] = "备注";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无数据', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //插播
    function interCutView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->interCut($request);

        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //播放完成
    function completePlayView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->completePlay($request);

        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //确认接走
    function surePickupView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $Model->surePickup($request);

        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //获取推荐学生背景
    function getBackgroundView(){
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $groundOne = $this->Show_css->getFieldOne("ptc_background","background_id,background_url,background_color","background_passage = '{$request['background_passage']}'");
        $res = array('error' => 0, 'errortip' => '获取成功', 'result' => $groundOne);
        ajax_return($res, $request['language_type']);
    }

    //修改推荐学生背景
    function updateBackgroundView(){
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $groundOne = $this->Show_css->getFieldOne("ptc_background","background_id,background_url,background_color","background_passage = '{$request['background_passage']}'");
        if($groundOne){
            $data = array();
            $data['background_url'] = $request['background_url'];
            $data['background_color'] = $request['background_color'];
            $this->Show_css->updateData("ptc_background","background_id = '{$groundOne['background_id']}'",$data);
        }else{
            $data = array();
            $data['background_url'] = $request['background_url'];
            $data['background_color'] = $request['background_color'];
            $data['background_passage'] = $request['background_passage'];
            $this->Show_css->insertData("ptc_background",$data);
        }
        $res = array('error' => 0, 'errortip' => '修改成功');
        ajax_return($res, $request['language_type']);
    }


    //学生反馈统计
    function getStuFeeListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getStuFeeList($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "学校";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "unittheme_cnname";
        $field[$k]["fieldname"] = "检核主题";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学生姓名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学生编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "feelog_content";
        $field[$k]["fieldname"] = "问题类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "createtime";
        $field[$k]["fieldname"] = "提交时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "teacher_cnname";
        $field[$k]["fieldname"] = "提交人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取反馈统计', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无反馈统计', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }


    //题目反馈统计 -- 读书检核
    function getPraFeeListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getPraFeeList($request);

        if(!$request['school_id']){
            $oid = $this->Show_css->getFieldOne("eas_teacher_postbe","organize_id","postbe_id = '{$request['postbe_id']}'");
            $mold = $this->Show_css->getFieldOne("eas_organize","organize_mold","organize_id = '{$oid['organize_id']}'");
            if($mold['organize_mold'] == '1'){
                $request['school_nature'] = 'kid';
            }elseif($mold['organize_mold'] == '0'){
                $request['school_nature'] = 'school';
            }
        }

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "学校";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        if($request['school_nature'] == 'school'){
            $field[$k]["fieldstring"] = "type";
            $field[$k]["fieldname"] = "检核类型";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

            $field[$k]["fieldstring"] = "theme";
            $field[$k]["fieldname"] = "检核主题";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;

//            if (isset($paramArray['type']) && $paramArray['type'] == '1') {
//                $field[$k]["fieldstring"] = "sort";
//                $field[$k]["fieldname"] = "所属week";
//                $field[$k]["show"] = 1;
//                $field[$k]["custom"] = 1;
//                $k++;
//            }elseif(isset($paramArray['type']) && $paramArray['type'] == '2'){
//                $field[$k]["fieldstring"] = "times_sort";
//                $field[$k]["fieldname"] = "检核主题";
//                $field[$k]["show"] = 1;
//                $field[$k]["custom"] = 1;
//                $k++;
//            }

        }else{
            $field[$k]["fieldstring"] = "unittheme_cnname";
            $field[$k]["fieldname"] = "检核主题";
            $field[$k]["show"] = 1;
            $field[$k]["custom"] = 1;
            $k++;
        }

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学生姓名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学生编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "paragraph";
        $field[$k]["fieldname"] = "题目序号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "paragraph_img";
        $field[$k]["fieldname"] = "题目图片";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["imgVisible"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "paragraph_content";
        $field[$k]["fieldname"] = "题目内容";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "feelog_voiceurl";
        $field[$k]["fieldname"] = "音频";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["patrolAudioVisible"] = 1;
        $k++;

//        $field[$k]["fieldstring"] = "feelog_score";
//        $field[$k]["fieldname"] = "分数";
//        $field[$k]["show"] = 1;
//        $field[$k]["custom"] = 1;
//        $k++;

        $field[$k]["fieldstring"] = "feelog_content";
        $field[$k]["fieldname"] = "问题类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "createtime";
        $field[$k]["fieldname"] = "提交时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "teacher_cnname";
        $field[$k]["fieldname"] = "提交人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取反馈统计', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无反馈统计', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }


    //题目反馈统计 -- 季度鉴定
    function getEvaluatFeeListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new SettingsModel($request);
        $dataList = $Model->getEvaluatFeeList($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "school_cnname";
        $field[$k]["fieldname"] = "学校";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "evaluatlevel_name";
        $field[$k]["fieldname"] = "检核级别";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "学生姓名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学生编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "question_pid";
        $field[$k]["fieldname"] = "题目编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "category_name";
        $field[$k]["fieldname"] = "题目类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "question_content";
        $field[$k]["fieldname"] = "文本";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "answers_url";
        $field[$k]["fieldname"] = "音频";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["questionAudioVisible"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "my_score";
        $field[$k]["fieldname"] = "得分";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "feelog_type";
        $field[$k]["fieldname"] = "问题类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "feelog_createtime";
        $field[$k]["fieldname"] = "提交时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "teacher_cnname";
        $field[$k]["fieldname"] = "提交人";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取题目反馈统计', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无题目反馈统计', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }



    //结尾魔术函数
    function __destruct()
    {

    }
}
