<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2023/7/31
 * Time: 14:29
 */

namespace Work\Controller\Teasxapi;

class ThirdPartyController extends viewTpl
{
    public $error = false;
    public $errortip = false;
    public $oktip = false;//正确提示
    public $bakerrorfuc = "errormotify";
    public $result = array();
    public $stafferOne = array();

    function __construct()
    {
        parent::__construct();
    }

    function stringReplace($string)
    {
        $datacode = trim(str_replace('"', "", $string));
        $datacode = urldecode(urldecode($datacode));
        $datacode = str_replace(' ', "+", $datacode);
        return $datacode;
    }

    //第三方授权访问权限校验
    function UserVerify($paramArray)
    {
        $apiuserOne = $this->Show_css->getFieldOne('app_apiuser', "apiuser_id,apiuser_aeskey,apiuser_aesiv", "apiuser_code = '{$paramArray['apiuser_code']}'");
        if (!$apiuserOne) {
            $this->errortip = "未查询到您的授权信息";
            $this->error = true;
            return false;
        }

        if (isset($paramArray['apiuser_aeskey']) && $paramArray['apiuser_aeskey'] !== '' && isset($paramArray['apiuser_aesiv']) && $paramArray['apiuser_aesiv'] !== '') {
            if ($apiuserOne['apiuser_aeskey'] == $paramArray['apiuser_aeskey'] && $apiuserOne['apiuser_aesiv'] == $paramArray['apiuser_aesiv']) {
                $baseOne = array();
                $baseOne['apiuser_id'] = $apiuserOne['apiuser_id'];
//                $companyOne = $this->Show_css->selectOne("select company_id,company_cnname from gmc_company WHERE company_id = '{$paramArray['company_id']}' limit 0,1");
                $companyOne['company_id'] = '8888';
                if ($companyOne) {
                    $baseOne['company_id'] = $companyOne['company_id'];
                    return $baseOne;
                } else {
                    $this->errortip = '你的授权集团编号错误，请确认编号正确';
                    $this->error = true;
                    return false;
                }
            } else {
                $this->errortip = "你的授权秘钥及偏移值不正确，{$paramArray['apiuser_aeskey']}-{$paramArray['apiuser_aesiv']}";
                $this->error = true;
                return false;
            }
        }

        if (!isset($paramArray['timesteps']) || $paramArray['timesteps'] == '') {
            $this->errortip = "请传入授权时间";
            $this->error = true;
            return false;
        }

        if ($paramArray['timesteps'] + 60 * 5 < time() || $paramArray['timesteps'] - 60 > time()) {
            $maxtimes = date("Y-m-d H:i:s", $paramArray['timesteps'] + 60 * 5);
            $this->errortip = "授权时间{$maxtimes}已过期5分钟，请确认连接及时性";//,{$timesteps}--{$jmsting}
            $this->error = true;
            return false;
        }

        $aes = new \Aesencdec($apiuserOne['apiuser_aeskey'], $apiuserOne['apiuser_aesiv']);
        $xssting = $aes->decrypt($this->stringReplace($paramArray['veytoken']));//解密
        if ($paramJson = json_decode($xssting, 1)) {//转化为数组
            if ((string)$paramJson['timesteps'] !== trim($paramArray['timesteps'])) {
                $this->errortip = '授权时间和连接时间不一致';
                $this->error = true;
                return false;
            }
            $baseOne = array();
            $baseOne['apiuser_id'] = $apiuserOne['apiuser_id'];
            $baseOne['tokenstring'] = $xssting;
//            $companyOne = $this->Show_css->selectOne("select company_id,company_cnname from gmc_company WHERE company_id = '{$paramJson['company_id']}' limit 0,1");
            $companyOne['company_id'] = '8888';
            if ($companyOne) {
                $baseOne['company_id'] = $companyOne['company_id'];
                return $baseOne;
            } else {
                $this->errortip = '你的授权集团编号错误，请确认编号正确';
                $this->error = true;
                return false;
            }
        } else {
            $this->errortip = '数据机密信息传输有误，请检查！';
            $this->error = true;
            return false;
        }
    }

    function VeryModelNums($apiuser_id, $apimodule_code, $paramArray)
    {
        $request = Input('get.', '', 'trim,addslashes');
        $apimoduleOne = $this->Show_css->getFieldOne('app_apiuser_apimodule'
            , "apimodule_id,apiuser_id,apimodule_name,apimodule_nums", "apiuser_id = '{$apiuser_id}' AND apimodule_code = '{$apimodule_code}'");
        $stattTimes = strtotime(date("Y-m-d"));
        $apilogOne = $this->Show_css->selectOne("SELECT COUNT(l.apilog_id) AS anums FROM app_apiuser_apilog AS l
WHERE l.apimodule_id = '{$apimoduleOne['apimodule_id']}' AND l.apilog_createtime > '{$stattTimes}'");
        if ($apilogOne['anums'] > $apimoduleOne['apimodule_nums']) {
            $this->errortip = "您接口{$apimoduleOne['apimodule_name']}的本日最大授权次数{$apimoduleOne['apimodule_nums']}已消耗完毕！";
            $this->error = true;
            return false;
        } else {
            $data = array();
            $data['apiuser_id'] = $apimoduleOne['apiuser_id'];
            $data['apimodule_id'] = $apimoduleOne['apimodule_id'];
            $data['apilog_posturl'] = "https://api." . KDD_RUL . "/{$request['u']}/{$request['t']}";
            $data['apilog_posttype'] = 'GET';
            $data['apilog_postorgjson'] = http_build_query($paramArray);
            $data['apilog_postjson'] = $paramArray['tokenstring'];
            $data['apilog_ip'] = real_ip();
            $data['apilog_createtime'] = time();
            $this->Show_css->insertData("app_apiuser_apilog", $data);
            return true;
        }
    }

    //模拟加密参数
    function testParameterView()
    {
        $parameter = array();
        $parameter['timesteps'] = time();
        $parameter['apiuser_code'] = 'RoleToKdd';
        $parameter['company_id'] = '8888';

        $apiuserOne = $this->Show_css->getFieldOne('app_apiuser', "apiuser_id,apiuser_aeskey,apiuser_aesiv", "apiuser_code = '{$parameter['apiuser_code']}'");
        if (!$apiuserOne) {
            $this->errortip = "未查询到您的授权信息";
            $this->error = true;
            return false;
        }

        $aes = new \Aesencdec($apiuserOne['apiuser_aeskey'], $apiuserOne['apiuser_aesiv']);
        $parameterJson = json_encode($parameter, '1');
        $jmsting = $aes->encrypt($parameterJson);//解密

        $result = array();
        $result['timesteps'] = $parameter['timesteps'];
        $result['apiuser_code'] = $parameter['apiuser_code'];
        $result['veytoken'] = $jmsting;

        ajax_return(array('error' => '0', 'errortip' => '模拟参数获取成功', 'result' => $result));
    }

    //第三方获取主要信息  -- 角色平台免登录接口
    function getThirdPartyLoginApiView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($request['employeepid'] == '') {
                ajax_return(array('error' => '1', 'errortip' => '职工编号不能为空', 'result' => array()));
            }
            if ($request['phone']) {
                //手机号获取教师数据
                $teachermobile = $request['phone'];
            } else {
                //人资信息
                $ApiInfoOne = request_by_curl("https://eduappv2.kidcastle.com.cn/api/beisen/zhuzhixinxi", "jobNumber={$request['employeepid']}", "GET", array());
                $infoInfo = json_decode($ApiInfoOne, true);
                if (!$infoInfo['data']['user']['mobile']) {
                    ajax_return(array('error' => '1', 'errortip' => '未找到您的职工信息，请联系管理员！', 'result' => array()));
                }
                //手机号获取教师数据
                $teachermobile = $infoInfo['data']['user']['mobile'];
            }
//            $teachermobile = '18703629223';
            if ($teachermobile == '') {
                ajax_return(array('error' => '1', 'errortip' => '职工手机号不能为空', 'result' => array()));
            }


            if($request['isToTw'] == '1'){
                if(mb_substr($teachermobile,0,1) != '0'){
                    $teachermobile = "0".$teachermobile;
                }
                //模拟登录
                $compass = $this->Show_css->getFieldOne("cms_variable", "content", "variable_id = '38'");
                $data = array();
                $data['school_branch'] = '';
                $data['mobile'] = $teachermobile;
                $data['password'] = $compass['content'];
                $data['language'] = 'zh-CN';
                $dataListJson = request_by_curl("https://teasxapi.kidcastleapp.tw/Login/Login", dataEncode($data), "POST", array());
            }else {
                $Model = new \Model\SchoolManageModel();
                $stafferOne = $this->Show_css->selectOne("select teacher_id,teacher_mobile,teacher_branch,fromchannel from app_teacher where teacher_mobile = '{$teachermobile}' limit 0,1");
                if (!$stafferOne || $stafferOne['fromchannel'] == '') {
                    //更新职工信息
                    $Model->UpdateTeacherByIDorMobile($teachermobile);

                    $stafferOne = $this->Show_css->selectOne("select teacher_id,teacher_mobile,teacher_branch from app_teacher where teacher_mobile = '{$teachermobile}' limit 0,1");
                    if (!$stafferOne) {
                        ajax_return(array('error' => '1', 'errortip' => '未找到您的信息，请联系管理员！', 'result' => array()));
                    }
                }

                //更新班级等
                $Model->UpdataTeacherTeaching($stafferOne['teacher_id'], $stafferOne['teacher_branch']);
                //更新组织架构 -- 下边接口会有更新数据
                //$Model->UpdataTeacherOrganize($stafferOne['teacher_branch']);
                //模拟登录
                $compass = $this->Show_css->getFieldOne("cms_variable", "content", "variable_id = '38'");
                $data = array();
                $data['school_branch'] = '';
                $data['mobile'] = $stafferOne['teacher_mobile'];
                $data['password'] = $compass['content'];
                $data['language'] = 'zh-CN';
                $dataListJson = request_by_curl("https://teasxapi.kidcastle.cn/Login/Login", dataEncode($data), "POST", array());
            }
//                $dataListJson = request_by_curl("https://teasxapi.chevady.cn/Login/Login", dataEncode($data), "POST",array());
            $jaonToData = json_decode($dataListJson, 1);

            if ($jaonToData['error'] == '1') {
                ajax_return(array('error' => '1', 'errortip' => "职工信息获取失败", 'result' => array()));
            }

//            $dataA = array();
//            $dataA['teacher_id'] = $jaonToData['result']['list']['teacher_id'];
//            $dataA['token'] = $jaonToData['result']['list']['token'];
//            $dataA['language'] = 'zh';
//            $dataAJson = request_by_curl("https://teasxapi.kidcastle.cn/Login/getPostbe", dataEncode($dataA), "GET",array());
//            $jaonAToData = json_decode($dataAJson, 1);
//            if($jaonAToData['error'] == '1'){
//                ajax_return(array('error' => '1', 'errortip' => "暂无校园职务", 'result' => array()));
//            }

            $dataList = array();
            $dataList['teacher_id'] = $jaonToData['result']['list']['teacher_id'];
            $dataList['token'] = $jaonToData['result']['list']['token'];

            if ($request['isToTw'] == '1') {//台湾
                if ($request['isToTeasxapp'] == '1') {//是否访问教师H5手机页面  Teasxapp
                    //正式
                    $dataList['urlToKdd'] = "https://teasxapp.kidcastleapp.tw/#/pages/thirdPartyLogin/index?type=TeasxPC&token={$jaonToData['result']['list']['token']}&teacher_id={$jaonToData['result']['list']['teacher_id']}&teacher_isdev={$jaonToData['result']['list']['teacher_isdev']}&teacher_istec={$jaonToData['result']['list']['teacher_istec']}&isToTw=1";
                } else {
                    //正式
                    $dataList['urlToKdd'] = "https://teasx.kidcastleapp.tw/thirdPartyLogin?type=TeasxPC&token={$jaonToData['result']['list']['token']}&teacher_id={$jaonToData['result']['list']['teacher_id']}&teacher_isdev={$jaonToData['result']['list']['teacher_isdev']}&teacher_istec={$jaonToData['result']['list']['teacher_istec']}";
                }
            } else {
                if ($request['isToTeasxapp'] == '1') {//是否访问教师H5手机页面  Teasxapp
                    //正式
                    $dataList['urlToKdd'] = "https://teasxapp.kidcastle.cn/#/pages/thirdPartyLogin/index?type=TeasxPC&token={$jaonToData['result']['list']['token']}&teacher_id={$jaonToData['result']['list']['teacher_id']}&teacher_isdev={$jaonToData['result']['list']['teacher_isdev']}&teacher_istec={$jaonToData['result']['list']['teacher_istec']}&isToTw=0";
                } else {
                    //正式
                    $dataList['urlToKdd'] = "https://teasx.kidcastle.cn/thirdPartyLogin?type=TeasxPC&token={$jaonToData['result']['list']['token']}&teacher_id={$jaonToData['result']['list']['teacher_id']}&teacher_isdev={$jaonToData['result']['list']['teacher_isdev']}&teacher_istec={$jaonToData['result']['list']['teacher_istec']}";
                }
            }
            //测试
//                $dataList['urlToKdd'] = "https://teasx.chevady.cn/thirdPartyLogin?type=TeasxPC&token={$jaonToData['result']['list']['token']}&teacher_id={$jaonToData['result']['list']['teacher_id']}&teacher_isdev={$jaonToData['result']['list']['teacher_isdev']}&teacher_istec={$jaonToData['result']['list']['teacher_istec']}";

            $field = [
                "teacher_id" => "职工ID",
                "token" => "职工token",
                "urlToKdd" => "登录跳转课叮铛的链接",
            ];

            $result = array();
            $result["field"] = $field;
            $result['list'] = $dataList ? $dataList : array();
            if ($dataList) {
                ajax_return(array('error' => '0', 'errortip' => "信息获取成功", 'result' => $result));
            } else {
                ajax_return(array('error' => '1', 'errortip' => "信息获取失败", 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    function getThirdPartyLoginInfoApiView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($request['employeepid'] == '') {
                ajax_return(array('error' => '1', 'errortip' => '职工编号不能为空', 'result' => array()));
            }
            if ($request['phone']) {
                //手机号获取教师数据
                $teachermobile = $request['phone'];
            } else {
                //人资信息
                $ApiInfoOne = request_by_curl("https://eduappv2.kidcastle.com.cn/api/beisen/zhuzhixinxi", "jobNumber={$request['employeepid']}", "GET", array());
                $infoInfo = json_decode($ApiInfoOne, true);
                if (!$infoInfo['data']['user']['mobile']) {
                    ajax_return(array('error' => '1', 'errortip' => '未找到您的职工信息，请联系管理员！', 'result' => array()));
                }
                //手机号获取教师数据
                $teachermobile = $infoInfo['data']['user']['mobile'];
            }
//            $teachermobile = '18703629223';
            if ($teachermobile == '') {
                ajax_return(array('error' => '1', 'errortip' => '职工手机号不能为空', 'result' => array()));
            }


            if($request['isToTw'] == '1'){
                if(mb_substr($teachermobile,0,1) != '0'){
                    $teachermobile = "0".$teachermobile;
                }
                //模拟登录
                $compass = $this->Show_css->getFieldOne("cms_variable", "content", "variable_id = '38'");
                $data = array();
                $data['school_branch'] = '';
                $data['mobile'] = $teachermobile;
                $data['password'] = $compass['content'];
                $data['language'] = 'zh-CN';
                $dataListJson = request_by_curl("https://teasxapi.kidcastleapp.tw/Login/Login", dataEncode($data), "POST", array());
            }else {
                $Model = new \Model\SchoolManageModel();
                $stafferOne = $this->Show_css->selectOne("select teacher_id,teacher_mobile,teacher_branch,fromchannel from app_teacher where teacher_mobile = '{$teachermobile}' limit 0,1");
                if (!$stafferOne || $stafferOne['fromchannel'] == '') {
                    //更新职工信息
                    $Model->UpdateTeacherByIDorMobile($teachermobile);

                    $stafferOne = $this->Show_css->selectOne("select teacher_id,teacher_mobile,teacher_branch from app_teacher where teacher_mobile = '{$teachermobile}' limit 0,1");
                    if (!$stafferOne) {
                        ajax_return(array('error' => '1', 'errortip' => '未找到您的信息，请联系管理员！', 'result' => array()));
                    }
                }

                //更新班级等
                $Model->UpdataTeacherTeaching($stafferOne['teacher_id'], $stafferOne['teacher_branch']);
                //更新组织架构 -- 下边接口会有更新数据
                //$Model->UpdataTeacherOrganize($stafferOne['teacher_branch']);
                //模拟登录
                $compass = $this->Show_css->getFieldOne("cms_variable", "content", "variable_id = '38'");
                $data = array();
                $data['school_branch'] = '';
                $data['mobile'] = $stafferOne['teacher_mobile'];
                $data['password'] = $compass['content'];
                $data['language'] = 'zh-CN';
                $dataListJson = request_by_curl("https://teasxapi.kidcastle.cn/Login/Login", dataEncode($data), "POST", array());
            }
//                $dataListJson = request_by_curl("https://teasxapi.chevady.cn/Login/Login", dataEncode($data), "POST",array());
            $jaonToData = json_decode($dataListJson, 1);

            if ($jaonToData['error'] == '1') {
                ajax_return(array('error' => '1', 'errortip' => "职工信息获取失败", 'result' => array()));
            }

//            $dataA = array();
//            $dataA['teacher_id'] = $jaonToData['result']['list']['teacher_id'];
//            $dataA['token'] = $jaonToData['result']['list']['token'];
//            $dataA['language'] = 'zh';
//            $dataAJson = request_by_curl("https://teasxapi.kidcastle.cn/Login/getPostbe", dataEncode($dataA), "GET",array());
//            $jaonAToData = json_decode($dataAJson, 1);
//            if($jaonAToData['error'] == '1'){
//                ajax_return(array('error' => '1', 'errortip' => "暂无校园职务", 'result' => array()));
//            }

            $dataList = array();
            $dataList['teacher_id'] = $jaonToData['result']['list']['teacher_id'];
            $dataList['token'] = $jaonToData['result']['list']['token'];

            if ($request['isToTw'] == '1') {//台湾
                if ($request['isToTeasxapp'] == '1') {//是否访问教师H5手机页面  Teasxapp
                    //正式
                    $dataList['urlToKdd'] = "https://teasxapp.kidcastleapp.tw/#/pages/thirdPartyLogin/index?type=TeasxPC&token={$jaonToData['result']['list']['token']}&teacher_id={$jaonToData['result']['list']['teacher_id']}&teacher_isdev={$jaonToData['result']['list']['teacher_isdev']}&teacher_istec={$jaonToData['result']['list']['teacher_istec']}&isToTw=1";
                } else {
                    //正式
                    $dataList['urlToKdd'] = "https://teasx.kidcastleapp.tw/thirdPartyLogin?type=TeasxPC&token={$jaonToData['result']['list']['token']}&teacher_id={$jaonToData['result']['list']['teacher_id']}&teacher_isdev={$jaonToData['result']['list']['teacher_isdev']}&teacher_istec={$jaonToData['result']['list']['teacher_istec']}";
                }
            } else {
                if ($request['isToTeasxapp'] == '1') {//是否访问教师H5手机页面  Teasxapp
                    //正式
                    $dataList['urlToKdd'] = "https://teasxapp.kidcastle.cn/#/pages/thirdPartyLogin/index?type=TeasxPC&token={$jaonToData['result']['list']['token']}&teacher_id={$jaonToData['result']['list']['teacher_id']}&teacher_isdev={$jaonToData['result']['list']['teacher_isdev']}&teacher_istec={$jaonToData['result']['list']['teacher_istec']}&isToTw=0";
                } else {
                    //正式
                    $dataList['urlToKdd'] = "https://teasx.kidcastle.cn/thirdPartyLogin?type=TeasxPC&token={$jaonToData['result']['list']['token']}&teacher_id={$jaonToData['result']['list']['teacher_id']}&teacher_isdev={$jaonToData['result']['list']['teacher_isdev']}&teacher_istec={$jaonToData['result']['list']['teacher_istec']}";
                }
            }

            $field = [
                "teacher_id" => "职工ID",
                "token" => "职工token",
                "urlToKdd" => "登录跳转课叮铛的链接",
            ];

            $result = array();
            $result["field"] = $field;
            $result['list'] = $dataList ? $dataList : array();
            if ($dataList) {
                ajax_return(array('error' => '0', 'errortip' => "信息获取成功", 'result' => $result));
            } else {
                ajax_return(array('error' => '1', 'errortip' => "信息获取失败", 'result' => array()));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }

    //园务考勤机获取班级相册图片接口
    function getClassesAlbumView()
    {
        header("Content-type: text/html; charset=utf-8");
        $request = Input('get.', '', 'trim,addslashes');
        $pucArray = $this->UserVerify($request);
        if ($pucArray) {
            $request['tokenstring'] = $pucArray['tokenstring'];
            if ($request['class_branch'] == '') {
                ajax_return(array('error' => '1', 'errortip' => '班级编号不能为空', 'result' => array()));
            }
            if ($request['fromchannel'] == '') {
                ajax_return(array('error' => '1', 'errortip' => '来源渠道不能为空', 'result' => array()));
            }

            $datawhere = " a.fromchannel = '{$request['fromchannel']}' and a.class_branch = '{$request['class_branch']}'  ";

            if (isset($request['keyword']) && $request['keyword'] != '') {
                $datawhere .= " ";
            }
            if (isset($request['p']) && $request['p'] !== '') {
                $page = $request['p'];
            } else {
                $page = '1';
            }
            if (isset($request['num']) && $request['num'] !== '') {
                $num = $request['num'];
            } else {
                $num = '10';
            }
            $pagestart = ($page - 1) * $num;

            $sql = " select a.class_branch,b.album_id,c.photos_id,c.photos_imgurl 
                from eas_classes as a 
                left join eas_classes_album as b ON a.class_id = b.class_id 
                left join eas_classes_album_photos as c ON b.album_id = c.album_id 
                where {$datawhere} 
                    and c.photos_imgurl <> '' and c.photos_id > 1 
                order by b.album_id desc,c.photos_id desc 
                limit {$pagestart},{$num}";
            $classalbum = $this->Show_css->selectClear($sql);

            $field = [
                "class_branch" => "班级编号",
                "album_id" => "相册ID",
                "photos_id" => "图片ID",
                "photos_imgurl" => "图片链接",
            ];

            $result = array();
            $result['field'] = $field;
            if ($classalbum) {
                $result['list'] = $classalbum;
                ajax_return(array('error' => '0', 'errortip' => "班级相册图片信息获取成功", 'result' => $result));
            } else {
                $result['list'] = array();
                ajax_return(array('error' => '1', 'errortip' => "暂无对应班级相册数据", 'result' => $result));
            }
        } else {
            ajax_return(array('error' => '1', 'errortip' => $this->errortip, 'result' => array()));
        }
    }


}