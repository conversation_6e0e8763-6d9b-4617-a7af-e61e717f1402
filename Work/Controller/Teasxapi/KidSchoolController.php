<?php
/**
 * ============================================================================
 * 版权所有 : http://www.mohism.cn
 * 网站地址 : http://www.mohism.cn
 * <AUTHOR> Zhugong <PERSON>
 * Date: 2017/4/13
 * Time: 23:25
 */

namespace Work\Controller\Teasxapi;

use Model\Teasx\KddkiddataModel;
use Model\Teasx\KidSchoolModel;
use Model\Teasx\TeacherModel;

class KidSchoolController extends viewTpl
{
    public $u;
    public $t;
    public $c;
    public $postbeOne = array();

    //预加载处理类
    function __construct()
    {
        parent::__construct();
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();

        $this->postbeOne = $this->Show_css->getFieldOne("eas_teacher_postbe", "organize_id", "postbe_id = '{$_POST['postbe_id']}'");
    }

    //数据总览
    function getDataScreeningView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new TeacherModel($request);
        $dataOne = $Model->getKidHoemRemindApi($request);

        $datalist = array();
        $datalist['total']['stationNum'] = $dataOne['station_num'];
        $datalist['total']['noticeNum'] = 0;
        $datalist['total']['classTaskNum'] = 0;
        $datalist['total']['albumNum'] = 0;
        $datalist['total']['dynamicNum'] = $dataOne['dynamic_num'];
        $datalist['total']['commentNum'] = $dataOne['stationlog_num'];
        $datalist['total']['effectNum'] = $dataOne['effect_num'];

        $dataTwo = $Model->getKidHoemDataApi($request);

        $datalist['rate']['qiquOpenRate'] = $dataTwo['stuapp_rate'];
        $datalist['rate']['blhOpenRate'] = $dataTwo['blhapp_rate'];
        $datalist['rate']['offlinetaskNum'] = $dataTwo['offlinetask_rate'];
        $datalist['rate']['commentRate'] = $dataTwo['reply_rate'];
        $datalist['rate']['kidEnrollRate'] = '--';

        $result = array();
        $result['list'] = $datalist;
        $res = array('error' => 0, 'errortip' => '获取数据总览成功', 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    //获取班级列表
    function getClassListView()
    {
         $request = Input('get.', '', 'trim,addslashes');
         $this->ThisVerify($request);//验证账户

         $Model = new KidSchoolModel($request);
         $dataList = $Model->getClassListApi($request);

         $result = array();
         if ($dataList['list']) {
             $result["list"] = $dataList['list'];
             $res = array('error' => 0, 'errortip' => '获取班级列表信息', 'result' => $result);
         } else {
             $result["list"] = array();
             $res = array('error' => 1, 'errortip' => '暂无班级列表', 'result' => $result);
         }
         ajax_return($res, $request['language_type']);
    }

    //获取奇趣任务列表
    function getQiQuTaskListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new KidSchoolModel($request);
        $dataList = $Model->getQiQuTaskListApi($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "course_cnname";
        $field[$k]["fieldname"] = "年级";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_num";
        $field[$k]["fieldname"] = "开班数量";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "stu_num";
        $field[$k]["fieldname"] = "学生人数";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "task_nums";
        $field[$k]["fieldname"] = "当前教学进度";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "task_rate";
        $field[$k]["fieldname"] = "开班奇趣任务完成率";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "task_time";
        $field[$k]["fieldname"] = "上次进度开启时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取奇趣任务列表信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无奇趣任务', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //开启奇趣任务
    function openTaskView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new KidSchoolModel($request);
        $Model->openTaskApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], $request['school_id'], $request['teacher_id'], "教学进度管理->教学进度开启", "开启任务", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //兴趣班开启奇趣任务
    function openInterestClassTaskView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new KidSchoolModel($request);
        $Model->openInterestClassTaskApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], $request['school_id'], $request['teacher_id'], "教学进度管理->教学进度开启", "开启任务", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //教师带班明细
    function getTeacherClassListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new KidSchoolModel($request);
        $dataList = $Model->getTeacherClassList($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "teacher_cnname";
        $field[$k]["fieldname"] = "中文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "teacher_enname";
        $field[$k]["fieldname"] = "英文名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "teacher_sex";
        $field[$k]["fieldname"] = "性别";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "teacher_branch";
        $field[$k]["fieldname"] = "教师编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "teacher_mobile";
        $field[$k]["fieldname"] = "手机号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "post_name";
        $field[$k]["fieldname"] = "职务";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "teacher_status_name";
        $field[$k]["fieldname"] = "是否在职";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_num";
        $field[$k]["fieldname"] = "带班数量";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_name";
        $field[$k]["fieldname"] = "带班班级";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
//        $k++;
//
//        $field[$k]["fieldstring"] = "postbe_ismanager";
//        $field[$k]["fieldname"] = "读书检核是否设置为主管版";
//        $field[$k]["show"] = 1;
//        $field[$k]["switchVisible"] = 1;
//        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取教师带班明细信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无教师带班明细', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //编辑教师职务
    function editTeacherPostView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new KidSchoolModel($request);
        $Model->editTeacherPostApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], $request['school_id'], $request['teacher_id'], "教学进度管理->教师明细", "编辑教师职务", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //改变读书检核主管权限
    function changeMiniManagerView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new KidSchoolModel($request);
        $Model->changeMiniManager($request);

        $this->addWorkLog($this->postbeOne['organize_id'], $request['school_id'], $request['teacher_id'], "小书检核改主管权限", "小书检核改主管权限", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //更新教师
    function updateTeacherView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $scbranch = $this->Show_css->getFieldOne("app_school","school_branch","school_id = '{$request['school_id']}'");

        $Model = new \Model\SchoolManageModel();
        $Model->UpdataSchoolTeacher($scbranch['school_branch'], 0, 100);

        $res = array('error' => 0, 'errortip' => '更新成功');
        ajax_return($res, $request['language_type']);
    }

    //获取家联本列表 -- 移动端通用
    function getStationlogView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new KidSchoolModel($request);
        $dataList = $Model->getStationlogApi($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "姓名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "is_comment";
        $field[$k]["fieldname"] = "是否发布家联本";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "read_status";
        $field[$k]["fieldname"] = "家长阅读状态";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        $result["stu_allnums"] = $dataList['stu_allnums'];
        $result["comment_num"] = $dataList['comment_num'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取家联本列表信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无家联本', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //获取家联本学员数量
    function getStationStuNumView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new KidSchoolModel($request);
        $dataList = $Model->getStationStuNumApi($request);

        $result = array();
        $result["list"] = $dataList;
        $res = array('error' => 0, 'errortip' => '获取家联本学员数量信息', 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    //查看点评详情 -- 移动端通用
    function getStationOneView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new KidSchoolModel($request);
        $dataList = $Model->getStationOneApi($request);

        $result = array();
        $result["list"] = $dataList;
        $res = array('error' => 0, 'errortip' => '获取点评详情信息', 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    //查看套用内容
    function getApplyNoteView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new KidSchoolModel($request);
        $dataList = $Model->getApplyNoteApi($request);

        $result = array();
        $result["list"] = $dataList;
        $res = array('error' => 0, 'errortip' => '获取套用内容信息', 'result' => $result);
        ajax_return($res, $request['language_type']);
    }

    //获取家联本字段
    function getStationFieldView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new KidSchoolModel($request);
        $dataList = $Model->getStationFieldApi($request);

        $result = array();
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $result["student"] = $dataList['student'];
            $res = array('error' => 0, 'errortip' => '获取家联本字段信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无家联本字段', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //学员点评
    function addStudentCommentsView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new KidSchoolModel($request);
        $Model->addStudentCommentsApi($request);

        $this->addUseRecord($request['school_id'],$request['teacher_id'],$request['class_id'],'发布家联本','Eas-Station','PC网页端','发布家联本');
        $this->addWorkLog($this->postbeOne['organize_id'], $request['school_id'], $request['teacher_id'], "班级管理->家联本", "操作学员点评", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //批量发送/设置定时发送
    function batchStationlogView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new KidSchoolModel($request);
        $Model->batchStationlogApi($request);

        if ($request['stationlog_isfixtimesend']) {
            $type = "批量设置定时发送";
        } else {
            $type = "批量发送家联本";
        }

        $this->addWorkLog($this->postbeOne['organize_id'], $request['school_id'], $request['teacher_id'], "班级管理->家联本", $type, dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //撤回家联本
    function recallStationlogView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new KidSchoolModel($request);
        $Model->recallStationlogApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], $request['school_id'], $request['teacher_id'], "班级管理->家联本", "撤回家联本", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //获取家长回复
    function getParentReplyListView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new KidSchoolModel($request);
        $dataList = $Model->getParentReplyListApi($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "student_cnname";
        $field[$k]["fieldname"] = "姓名";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "student_branch";
        $field[$k]["fieldname"] = "学员编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "submit_time";
        $field[$k]["fieldname"] = "点评日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "classhour_comment";
        $field[$k]["fieldname"] = "教师点评";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取家长回复信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无家长回复', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //获取家长回复 -- 园移动端
    function getParentReplyAppView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new KidSchoolModel($request);
        $dataList = $Model->getParentReplyAppApi($request);

        $result = array();
        $result["allnum"] = $dataList['allnum'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取家长回复信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无家长回复', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //回复/批量回复
    function batchReplayView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new KidSchoolModel($request);
        $Model->batchReplayApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], $request['school_id'], $request['teacher_id'], "班级管理->家联本回复", "批量回复", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //一键无需回复
    function oneKeyReplyView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new KidSchoolModel($request);
        $Model->oneKeyReplyApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], $request['school_id'], $request['teacher_id'], "班级管理->家联本回复", "一键无需回复", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //撤回教师回复
    function revokeReplyView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new KidSchoolModel($request);
        $Model->revokeReplyApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], $request['school_id'], $request['teacher_id'], "班级管理->家联本回复", "撤回教师回复", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //获取配音作品
    function getDubbingWorksView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new KidSchoolModel($request);
        $dataList = $Model->getDubbingWorksApi($request);

        $result = array();
        if ($dataList) {
            $result["list"] = $dataList;
            $res = array('error' => 0, 'errortip' => '获取配音作品信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无配音作品', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //获取作品详情
    function getWorksDetailView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new KidSchoolModel($request);
        $dataList = $Model->getWorksDetailApi($request);

        $result = array();
        if ($dataList) {
            $result["list"] = $dataList;
            $res = array('error' => 0, 'errortip' => '获取作品详情信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无作品详情', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //学员作品点评/回复
    function addWorksReplyReviewView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new KidSchoolModel($request);
        $Model->addWorksReplyReviewApi($request);

        if ($request['work_type'] == '1') {
            $module = "配音秀";
        } elseif ($request['work_type'] == '2') {
            $module = "小奇阅读";
        } elseif ($request['work_type'] == '3') {
            $module = "堡贝乐";
        }

        $this->addWorkLog($this->postbeOne['organize_id'], $request['school_id'], $request['teacher_id'], "班级管理->{$module}", "学员作品点评/回复", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //获取中心端巡检任务配置
    function getInspecTasksView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new KidSchoolModel($request);
        $dataList = $Model->getInspecTasksApi($request);

        $result = array();
        if ($dataList) {
            $result["list"] = $dataList;
            $res = array('error' => 0, 'errortip' => '获取中心端巡检任务配置信息', 'result' => $result);
        } else {
            if ($request['type'] == '1') {
                $tip = '暂无巡检内容';
            } else {
                $tip = '暂无听课内容';
            }
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => $tip, 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //获取项目巡检
    function getInspecItemView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new KidSchoolModel($request);
        $dataList = $Model->getInspecItemApi($request);

        $result = array();
        $result['draft_nums'] = $dataList['draft_nums'];
        $result['error_nums'] = $dataList['error_nums'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取项目巡检信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '此主题进度暂未开启，在开启进度后进行巡检', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //获取项目概览
    function getItemOutlineView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new KidSchoolModel($request);
        $dataList = $Model->getItemOutlineApi($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "inspecitem_name";
        $field[$k]["fieldname"] = "项目名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "inspectype_name";
        $field[$k]["fieldname"] = "巡检类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "check_notnums";
        $field[$k]["fieldname"] = "待检核班级";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "check_finenums";
        $field[$k]["fieldname"] = "优秀班级";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "check_normnums";
        $field[$k]["fieldname"] = "正常班级";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "check_errnums";
        $field[$k]["fieldname"] = "异常班级";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取项目概览信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无概览内容', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //获取班级概览
    function getClassOutlineView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new KidSchoolModel($request);
        $dataList = $Model->getThemeStatApi($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_branch";
        $field[$k]["fieldname"] = "班级编号";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "notcheck_nums";
        $field[$k]["fieldname"] = "待检核";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "excellent_nums";
        $field[$k]["fieldname"] = "优秀";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "normal_nums";
        $field[$k]["fieldname"] = "正常";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "aberrant_nums";
        $field[$k]["fieldname"] = "异常";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        if ($dataList['list']) {
            $result['list'] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取班级概览成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无信息', 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    //获取巡检明细
    function getInspecDetailView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new KidSchoolModel($request);
        $dataList = $Model->getInspecDetailApi($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "inspecitem_name";
        $field[$k]["fieldname"] = "项目名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "inspectype_name";
        $field[$k]["fieldname"] = "巡检类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "class_cnname";
        $field[$k]["fieldname"] = "班级";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "times_name";
        $field[$k]["fieldname"] = "检核周次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "patrolcheck_level_name";
        $field[$k]["fieldname"] = "检核结果";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "patrolcheck_imagenums";
        $field[$k]["fieldname"] = "照片/视频";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["clickVisible"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "patrolcheck_content";
        $field[$k]["fieldname"] = "评语";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "teacher_cnname";
        $field[$k]["fieldname"] = "检核教师";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "patrolcheck_createtime";
        $field[$k]["fieldname"] = "检核时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        if ($dataList['list']) {
            $result['list'] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取巡检明细成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无信息', 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    //获取项目巡检详情
    function getInspecItemDetailView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new KidSchoolModel($request);
        $dataList = $Model->getInspecItemOne($request);

        $result = array();
        if ($dataList) {
            $result["list"] = $dataList;
            $res = array('error' => 0, 'errortip' => '获取项目巡检详情信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无项目巡检', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //获取巡班听课草稿箱
    function getDraftBoxView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new KidSchoolModel($request);
        $dataList = $Model->getDraftBoxApi($request);

        $result = array();
        if ($dataList) {
            $result["list"] = $dataList;
            $res = array('error' => 0, 'errortip' => '获取信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //获取巡检班级
    function getCheckClassView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new KidSchoolModel($request);
        $dataList = $Model->getCheckClassApi($request);

        $result = array();
        if ($dataList) {
            $result['list'] = $dataList;
            $res = array('error' => 0, 'errortip' => '获取巡检班级成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无巡检班级信息', 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    //立即检核
    function addPatrolCheckView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new KidSchoolModel($request);
        $Model->addPatrolCheckApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], $request['school_id'], $request['teacher_id'], "教务回报->巡班听课", "立即检核", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //添加听课记录
    function addInspecLslistensView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new KidSchoolModel($request);
        $Model->addInspecLslistensApi($request);

        $this->addWorkLog($this->postbeOne['organize_id'], $request['school_id'], $request['teacher_id'], "教务回报->巡班听课", "添加听课记录", dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //删除巡班/听课草稿箱数据
    function delDraftBoxDataView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new KidSchoolModel($request);
        $Model->delDraftBoxDataApi($request);
        if ($request['type'] == '1') {
            $type = "删除巡班草稿箱数据";
        } else {
            $type = "删除听课草稿箱数据";
        }

        $this->addWorkLog($this->postbeOne['organize_id'], $request['school_id'], $request['teacher_id'], "教务回报->巡班听课", $type, dataEncode($request));
        $res = array('error' => $Model->error, 'errortip' => $Model->errortip, 'result' => array());
        ajax_return($res, $request['language_type']);
    }

    //获取听课记录
    function getLectureRecordView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new KidSchoolModel($request);
        $dataList = $Model->getLectureRecordApi($request);

        $result = array();
        $result['draft_nums'] = $dataList['draft_nums'];
        $result['error_nums'] = $dataList['error_nums'];
        if ($dataList['list']) {
            $result["list"] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取听课记录信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无听课内容', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //获取听课记录详情
    function getLectureRecordDetailView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new KidSchoolModel($request);
        $dataList = $Model->getLectureRecordOne($request);

        $result = array();
        if ($dataList) {
            $result["list"] = $dataList;
            $res = array('error' => 0, 'errortip' => '获取听课记录详情信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无听课记录', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //获取听课记录信息
    function getLectureRecordInfoView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new KidSchoolModel($request);
        $dataList = $Model->getLectureRecordInfo($request);

        $result = array();
        if ($dataList) {
            $result["list"] = $dataList['list'];
            $result["inspectype"] = $dataList['inspectype'];
            $res = array('error' => 0, 'errortip' => '获取听课记录信息', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无听课记录信息', 'result' => $result);
        }
        ajax_return($res, $request['language_type']);
    }

    //获取听课班级
    function getLslistensClassView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new KidSchoolModel($request);
        $dataList = $Model->getLslistensClassApi($request);

        $result = array();
        if ($dataList) {
            $result['list'] = $dataList;
            $res = array('error' => 0, 'errortip' => '获取听课班级成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无听课班级信息', 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    //获取巡班类型
    function getPatrolInSpecTypeView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new KidSchoolModel($request);
        $dataList = $Model->getPatrolInSpecTypeApi($request);

        $result = array();
        if ($dataList) {
            $result['list'] = $dataList;
            $res = array('error' => 0, 'errortip' => '获取巡班类型成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无巡班类型信息', 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    //获取巡班项目
    function getPatrolInSpecItemView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new KidSchoolModel($request);
        $dataList = $Model->getPatrolInSpecItemApi($request);

        $result = array();
        if ($dataList) {
            $result['list'] = $dataList;
            $res = array('error' => 0, 'errortip' => '获取巡班项目成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无巡班项目信息', 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    //获取听课项目
    function getLslistensItemView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new KidSchoolModel($request);
        $dataList = $Model->getLslistensItemApi($request);

        $result = array();
        if ($dataList) {
            $result['list'] = $dataList;
            $res = array('error' => 0, 'errortip' => '获取听课项目成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无听课项目信息', 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    //获取周次统计
    function getTimesStatView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new KidSchoolModel($request);
        $dataList = $Model->getTimesStatApi($request);

        $result = array();
        if ($dataList) {
            $result['list'] = $dataList;
            $res = array('error' => 0, 'errortip' => '获取周次统计成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无信息', 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    //获取主题统计
    function getThemeStatView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new KidSchoolModel($request);
        $dataList = $Model->getThemeStatApi($request);

        $result = array();
        if ($dataList['list']) {
            $result['list'] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取主题统计成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无信息', 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    //获取班级主题详情
    function getClassThemeDetailView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new KidSchoolModel($request);
        $dataList = $Model->getClassThemeDetailApi($request);

        $result = array();
        $result['times'] = $dataList['times'];
        if ($dataList['list']) {
            $result['list'] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取班级主题详情成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无信息', 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    //获取异常复检
    function getAberrantRecheckView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new KidSchoolModel($request);
        $dataList = $Model->getAberrantRecheckApi($request);

        $result = array();
        $result['patrol_num'] = $dataList['patrol_num'];
        $result['listen_num'] = $dataList['listen_num'];
        $result['classList'] = $dataList['classList'];
        if ($dataList['list']) {
            $result['list'] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取异常复检成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无异常复检', 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    //获取班级项目巡检
    function getClassInspecItemView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new KidSchoolModel($request);
        $dataList = $Model->getClassInspecItemApi($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "inspecitem_name";
        $field[$k]["fieldname"] = "项目名称";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "inspectype_name";
        $field[$k]["fieldname"] = "巡检类型";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "times_name";
        $field[$k]["fieldname"] = "检核周次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "patrolcheck_level_name";
        $field[$k]["fieldname"] = "检核结果";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "teacher_cnname";
        $field[$k]["fieldname"] = "检核教师";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "patrolcheck_createtime";
        $field[$k]["fieldname"] = "检核时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "patrolcheck_imagenums";
        $field[$k]["fieldname"] = "检核照片/视频";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $field[$k]["clickVisible"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "patrolcheck_content";
        $field[$k]["fieldname"] = "检核评语";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        $result["checkStat"] = $dataList['checkStat'];
        if ($dataList['list']) {
            $result['list'] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取项目巡检成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无项目巡检', 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    //获取项目检核记录
    function getInspecItemRecordView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new KidSchoolModel($request);
        $dataList = $Model->getInspecItemRecordApi($request);

        $result = array();
        if ($dataList) {
            $result['list'] = $dataList;
            $res = array('error' => 0, 'errortip' => '获取项目检核记录成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无项目检核记录', 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    //获取班级听课记录
    function getClassListensRecordView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $Model = new KidSchoolModel($request);
        $dataList = $Model->getClassListensRecordApi($request);

        $field = array();
        $k = 0;
        $field[$k]["fieldstring"] = "inspecitem_name";
        $field[$k]["fieldname"] = "听课项目";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "times_name";
        $field[$k]["fieldname"] = "听课周次";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "lslistens_day";
        $field[$k]["fieldname"] = "听课日期";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "teacher_cnname";
        $field[$k]["fieldname"] = "听课教师";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "lslistens_score";
        $field[$k]["fieldname"] = "听课评分";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;
        $k++;

        $field[$k]["fieldstring"] = "createtime";
        $field[$k]["fieldname"] = "提交时间";
        $field[$k]["show"] = 1;
        $field[$k]["custom"] = 1;

        $result = array();
        $result["field"] = $field;
        $result["allnum"] = $dataList['allnum'];
        if ($dataList['list']) {
            $result['list'] = $dataList['list'];
            $res = array('error' => 0, 'errortip' => '获取听课记录成功', 'result' => $result);
        } else {
            $result["list"] = array();
            $res = array('error' => 1, 'errortip' => '暂无听课记录', 'result' => $result);
        }
        ajax_return($res,$request['language_type']);
    }

    function readAudioActionView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);

        $data = array();
        $data['reviewlog_audioisread'] = '1';
        $this->Show_css->updateData("app_dubbingworks_reviewlog","reviewlog_id = '{$request['reviewlog_id']}'", $data);

        ajax_return(array('error' => 0, 'errortip' => "阅读成功"));
    }

    //获取教师任务
    function getStafferTaskView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $schoolOne = $this->Show_css->getFieldOne("app_school", "school_branch", "school_id = '{$request['school_id']}'");
        $teacherOne = $this->Show_css->getFieldOne("app_teacher", "teacher_branch", "teacher_id = '{$request['teacher_id']}'");

        $kddAuthprivModel = new KddkiddataModel();
        $res = $kddAuthprivModel->getStafferTaskApi($request, $schoolOne['school_branch'], $teacherOne['teacher_branch']);

        ajax_return(array('error' => $res['error'], 'errortip' => $res['errortip'],'result' => $res['result']), $request['language_type']);
    }

    //获取任务详情
    function getTaskDetailView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $schoolOne = $this->Show_css->getFieldOne("app_school", "school_branch", "school_id = '{$request['school_id']}'");
        $teacherOne = $this->Show_css->getFieldOne("app_teacher", "teacher_branch", "teacher_id = '{$request['teacher_id']}'");

        $kddAuthprivModel = new KddkiddataModel();
        $res = $kddAuthprivModel->getTaskDetailApi($request, $schoolOne['school_branch'], $teacherOne['teacher_branch']);

        ajax_return(array('error' => $res['error'], 'errortip' => $res['errortip'],'result' => $res['result']), $request['language_type']);
    }

    //获取历史任务
    function getHistoryTaskView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $schoolOne = $this->Show_css->getFieldOne("app_school", "school_branch", "school_id = '{$request['school_id']}'");
        $teacherOne = $this->Show_css->getFieldOne("app_teacher", "teacher_branch", "teacher_id = '{$request['teacher_id']}'");

        $kddAuthprivModel = new KddkiddataModel();
        $res = $kddAuthprivModel->getHistoryTaskApi($request, $schoolOne['school_branch'], $teacherOne['teacher_branch']);

        ajax_return(array('error' => $res['error'], 'errortip' => $res['errortip'],'result' => $res['result']), $request['language_type']);
    }

    //获取任务明细
    function getTaskOneView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $schoolOne = $this->Show_css->getFieldOne("app_school", "school_branch", "school_id = '{$request['school_id']}'");
        $teacherOne = $this->Show_css->getFieldOne("app_teacher", "teacher_branch", "teacher_id = '{$request['teacher_id']}'");

        $kddAuthprivModel = new KddkiddataModel();
        $res = $kddAuthprivModel->getTaskOneApi($request, $schoolOne['school_branch'], $teacherOne['teacher_branch']);

        ajax_return(array('error' => $res['error'], 'errortip' => $res['errortip'],'result' => $res['result']), $request['language_type']);
    }

    //提交任务
    function submitTaskView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户

        $teacherOne = $this->Show_css->getFieldOne("app_teacher", "teacher_branch", "teacher_id = '{$request['teacher_id']}'");

        $kddAuthprivModel = new KddkiddataModel();
        $res = $kddAuthprivModel->submitTaskApi($request, $teacherOne['teacher_branch']);

        ajax_return(array('error' => $res['error'], 'errortip' => $res['errortip'],'result' => array()), $request['language_type']);
    }


    //班级考勤列表
    function classAttendanceListApiView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $teacherOne = $this->Show_css->getFieldOne("app_teacher", "teacher_branch", "teacher_id = '{$request['teacher_id']}'");
        $schoolOne = $this->Show_css->getFieldOne("app_school", "school_branch", "school_id = '{$request['school_id']}'");
        $classOne = $this->Show_css->getFieldOne("eas_classes", "class_branch", "class_id = '{$request['class_id']}'");

        $kddAuthprivModel = new KddkiddataModel();
        $res = $kddAuthprivModel->classAttendanceListApi($request, $teacherOne['teacher_branch'],$schoolOne['school_branch'],$classOne['class_branch']);

        ajax_return($res);
    }

    //班级日考勤分析
    function dateAtteAnalysisApiView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $teacherOne = $this->Show_css->getFieldOne("app_teacher", "teacher_branch", "teacher_id = '{$request['teacher_id']}'");
        $schoolOne = $this->Show_css->getFieldOne("app_school", "school_branch", "school_id = '{$request['school_id']}'");
        $classOne = $this->Show_css->getFieldOne("eas_classes", "class_branch", "class_id = '{$request['class_id']}'");
        $studentOne = $this->Show_css->getFieldOne("app_student", "student_branch", "student_id = '{$request['student_id']}'");

        $kddAuthprivModel = new KddkiddataModel();
        $res = $kddAuthprivModel->dateAtteAnalysisApi($request, $teacherOne['teacher_branch'],$schoolOne['school_branch'],$classOne['class_branch'],$studentOne['student_branch']);

        ajax_return($res);
    }


    //班级月考勤分析
    function monthAtteAnalysisApiView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $teacherOne = $this->Show_css->getFieldOne("app_teacher", "teacher_branch", "teacher_id = '{$request['teacher_id']}'");
        $schoolOne = $this->Show_css->getFieldOne("app_school", "school_branch", "school_id = '{$request['school_id']}'");
        $classOne = $this->Show_css->getFieldOne("eas_classes", "class_branch", "class_id = '{$request['class_id']}'");

        $kddAuthprivModel = new KddkiddataModel();
        $res = $kddAuthprivModel->monthAtteAnalysisApi($request, $teacherOne['teacher_branch'],$schoolOne['school_branch'],$classOne['class_branch']);

        ajax_return($res);
    }

    //学生考勤
    function checkAttendanceActionView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $teacherOne = $this->Show_css->getFieldOne("app_teacher", "teacher_branch", "teacher_id = '{$request['teacher_id']}'");
        $schoolOne = $this->Show_css->getFieldOne("app_school", "school_branch", "school_id = '{$request['school_id']}'");
        $classOne = $this->Show_css->getFieldOne("eas_classes", "class_branch", "class_id = '{$request['class_id']}'");
        $studentOne = $this->Show_css->getFieldOne("app_student", "student_branch", "student_id = '{$request['student_id']}'");

        $kddAuthprivModel = new KddkiddataModel();
        $res = $kddAuthprivModel->checkAttendanceAction($request, $teacherOne['teacher_branch'],$schoolOne['school_branch'],$classOne['class_branch'],$studentOne['student_branch']);

        ajax_return($res);
    }

    //班级一键考勤
    function allCheckAttendanceActionView()
    {
        $request = Input('post.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $teacherOne = $this->Show_css->getFieldOne("app_teacher", "teacher_branch", "teacher_id = '{$request['teacher_id']}'");
        $schoolOne = $this->Show_css->getFieldOne("app_school", "school_branch", "school_id = '{$request['school_id']}'");
        $classOne = $this->Show_css->getFieldOne("eas_classes", "class_branch", "class_id = '{$request['class_id']}'");

        $kddAuthprivModel = new KddkiddataModel();
        $res = $kddAuthprivModel->checkAttendanceAction($request, $teacherOne['teacher_branch'],$schoolOne['school_branch'],$classOne['class_branch']);

        ajax_return($res);
    }



    //获取教师token
    function getStafferTokenView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $this->ThisVerify($request);//验证账户
        $teacherOne = $this->Show_css->getFieldOne("app_teacher", "teacher_mobile", "teacher_id = '{$request['teacher_id']}'");
        $schoolOne = $this->Show_css->getFieldOne("app_school", "school_branch", "school_id = '{$request['school_id']}'");
        $kddAuthprivModel = new KddkiddataModel();
        $res = $kddAuthprivModel->getStafferToken($request, $teacherOne['teacher_mobile'],$schoolOne['school_branch']);

        ajax_return($res);
    }





    //结尾魔术函数
    function __destruct()
    {

    }
}
