<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2018/10/6
 * Time: 17:21
 */

namespace Work\Controller\Stuappapi;

class PkcDubshowController extends viewTpl
{
	public $u;
	public $t;
	public $c;
	
	function __construct()
	{
		parent::__construct();
		$this->u = $this->router->getController();
		$this->t = $this->router->getUrl();
		$this->c = $this->router->getAction();
		
	}



    //配音秀首页
    function getShowHomeView(){
        $request=Input('get.','','trim,addslashes');
        $this->ThisVerify($request);

        $Media=new \Model\Stuappapi\PkcDubshowModel();
        $res=$Media->getShowHome($request);
        if($res){
            $data = array('error' => '0', 'errortip' => '获取资源成功', 'result' => $res);
            ajax_return($data);
        }else{
            $data = array('error' => '0', 'errortip' => '暂无资源', 'result' => $res);
            ajax_return($data);
        }
    }
    //配音秀首页
    function getErShowHomeView(){
        $request=Input('get.','','trim,addslashes');
        $this->ThisVerify($request);

        $Media=new \Model\Stuappapi\PkcDubshowModel();
        $res=$Media->getErShowHome($request);
        if($res){
            $data = array('error' => '0', 'errortip' => '获取资源成功', 'result' => $res);
            ajax_return($data);
        }else{
            $data = array('error' => '0', 'errortip' => '暂无资源', 'result' => $res);
            ajax_return($data);
        }
    }

    //学前主题分类列表
    function textbookCateView(){
        $request=Input('get.','','trim,addslashes');
        $this->ThisVerify($request);

        $Media=new \Model\Stuappapi\PkcDubshowModel();
        $res=$Media->textbookCate($request);
        if($res){
            $result = array();
            $result["list"] = $res;
            $data = array('error' => '0', 'errortip' => '获取成功', 'result' => $result);
            ajax_return($data);
        }else{
            $result = array();
            $result["list"] = array();
            $data = array('error' => '0', 'errortip' => '暂无资源', 'result' => $result);
            ajax_return($data);
        }
    }

    //获取单个音频录音内容
    function getAudioOneView(){
        $request=Input('get.','','trim,addslashes');
        $this->ThisVerify($request);

        $Media=new \Model\Stuappapi\PkcDubshowModel();
        $res=$Media->getAudioOne($request);
        if($res){
            $result = array();
            $result["list"] = $res;
            $data = array('error' => '0', 'errortip' => '获取成功', 'result' => $result);
            ajax_return($data);
        }else{
            $result = array();
            $result["list"] = array();
            $data = array('error' => '0', 'errortip' => '暂无资源', 'result' => $result);
            ajax_return($data);
        }
    }

    //获取单个视频录音内容
    function getVideoOneView(){
        $request=Input('get.','','trim,addslashes');
        $this->ThisVerify($request);

        $Media=new \Model\Stuappapi\PkcDubshowModel();
        $res=$Media->getVideoOne($request);
        if($res){
            $result = array();
            $result["list"] = $res;
            $data = array('error' => '0', 'errortip' => '获取成功', 'result' => $result);
            ajax_return($data);
        }else{
            $result = array();
            $result["list"] = array();
            $data = array('error' => '0', 'errortip' => '暂无资源', 'result' => $result);
            ajax_return($data);
        }
    }

    //提交趣配音
    function SubmitAudioView(){
        $request = Input('post.','','trim,addslashes');
        // $this->ThisVerify($request);

        $Media = new \Model\Stuappapi\PkcDubshowModel();
        $res = $Media->SubmitAudio($request);
        if($res){
            $result = array();
            $result["list"] = $res;
            $data = array('error' => '0', 'errortip' => '配音提交成功', 'result' => $result);
            ajax_return($data);
        }else{
            $result = array();
            $result["list"] = (object)array();
            $data = array('error' => '0', 'errortip' => $Media->errortip, 'result' => $result);
            ajax_return($data);
        }
    }

    //我的作品
    function MyWorksView(){
        $request=Input('get.','','trim,addslashes');
        $this->ThisVerify($request);

        $Media = new \Model\Stuappapi\PkcDubshowModel();
        $res = $Media->MyWorks($request);

        if($res){
            $result = array();
            $result["list"] = $res;
            $data = array('error' => '0', 'errortip' => '获取资源成功', 'result' => $result);
            ajax_return($data);
        }else{
            $result = array();
            $result["list"] = array();
            $data = array('error' => '0', 'errortip' => '暂无资源', 'result' => $result);
            ajax_return($data);
        }
    }

    //我的作品
    function myWorksOneInfoView(){
        $request=Input('get.','','trim,addslashes');
        $this->ThisVerify($request);

        $Media=new \Model\Stuappapi\PkcDubshowModel();
        $res=$Media->MyWorksOneInfo($request);

        if($res){
            $result = array();
            $result["list"] = $res;
            $data = array('error' => '0', 'errortip' => '获取资源成功', 'result' => $result);
            ajax_return($data);
        }else{
            $result = array();
            $result["list"] = array();
            $data = array('error' => '0', 'errortip' => '暂无资源', 'result' => $result);
            ajax_return($data);
        }
    }


}