<?php
/**
 * ============================================================================
 * 版权所有 : https://www.mohism.cn
 * 网站地址 : https://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2016/12/15
 * Time: 15:45
 */

namespace Work\Controller\Manage;


class TaskItemsController extends viewTpl
{
    public $data;
    public $u;
    public $t;
    public $c;
    public $iuser;
    public $Viewhtm;

    //预加载处理类
    function __construct()
    {
        parent::__construct();
        if (!$this->check_login()) {
            $this->LoginView();
        }
        if ($this->check_login()) {
            $this->smarty->assign("iuser", $this->UserLogin);
            $this->iuser = $this->UserLogin;
        }
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->Viewhtm = $this->router->getController() . "/" . $this->router->getUrl() . ".htm";

        $moduleOne = $this->Show_css->getOne("cms_module", "module_mark='{$this->u}'");
        $this->smarty->assign("moduleOne", $moduleOne);
    }

    //主页
    function HomeView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        if (!isset($request['p'])) {
            $p = '1';
        } else {
            $p = $request['p'];
        }

        $datawhere = " 1 ";
        $pageurl = "/{$this->u}/{$this->t}?";
        if ($request['site_id']) {
            $pageurl .= "&site_id={$request['site_id']}";
        }

        $datatype = array();
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (c.course_cnname like '%{$request['keyword']}%' or c.course_branch like '%{$request['keyword']}%')";
            $pageurl .= "&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }

        if (isset($request['fromchannel']) && $request['fromchannel'] !== '') {
            $datawhere .= " and c.fromchannel = '{$request['fromchannel']}'";
            $pageurl .= "&fromchannel={$request['fromchannel']}";
            $datatype['fromchannel'] = $request['fromchannel'];
        }

        if (isset($request['coursecat_branch']) && $request['coursecat_branch'] !== '') {
            $datawhere .= " and c.coursecat_branch = '{$request['coursecat_branch']}'";
            $pageurl .= "&coursecat_branch={$request['coursecat_branch']}";
            $datatype['coursecat_branch'] = $request['coursecat_branch'];
        }

        $sql = "SELECT c.course_isforbidden,c.course_id,c.course_cnname,c.course_series,c.course_hours,c.course_times,c.fromchannel,c.coursecat_branch,c.course_branch,c.course_times,co.coursecat_cnname,c.course_isshowexam,c.course_forbid,c.course_kctvopen,course_assist_task,c.course_isrecordtask,c.course_isfamily,c.course_isjjopen
                ,(SELECT count(h.times_id) FROM eas_course_times as h WHERE h.course_branch = c.course_branch) as timesNum
                ,(select familytype_id from app_familycode as fc where fc.course_id =c.course_id limit 0,1 ) as familytype_id
                FROM eas_course AS c 
                LEFT JOIN eas_code_coursecat AS co ON c.coursecat_branch = co.coursecat_branch 
                WHERE {$datawhere} 
                ORDER BY c.coursecat_branch DESC,c.course_branch ASC";

        $db_nums = $Show_css->select("SELECT COUNT(c.course_id) FROM eas_course AS c LEFT JOIN eas_code_coursecat AS co ON c.coursecat_branch = co.coursecat_branch WHERE {$datawhere} ORDER BY c.course_id DESC");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums[0][0];

        $datalist = $Show_css->dbwherePage($sql, $allnum, '15', $pageurl . '&p=', $p, '10', '2');

        $smarty->assign("pagelist", $datalist['pages']);//筛选信息

        $smarty->assign("dataList", $datalist['cont']);
        $smarty->assign("datatype", $datatype);

        $categoryList = $this->Show_css->getFieldquery('eas_code_coursecat', 'coursecat_branch,coursecat_cnname', "");
        $this->smarty->assign("categoryList", $categoryList);
    }

    function ImportView(){
    }

    function ImportAllExcelView(){
        $request = Input('post.','','trim,addslashes');
        if($_FILES['uploadFile']['name'] == '' || $_FILES['uploadFile']['error'] == '1'){
            $PlayInfoVar = array();
            $PlayInfoVar['picbooks_branch'] = "导入出错";
            $PlayInfoVar['picbooks_title'] = "导入出错";
            $PlayInfoVar['error'] = "1";
            $PlayInfoVar['errortip'] = "文件不存在";
            $PlayInfo[] = $PlayInfoVar;
            $this->smarty->assign("PlayInfo", $PlayInfo);
            exit;
        }

        $fileType = array('xls','csv','xlsx');
        $uploadfile = new \Webfile($_FILES['uploadFile'],$files_dir='../static/file', $size = 2097152*10,$fileType);
        $up_file = $uploadfile->upload();

        $PlayInfo = array();
        if($up_file && $uploadfile->updatastatus) {
            $ExeclName = array();
            $ExeclName['序号'] = "sort";
            $ExeclName['课程别编号'] = "course_branch";
            $ExeclName['班级已上课周次'] = "now_times_sort";
            $ExeclName['任务id'] = "taskitems_id";
            $ExeclName['任务名称'] = "taskitems_title";
            $ExeclName['绘本id'] = "picbooks_id";
            $ExeclName['任务模块'] = "plate_ids";
            $ExeclName['常驻模块'] = "perplate_ids";

            $WorkerList = execl_to_array($up_file, $ExeclName);
            unset($WorkerList[0]);

            if ($WorkerList) {
                foreach ($WorkerList as $WorkerrVar) {
                    if ($WorkerrVar['sort'] !== '' && $WorkerrVar['course_branch'] !== '' && $WorkerrVar['times_sort'] !== '' && $WorkerrVar['taskitems_id'] !== '' && $WorkerrVar['picbooks_id'] !== '' ) {
                        $workersList[] = $WorkerrVar;
                    } else {
                        $PlayInfoVar = array();
                        $PlayInfoVar['picbooks_branch'] = $WorkerrVar['picbooks_branch'];
                        $PlayInfoVar['picbooks_title'] = $WorkerrVar['picbooks_title'];
                        $PlayInfoVar['error'] = "1";
                        $PlayInfoVar['errortip'] = "导入媒体信息不完整";
                        $PlayInfo[] = $PlayInfoVar;
                    }
                }
            }
            if (count($workersList) > 100000) {
                ajax_return(array('error' => 1, 'errortip' => "导入数量不能大于100000!", "bakfuntion" => "errormotify"));
            }
            if($workersList) {
                foreach ($workersList as $workersVar) {

                    $workersVar['sort'] = trim($workersVar['sort']);
                    $workersVar['course_branch'] = trim($workersVar['course_branch']);
                    $workersVar['now_times_sort'] = trim($workersVar['now_times_sort']);
                    $workersVar['taskitems_id'] = trim($workersVar['taskitems_id']);
                    $workersVar['taskitems_title'] = trim($workersVar['taskitems_title']);
                    $workersVar['picbooks_id'] = trim($workersVar['picbooks_id']);
                    $workersVar['plate_ids'] = trim($workersVar['plate_ids']);
                    $workersVar['perplate_ids'] = trim($workersVar['perplate_ids']);


                    $times_sort=trim($workersVar['sort'])+trim($workersVar['now_times_sort']);

//                    $data=array();
//                    $data['sort'] = $workersVar['sort'];
//                    $data['course_branch'] = $workersVar['course_branch'];
//                    $data['now_times_sort'] = $workersVar['now_times_sort'];
//                    $data['taskitems_id'] = $workersVar['taskitems_id'];
//                    $data['taskitems_title'] = $workersVar['taskitems_title'];
//                    $data['picbooks_id'] = $workersVar['picbooks_id'];
//                    $data['plate_ids'] = $workersVar['plate_ids'];
//                    $data['perplate_ids'] = $workersVar['perplate_ids'];
//                    $data['times_sort'] = $times_sort;

                    $timesOne=$this->Show_css->getFieldOne("eas_course_times","times_id","course_branch='{$workersVar['course_branch']}' and times_sort='{$times_sort}'");


                    $taskOne=$this->Show_css->getFieldOne("app_taskitems","taskitems_id","taskitems_id='{$workersVar['taskitems_id']}' and course_branch='{$workersVar['course_branch']}' and times_id='{$timesOne['times_id']}'");

//                    $taskOne=$this->Show_css->getFieldOne("app_taskitems","taskitems_id","course_branch='{$workersVar['course_branch']}' and taskitems_tasktype=8 and times_id='{$timesOne['times_id']}'");

                    if($taskOne){

                        $data=array();
                        $data['taskitems_id']=$taskOne['taskitems_id'];
                        $data['picbooks_id']=$workersVar['picbooks_id'];
                        $data['now_times_sort']=$workersVar['now_times_sort'];
                        $data['times_sort']=$times_sort;
                        $data['plate_ids']=$workersVar['plate_ids'];
                        $data['perplate_ids']=$workersVar['perplate_ids'];
                        $data['taskitems_title']=addslashes($workersVar['taskitems_title']);

                        if(!$this->Show_css->getFieldOne("irc_taskitems_picbooks","taskitems_id","taskitems_id='{$taskOne['taskitems_id']}' and picbooks_id='{$workersVar['picbooks_id']}' and times_sort='{$times_sort}' and now_times_sort='{$workersVar['now_times_sort']}'")){

                            if($this->Show_css->insertData("irc_taskitems_picbooks",$data)){
                                $PlayInfoVar['value1'] = $taskOne['taskitems_id'];
                                $PlayInfoVar['value2'] = $workersVar['picbooks_id'];
                                $PlayInfoVar['error'] = "0";
                                $PlayInfoVar['errortip'] = "导入成功";
                            }else{
                                $PlayInfoVar['value1'] = $taskOne['taskitems_id'];
                                $PlayInfoVar['value2'] = $workersVar['picbooks_id'];
                                $PlayInfoVar['error'] = "1";
                                $PlayInfoVar['errortip'] = "新增失败";
                            }



                        }else{
                            $PlayInfoVar['value1'] = $taskOne['taskitems_id'];
                            $PlayInfoVar['value2'] = $workersVar['picbooks_id'];
                            $PlayInfoVar['error'] = "1";
                            $PlayInfoVar['errortip'] = "重复导入";
                        }
                    }else{
                        $PlayInfoVar['value1'] = $workersVar['taskitems_id'];
                        $PlayInfoVar['value2'] = $workersVar['picbooks_id'];
                        $PlayInfoVar['error'] = "1";
                        $PlayInfoVar['errortip'] = "无对应任务";
                    }

                    $PlayInfo[] = $PlayInfoVar;
                }
            }

        }else{
            $PlayInfoVar = array();
            $PlayInfoVar['picbooks_branch'] = "导入出错";
            $PlayInfoVar['picbooks_title'] = "导入出错";
            $PlayInfoVar['error'] = "1";
            $PlayInfoVar['errortip'] = "文件不存在";
            $PlayInfo[] = $PlayInfoVar;
        }

        $this->smarty->assign("PlayInfo", $PlayInfo);
    }




    function HourView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $ClasscodeOne = $this->Show_css->getOne("eas_course", "course_id='{$request['course_id']}'");

        $this->smarty->assign("ClasscodeOne", $ClasscodeOne);

        $datawhere = "1";
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (h.hour_name like '%{$request['keyword']}%') ";
            $datatype['keyword'] = $request['keyword'];
        }

        $sql = "SELECT h.*,cd.course_branch,
                (SELECT count(i.taskitems_id) FROM app_taskitems as i where i.times_id = h.times_id and i.taskitems_putaway=1) as itemnums
                 FROM eas_course_times AS h
                 LEFT JOIN  eas_course AS cd ON  cd.course_branch =  h.course_branch
                 WHERE {$datawhere} AND h.course_branch='{$ClasscodeOne['course_branch']}' ORDER BY h.times_sort ASC";

        $hourList = $this->Show_css->selectClear($sql);
        $this->smarty->assign("hourList", $hourList);

        $this->smarty->assign("datatype", $datatype);
    }

    function EditHourView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $hourOne = $this->Show_css->getOne("eas_course_times", "times_id='{$request['times_id']}'");
        $this->smarty->assign("dataVar", $hourOne);

        $ClasscodeOne = $this->Show_css->getOne("eas_course", "course_branch='{$hourOne['course_branch']}'");
        $surveyList = $this->Show_css->selectClear("select  survey_id, survey_name from  app_survey  where  '1' ");
        $this->smarty->assign("surveyList", $surveyList);

        $this->smarty->assign("ClasscodeOne", $ClasscodeOne);

        $this->Viewhtm = $this->router->getController() . "/" . "AddHour.htm";

        $this->smarty->assign("act", "EditHour");
    }

    //提交处理机制
    function EditHourAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        if ($request['times_name'] == '' || $request['times_sort'] == '') {
            ajax_return(array('error' => 1, 'errortip' => "课时名称／课时排序必须设置!", "bakfuntion" => "warningFromTip"));
        }
        $classcodeOne = $this->Show_css->getFieldOne("eas_course", "course_branch", "course_id='{$request['course_id']}'");
        $data = array();
        $data['course_branch'] = $classcodeOne['course_branch'];
        $data['times_name'] = $request['times_name'];
        $data['times_branch'] = $classcodeOne['course_branch'] . '_' . $request['times_sort'];
        $data['times_sort'] = $request['times_sort'];
        $data['times_topic'] = $request['times_topic'];
        $data['times_target'] = $request['times_target'];
        $data['times_preview'] = $request['times_preview'];
        $data['times_propose'] = $request['times_propose'];
        $data['times_sector'] = $request['times_sector'];
        $data['times_compare'] = $request['times_compare'];
//        $data['times_task'] = $request['times_task'];
//        $data['survey_id'] = $request['survey_id'];
        if ($this->Show_css->updateData("eas_course_times", "times_id = '{$request['times_id']}'", $data)) {
            $this->Recordweblog($request['site_id'], $this->Module['module_id'], $this->c, "修改课时ID:{$request['times_id']}");
            ajax_return(array('error' => 0, 'errortip' => "课时修改成功!", "bakfuntion" => "successFromTip", "bakurl" => "/{$this->u}/Hour?site_id={$request['site_id']}&course_id={$request['course_id']}"));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "修改失败!", "bakfuntion" => "errormotify"));
        }
    }

    function TaskItemView(){
        $request = Input('get.','','trim,addslashes');
        $stutaskOne = $this->Show_css->selectOne("select c.course_id,c.course_cnname,h.times_name,h.times_id from eas_course_times as h,eas_course as c
WHERE h.course_branch = c.course_branch and h.times_id = '{$request['times_id']}' ");

        $this->smarty->assign("stutaskOne", $stutaskOne);

        //任务清单
        $itemwhere = "i.times_id = '{$request['times_id']}'";
        if(isset($request['keyword']) && $request['keyword'] !==''){
            $itemwhere .= " and (i.taskitems_title like '%{$request['keyword']}%')";
        }



        if (isset($request['times_id']) && $request['times_id'] !== '') {
            $timesOne=$this->Show_css->getFieldOne("eas_course_times","times_name","times_id='{$request['times_id']}'");
            $datatype['times_name'] = $timesOne['times_name'];
        }

        if(isset($request['taskitems_putaway']) && $request['taskitems_putaway'] !==''){
            $datatype['taskitems_putaway'] = $request['taskitems_putaway'];
            $itemwhere .= " and i.taskitems_putaway = '{$request['taskitems_putaway']}'";
        }
        if(isset($request['taskitems_week']) && $request['taskitems_week'] !==''){
            $datatype['taskitems_week'] = $request['taskitems_week'];
            $itemwhere .= " and i.taskitems_week = '{$request['taskitems_week']}'";
        }

        if(isset($request['taskitems_tasktype']) && $request['taskitems_tasktype'] !==''){
            $datatype['taskitems_tasktype'] = $request['taskitems_tasktype'];
            $itemwhere .= " and i.taskitems_tasktype = '{$request['taskitems_tasktype']}'";
        }
        if(isset($request['taskitems_taskclass']) && $request['taskitems_taskclass'] !==''){
            $datatype['taskitems_taskclass'] = $request['taskitems_taskclass'];
            $itemwhere .= " and i.taskitems_taskclass = '{$request['taskitems_taskclass']}'";
        }

        $sql = "select i.*,b.modetype_name
                ,(SELECT v.list_name FROM cms_variablelist as v WHERE v.variable_id = '9' and v.list_parameter = i.taskitems_tasktype) as tasktypename
                ,(SELECT v.list_name FROM cms_variablelist as v WHERE v.variable_id = '10' and v.list_parameter = i.taskitems_taskclass) as taskclassname
                from app_taskitems as i
                left join app_taskitems_modetype as b on i.taskitems_modetype=b.modetype_id
                WHERE {$itemwhere} 
                ORDER BY i.taskitems_week ASC,i.taskitems_sort ASC";

        $itemList = $this->Show_css->selectClear($sql);

        $weekList=$this->Show_css->getList("app_taskitems_weektype"," weektype_status=1 order by weektype_value asc");
        $this->smarty->assign("weekList", $weekList);

        $this->smarty->assign("itemList",$itemList);
        $this->smarty->assign("datatype",$datatype);
    }

    function EditTaskItemAction(){
        $request = Input('get.','','trim,addslashes');
        $data = array();
        $data[$request['field']] = $request['value'];
        if($this->Show_css->updateData("app_taskitems","taskitems_id = '{$request['id']}'",$data)){
            ajax_return(array('error' => 0,'errortip' => "修改成功!"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "修改失败!"));
        }
    }

    function AddItemView(){

        $request = Input('get.','','trim,addslashes');
        $sql="select b.course_branch,b.course_cnname from eas_course_times as a,eas_course as b where a.course_branch=b.course_branch and a.times_id='{$request['course_times_id']}'";

        $stutaskOne = $this->Show_css->selectOne($sql);

        $this->smarty->assign("stutaskOne", $stutaskOne);

        $tasktypeOne = $this->Show_css->selectOne("select v.list_name,v.list_parameter from cms_variablelist as v
WHERE v.list_parameter = '{$request['taskitems_tasktype']}' and v.variable_id = '9'");

        $this->smarty->assign("tasktypeOne", $tasktypeOne);

        $modeList=$this->Show_css->getList("app_taskitems_modetype","1 order by modetype_sort asc");
        $this->smarty->assign("modeList", $modeList);

        $playtype2=$this->Show_css->getList("app_taskitems_playtype"," playtype_tasktype = 2 order by playtype_id asc");
        $this->smarty->assign("playtype2", $playtype2);

        $weekList=$this->Show_css->getList("app_taskitems_weektype"," weektype_status=1 order by weektype_value asc");
        $this->smarty->assign("weekList", $weekList);

        $sql = "select times_id,course_branch,times_name
                from eas_course_times 
                where course_branch='{$stutaskOne['course_branch']}'
                order by times_sort asc
                ";
        $timesList=$this->Show_css->selectClear($sql);

        $this->smarty->assign("timesList",$timesList);

        $dataVar = array();
        $dataVar['taskitems_tasktype'] = $request['taskitems_tasktype'];
        $dataVar['times_id'] = $request['course_times_id'];

        switch ($request['taskitems_tasktype']){
            case 1:
                $dataVar['fucmodule_branch']='Adventmap';
                break;
            case 2:
                $dataVar['fucmodule_branch']='Challenge';
                break;
            case 4:
                $dataVar['fucmodule_branch']='Library';
                break;
            case 6:
                $dataVar['fucmodule_branch']='Dubshow';
                break;
            case 8:
                $dataVar['fucmodule_branch']='Readcvd';
                break;
            case 9:
                $dataVar['fucmodule_branch']='Ebookkid';
                break;
            case 10:
                $dataVar['fucmodule_branch']='Bailehui';
                break;

        }

        if($dataVar['fucmodule_branch']!=''){
            $fucmoduleOne=$this->Show_css->getFieldOne("pro_product_fucmodule","fucmodule_name","fucmodule_branch='{$dataVar['fucmodule_branch']}'");
            $dataVar['fucmodule_name']=$fucmoduleOne['fucmodule_name'];
        }

        $this->smarty->assign("dataVar",$dataVar);

        if($request['taskitems_tasktype']==8){
            $plateList=$this->Show_css->getList("app_picbooks_plate","","order by plate_sort asc");
            $this->smarty->assign("plateList",$plateList);
        }

        $this->smarty->assign("act","AddItem");

        $this->Viewhtm = $this->router->getController()."/"."EditItem/tasktype{$request['taskitems_tasktype']}.htm";

    }

    //提交处理机制
    function AddItemAction()
    {
        $request = Input('post.','','trim,addslashes');

        $sql = "select a.course_branch,b.course_mold from eas_course_times as a,eas_course as b where a.course_branch=b.course_branch and a.times_id='{$request['times_id']}'";

        $timesOne=$this->Show_css->selectOne($sql);

        if(!$timesOne){
            ajax_return(array('error' => 1,'errortip' => "课次不存在!","bakfuntion"=>"warningFromTip"));
        }

        if(!isset($request['times_id']) || $request['times_id'] == ''){
            ajax_return(array('error' => 1,'errortip' => "课次必须传!","bakfuntion"=>"warningFromTip"));
        }

        if(!isset($request['taskitems_tasktype']) || $request['taskitems_tasktype'] == ''){
            ajax_return(array('error' => 1,'errortip' => "任务类型必须设置!","bakfuntion"=>"warningFromTip"));
        }

//        if(!isset($request['taskitems_taskclass']) || $request['taskitems_taskclass'] == '' ){
//            ajax_return(array('error' => 1,'errortip' => "任务模式必须设置!","bakfuntion"=>"warningFromTip"));
//        }

        if($timesOne['course_mold']!=1){
            if(!isset($request['taskitems_modetype']) || $request['taskitems_modetype'] == '' ){
                ajax_return(array('error' => 1,'errortip' => "任务分类必须设置!","bakfuntion"=>"warningFromTip"));
            }
        }

        $data=array();

        $data['course_branch']=$timesOne['course_branch'];
        $data['textbook_id']=$request['textbook_id']?$request['textbook_id']:0;
        $data['times_id']=$request['times_id'];
        $data['taskitems_sort']=$request['taskitems_sort'];
        if($timesOne['course_mold']==1 || $timesOne['course_mold']==2){
            $data['taskitems_week']=$request['taskitems_week'];
        }
        $data['taskitems_tasktype']=$request['taskitems_tasktype'];
        $data['taskitems_taskclass']=$request['taskitems_taskclass'];
        $data['taskitems_modetype']=$request['taskitems_modetype'];
        $data['taskitems_title']=$request['taskitems_title'];
        $data['fucmodule_branch']=$request['fucmodule_branch'];
        $data['taskitems_content']=$request['taskitems_content'];
        $data['taskitems_url']=$request['taskitems_url'];
        $data['taskitems_isbook']=$request['taskitems_isbook'];
        $data['taskitems_diamondprice']=$request['taskitems_diamondprice'];

        $data['taskitems_playtype']=$request['taskitems_playtype'];//玩法ID
        $data['item_createtime']=time();

        switch ($request['taskitems_tasktype']){
            case 0:
                if(!isset($request['media_id']) || $request['media_id'] == ''){
                    ajax_return(array('error' => 1,'errortip' => "KCTV视频必须设置!","bakfuntion"=>"warningFromTip"));
                }
                break;
            case 1:
                if(!isset($request['steps_id']) || $request['steps_id'] == ''){
                    ajax_return(array('error' => 1,'errortip' => "复习关卡必须设置!","bakfuntion"=>"warningFromTip"));
                }
                break;
            case 2:
                if(!isset($request['testpaper_id']) || $request['testpaper_id'] == ''){
                    ajax_return(array('error' => 1,'errortip' => "真题试卷必须设置!","bakfuntion"=>"warningFromTip"));
                }
                break;
            case 4:
                if(!isset($request['readbooks_id']) || $request['readbooks_id'] == ''){
                    ajax_return(array('error' => 1,'errortip' => "阅读图书必须设置!","bakfuntion"=>"warningFromTip"));
                }
                break;
            case 6:
                if(!isset($request['audiorecords_id']) || $request['audiorecords_id']==''){
                    ajax_return(array('error' => 1,'errortip' => "趣配音必须设置!","bakfuntion"=>"warningFromTip"));
                }
                break;
            case 8:
                if(!isset($request['picbooks_id']) || $request['picbooks_id']==''){
                    ajax_return(array('error' => 1,'errortip' => "阅读绘本必须设置!","bakfuntion"=>"warningFromTip"));
                }
                break;
            case 9:
                if(isset($request['isall']) && $request['isall']!='' && $request['isall']==0 && (!isset($request['textbookcate_id']) || $request['textbookcate_id']==0 || $request['textbookcate_id']=='' )){
                    ajax_return(array('error' => 1,'errortip' => "堡贝乐主题必须设置!","bakfuntion"=>"warningFromTip"));
                }
                break;
            case 10:
                if(!isset($request['class_id']) || $request['class_id']==''){
                    ajax_return(array('error' => 1,'errortip' => "百乐汇分类必须选择!","bakfuntion"=>"warningFromTip"));
                }
                break;

        }


        if($taskitems_id = $this->Show_css->insertData("app_taskitems",$data)){

            $data=array();
            $data['taskitems_id']=$taskitems_id;

            switch ($request['taskitems_tasktype']){
                case 0:
                    $data['media_id']=$request['media_id'];
                    $data['can_drag']=$request['can_drag'];
                    $this->Show_css->insertData("app_taskitems_media",$data);
                    break;
                case 1:
                    $data['steps_id']=$request['steps_id'];
                    $this->Show_css->insertData("app_taskitems_steps",$data);
                    break;
                case 2:
                    $data['testpaper_id']=$request['testpaper_id'];
                    $this->Show_css->insertData("app_taskitems_testpaper",$data);
                    break;
                case 4:
                    $data['readbooks_id']=$request['readbooks_id'];
                    $data['plate_ids']=$request['plate_ids'];
                    $this->Show_css->insertData("app_taskitems_readbooks",$data);
                    break;
                case 6:
                    $data['audiorecords_id']=$request['audiorecords_id'];
                    $this->Show_css->insertData("app_taskitems_audiorecords",$data);
                    break;
                case 8:
                    $data['picbooks_id']=$request['picbooks_id'];

                    if(array_intersect($request['plate1'],$request['plate2'])){
                        ajax_return(array('error' => 1,'errortip' => "任务板块与常驻包括不可存在交集!","bakfuntion"=>"warningFromTip"));

                    }

                    if (isset($request['plate1']) && count($request['plate1']) > 0) {
                        $idStr='';
                        foreach ($request['plate1'] as $v) {
                            if($idStr){
                                $idStr.=','.$v;
                            }else{
                                $idStr=$v;
                            }
                        }

                        $data['plate_ids']=$idStr;
                    }

                    if (isset($request['plate2']) && count($request['plate2']) > 0) {
                        $idStr='';
                        foreach ($request['plate2'] as $v) {
                            if($idStr){
                                $idStr.=','.$v;
                            }else{
                                $idStr=$v;
                            }
                        }

                        $data['perplate_ids']=$idStr;
                    }

                    $this->Show_css->insertData("app_taskitems_picbooks",$data);
                    break;
                case 9:
                    if(isset($request['isall']) && $request['isall']!='' && $request['isall']=='0' && isset($request['textbookcate_id']) && $request['textbookcate_id']!=='' ){
                        $data['textbookcate_id']=$request['textbookcate_id'];
                        $data['steps_id']=$request['steps_id']?$request['steps_id']:0;
                        $this->Show_css->insertData("app_taskitems_ekidbook",$data);
                    }
                    break;

                case 10:
                    $data['class_id']=$request['class_id'];
                    $data['can_drag']=$request['can_drag'];
                    $this->Show_css->insertData("app_taskitems_blhclass",$data);
                    break;
            }

            $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"新增任务ID:{$taskitems_id}");
            ajax_return(array('error' => 0,'errortip' => "新增任务成功!","bakfuntion"=>"successFromTip","bakurl"=>"/{$this->u}/TaskItem?times_id={$request['times_id']}&site_id={$request['site_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"dangerFromTip"));
        }
    }

    function ChargerPutawayAction()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $taskOne = $this->Show_css->getOne("app_taskitems", "taskitems_id='{$request['taskitems_id']}'");
        $data = array();
        if ($taskOne['taskitems_putaway'] == '1') {
            $data['taskitems_putaway'] = "0";
        } else {
            $data['taskitems_putaway'] = "1";
        }
        if ($this->Show_css->updateData("app_taskitems", "taskitems_id = '{$request['taskitems_id']}'", $data)) {
            if ($data['taskitems_putaway'] == '1') {
                $this->Recordweblog($request['site_id'], $this->Module['module_id'], $this->c, "修改任务状态1，ID:{$request['taskitems_id']}");
                ajax_return(array('error' => 0, 'errortip' => "提交成功!", "bakfuntion" => "okmotify", "state" => "1"));
            } else {
                $this->Recordweblog($request['site_id'], $this->Module['module_id'], $this->c, "修改任务状态0，ID:{$request['taskitems_id']}");
                ajax_return(array('error' => 0, 'errortip' => "提交成功!", "bakfuntion" => "okmotify", "state" => "0"));
            }
        } else {
            ajax_return(array('error' => 1, 'errortip' => "修改失败!", "bakfuntion" => "errormotify"));
        }
    }


    function EditItemView(){
        $request = Input('get.','','trim,addslashes');


        $sql = "select a.*,b.fucmodule_name 
                from app_taskitems as a 
                left join pro_product_fucmodule as b on b.fucmodule_branch=a.fucmodule_branch 
                where a.taskitems_id='{$request['taskitems_id']}'";

        $taskOne = $this->Show_css->selectOne($sql);

        $stutaskOne = $this->Show_css->selectOne("select b.course_branch,b.course_cnname from eas_course_times as a,eas_course as b where a.course_branch=b.course_branch and a.times_id='{$taskOne['times_id']}'");
        $this->smarty->assign("stutaskOne", $stutaskOne);

        $modeList=$this->Show_css->getList("app_taskitems_modetype","1 order by modetype_sort asc");
        $this->smarty->assign("modeList", $modeList);

        $playtype2=$this->Show_css->getList("app_taskitems_playtype"," playtype_tasktype = 2 order by playtype_id asc");
        $this->smarty->assign("playtype2", $playtype2);

        $weekList=$this->Show_css->getList("app_taskitems_weektype"," weektype_status=1 order by weektype_value asc");
        $this->smarty->assign("weekList", $weekList);

        switch ($taskOne['taskitems_tasktype']){
            case 0:
                $qsql = "select a.media_id,b.media_name,a.can_drag  
                        from app_taskitems_media as a 
                        inner join eas_course_media as b on a.media_id=b.media_id";
                break;
            case 1:
                $qsql = "select a.steps_id,b.steps_title,c.maps_id,c.maps_title ,u.unit_name 
                        from app_taskitems_steps as a 
                        inner join app_maps_steps as b on a.steps_id=b.steps_id
                        inner join app_textbook_unit as u on u.unit_id=b.unit_id
                        inner join app_maps as c on c.maps_id=b.maps_id
                        ";
                break;
            case 2:
                $qsql = "select a.testpaper_id,b.testpaper_name
                        from app_taskitems_testpaper as a 
                        inner join app_testpaper as b on a.testpaper_id=b.testpaper_id";
                break;
            case 4:
                $qsql = "select a.readbooks_id,b.readbooks_title,a.plate_ids
                        from app_taskitems_readbooks as a 
                        inner join app_readbooks as b on a.readbooks_id=b.readbooks_id
                        ";
                break;
            case 6:
                $qsql = "select a.audiorecords_id,b.audiorecords_name 
                        from app_taskitems_audiorecords as a 
                        inner join app_audiorecords as b on a.audiorecords_id=b.audiorecords_id";
                break;
            case 8:
                $qsql = "select a.*,b.* 
                        from app_taskitems_picbooks as a 
                        inner join app_picbooks as b on a.picbooks_id=b.picbooks_id";
                break;
            case 9:
                $qsql = "select a.textbookcate_id,a.steps_id,b.*,f.category_name,d.textbook_name,e.textbooksuit_name 
                        from app_taskitems_ekidbook as a 
                        inner join app_ekidbook_textbookcate as b on a.textbookcate_id=b.textbookcate_id
                        left join app_ekidbook_steps as c on c.steps_id=a.steps_id
                        left join app_ekidbook_textbook AS d ON d.textbook_id = c.textbook_id
                        left join app_ekidbook_textbooksuit AS e ON e.textbooksuit_id = d.textbooksuit_id 
                        left join app_ekidbook_category as f on f.category_id = c.category_id
                        ";
                break;
            case 10:
                $qsql = "select a.class_id,b.class_name,b.class_showname,a.can_drag,b.class_mode
                        from app_taskitems_blhclass as a 
                        inner join app_media_class as b on a.class_id=b.class_id";
                break;
        }
        if($qsql){
            $qsql.=" where a.taskitems_id='{$request['taskitems_id']}'";

            $dataVar = $this->Show_css->selectOne($qsql." limit 0,1");

            if($taskOne['taskitems_tasktype']==9){
                if($dataVar){
                    $taskOne['isall']=0;
                }else{
                    $taskOne['isall']=1;
                }

            }

            if($taskOne['taskitems_tasktype']==8){

                if($dataVar['plate_ids']){
                    $dataVar['plate_idArray']=explode(",",$dataVar['plate_ids']);
                }else{
                    $dataVar['plate_idArray']=array();
                }

                if($dataVar['plate_idArray']){
                    $sql = "select * 
                            from app_picbooks_plate 
                            where plate_id in ({$dataVar['plate_ids']}) order by plate_sort asc";

                    $plateArray=$this->Show_css->selectClear($sql);

                    $this->smarty->assign("plateArray", $plateArray);


                    foreach($plateArray as $plateOne){


                        switch ($plateOne['plate_id']){
                            case 1:
                                $sql = "SELECT q.audio_id as id,q.audio_name,q.audio_url
                                        ,( SELECT COUNT( b.picbooks_id ) FROM app_taskitems_picaudio AS b WHERE b.taskitems_id = '{$taskOne['taskitems_id']}' AND b.picbooks_id = q.picbooks_id and b.plate_id='{$plateOne['plate_id']}' and b.audio_id=q.audio_id ) AS thestatus
                                        FROM app_picbooks_audio AS q
                                        WHERE q.picbooks_id='{$dataVar['picbooks_id']}'
                                        ORDER BY thestatus DESC,q.audio_id asc";

                                $itemList_1=$this->Show_css->selectClear($sql);

                                $this->smarty->assign('itemList'.$plateOne['plate_id'], $itemList_1);
                                break;
                            case 2:
                                //app_picbooks_pages

                                $sql = "SELECT q.pages_id as id,q.pages_sentence,q.pages_imageurl,q.pages_audiourl,q.pages_sort,q.pages_paragraph
                                        ,( SELECT COUNT( b.picbooks_id ) FROM app_taskitems_picpages AS b WHERE b.taskitems_id = '{$taskOne['taskitems_id']}' AND b.picbooks_id = q.picbooks_id and b.plate_id='{$plateOne['plate_id']}' and b.pages_id=q.pages_id ) AS thestatus
                                        FROM app_picbooks_pages AS q
                                        WHERE q.picbooks_id='{$dataVar['picbooks_id']}'
                                        ORDER BY thestatus DESC,q.pages_sort asc";
                                $itemList_2=$this->Show_css->selectClear($sql);

                                $this->smarty->assign('itemList'.$plateOne['plate_id'], $itemList_2);
                                break;
                            case 3:
                                //app_picbooks_words
                                $sql = "SELECT q.words_id as id,q.words_sentence,q.words_audiourl,q.words_imageurl
                                        ,( SELECT COUNT( b.picbooks_id ) FROM app_taskitems_picwords AS b WHERE b.taskitems_id = '{$taskOne['taskitems_id']}' AND b.picbooks_id = q.picbooks_id and b.plate_id='{$plateOne['plate_id']}' and b.words_id=q.words_id ) AS thestatus
                                        FROM app_picbooks_words AS q
                                        WHERE q.picbooks_id='{$dataVar['picbooks_id']}'
                                        ORDER BY thestatus DESC,q.words_id asc";

                                $itemList_3=$this->Show_css->selectClear($sql);

                                $this->smarty->assign('itemList'.$plateOne['plate_id'], $itemList_3);
                                break;
                            case 4:
                                //app_picbooks_pages
                                $sql = "SELECT q.pages_id as id,q.pages_sentence,q.pages_imageurl,q.pages_audiourl,q.pages_sort,q.pages_paragraph
                                        ,( SELECT COUNT( b.picbooks_id ) FROM app_taskitems_picpages AS b WHERE b.taskitems_id = '{$taskOne['taskitems_id']}' AND b.picbooks_id = q.picbooks_id and b.plate_id='{$plateOne['plate_id']}' and b.pages_id=q.pages_id ) AS thestatus
                                        FROM app_picbooks_pages AS q
                                        WHERE q.picbooks_id='{$dataVar['picbooks_id']}'
                                        ORDER BY thestatus DESC,q.pages_sort asc";
                                $itemList_4=$this->Show_css->selectClear($sql);

                                $this->smarty->assign('itemList'.$plateOne['plate_id'], $itemList_4);
                                break;
                            case 5:
                                //app_picbooks_exercises
                                $sql = "SELECT q.exercises_id as id,q.exercises_title,q.exercises_correct,q.exercises_audio
                                        ,( SELECT COUNT( b.picbooks_id ) FROM app_taskitems_picexercises AS b WHERE b.taskitems_id = '{$taskOne['taskitems_id']}' AND b.picbooks_id = q.picbooks_id and b.plate_id='{$plateOne['plate_id']}' and b.exercises_id=q.exercises_id ) AS thestatus
                                        FROM app_picbooks_exercises AS q
                                        WHERE q.picbooks_id='{$dataVar['picbooks_id']}'
                                        ORDER BY thestatus DESC,q.exercises_id asc";

                                $itemList_5=$this->Show_css->selectClear($sql);

                                $this->smarty->assign('itemList'.$plateOne['plate_id'], $itemList_5);
                                break;
                            case 6:
                                //app_picbooks_pages
                                $sql = "SELECT q.pages_id as id,q.pages_sentence,q.pages_imageurl,q.pages_audiourl,q.pages_sort,q.pages_paragraph
                                        ,( SELECT COUNT( b.picbooks_id ) FROM app_taskitems_picpages AS b WHERE b.taskitems_id = '{$taskOne['taskitems_id']}' AND b.picbooks_id = q.picbooks_id and b.plate_id='{$plateOne['plate_id']}' and b.pages_id=q.pages_id ) AS thestatus
                                        FROM app_picbooks_pages AS q
                                        WHERE q.picbooks_id='{$dataVar['picbooks_id']}'
                                        ORDER BY thestatus DESC,q.pages_sort asc";
                                $itemList_6=$this->Show_css->selectClear($sql);

                                $this->smarty->assign('itemList'.$plateOne['plate_id'], $itemList_6);
                                break;
                        }
                    }
                }

                if($dataVar['perplate_ids']){
                    $dataVar['perplate_idArray']=explode(",",$dataVar['perplate_ids']);
                }else{
                    $dataVar['perplate_idArray']=array();
                }

            }elseif($taskOne['taskitems_tasktype']==4){
                $plateArray=[
                    '1'=>'核心词汇',
                    '2'=>'伴读动画',
                    '3'=>'听故事',
                    '4'=>'亲子互动',
                ];

                $dataVar['plate_ids_name']=$plateArray[$dataVar['plate_ids']];

            }

            if($taskOne['taskitems_tasktype']==10){

                $taskOne['class_id']=$dataVar['class_id'];
                $taskOne['can_drag']=$dataVar['can_drag'];
                $taskOne['class_mode']=$dataVar['class_mode'];
                $taskOne['class_name']=$dataVar['class_name'];
                $taskOne['class_showname']=$dataVar['class_showname'];

                $sql = "SELECT q.item_id,q.item_title,q.item_weeknums,q.item_sort
                        ,( SELECT COUNT( b.item_id ) FROM app_taskitems_blhitem AS b WHERE b.taskitems_id = '{$taskOne['taskitems_id']}' AND b.item_id = q.item_id) AS thestatus
                        ,( SELECT b.taskitmes_sort FROM app_taskitems_blhitem AS b WHERE b.taskitems_id = '{$taskOne['taskitems_id']}' AND b.item_id = q.item_id) AS taskitmes_sort
                        FROM app_media_item AS q
                        WHERE q.class_id='{$dataVar['class_id']}'
                        ORDER BY thestatus DESC,q.item_sort asc,q.item_id asc";
                $blhitemList=$this->Show_css->selectClear($sql);
                $this->smarty->assign('blhitemList', $blhitemList);
            }

            if($taskOne['taskitems_tasktype']==0) {
                $taskOne['media_id'] = $dataVar['media_id'];
                $taskOne['media_name'] = $dataVar['media_name'];
                $taskOne['can_drag'] = $dataVar['can_drag'];
            }
        }


        $tasktypeOne = $this->Show_css->selectOne("select v.list_name,v.list_parameter from cms_variablelist as v
WHERE v.list_parameter = '{$taskOne['taskitems_tasktype']}' and v.variable_id = '9'");

        $this->smarty->assign("tasktypeOne", $tasktypeOne);


        if($taskOne['taskitems_tasktype']==8){
            $plateList=$this->Show_css->getList("app_picbooks_plate","","order by plate_sort asc");
            $this->smarty->assign("plateList",$plateList);
        }

        if($taskOne['textbook_id']!=0){
            $textbookOne = $this->Show_css->getFieldOne("app_textbook","textbook_cnname","textbook_id = '{$taskOne['textbook_id']}'");
            $taskOne['textbook_cnname'] = $textbookOne['textbook_cnname']."(".$taskOne['textbook_id'].")";
        }

        $sql = "select times_id,course_branch,times_name
                from eas_course_times 
                where course_branch='{$stutaskOne['course_branch']}'
                order by times_sort asc
                ";
        $timesList=$this->Show_css->selectClear($sql);

        $this->smarty->assign("timesList",$timesList);
        $this->smarty->assign("dataVar",array_merge($taskOne?$taskOne:array(),$stutaskOne?$stutaskOne:array(),$dataVar?$dataVar:array()));
        $this->smarty->assign("act","EditItem");

        $this->Viewhtm = $this->router->getController()."/"."EditItem/tasktype{$taskOne['taskitems_tasktype']}.htm";

    }

    //提交处理机制
    function EditItemAction()
    {
        $request = Input('post.','','trim,addslashes');

        $sql = "select a.course_branch,b.course_mold from eas_course_times as a,eas_course as b where a.course_branch=b.course_branch and a.times_id='{$request['times_id']}'";

        $timesOne=$this->Show_css->selectOne($sql);

        if(!$timesOne){
            ajax_return(array('error' => 1,'errortip' => "课次不存在!","bakfuntion"=>"warningFromTip"));
        }

        if(!isset($request['times_id']) || $request['times_id'] == ''){
            ajax_return(array('error' => 1,'errortip' => "课次必须传!","bakfuntion"=>"warningFromTip"));
        }

        if(!isset($request['taskitems_tasktype']) || $request['taskitems_tasktype'] == ''){
            ajax_return(array('error' => 1,'errortip' => "任务类型必须设置!","bakfuntion"=>"warningFromTip"));
        }

//        if(!isset($request['taskitems_taskclass']) || $request['taskitems_taskclass'] == '' ){
//            ajax_return(array('error' => 1,'errortip' => "任务模式必须设置!","bakfuntion"=>"warningFromTip"));
//        }

        if($timesOne['course_mold']!=1){
            if(!isset($request['taskitems_modetype']) || $request['taskitems_modetype'] == '' ){
                ajax_return(array('error' => 1,'errortip' => "任务分类必须设置!","bakfuntion"=>"warningFromTip"));
            }
        }


        $data=array();
        $data['course_branch']=$timesOne['course_branch'];
        $data['textbook_id']=$request['textbook_id']?$request['textbook_id']:0;
        $data['times_id']=$request['g_times_id'];
        $data['taskitems_sort']=$request['taskitems_sort'];
        if($timesOne['course_mold']==1 || $timesOne['course_mold']==2){
            $data['taskitems_week'] = $request['taskitems_week'];
        }
        $data['taskitems_tasktype']=$request['taskitems_tasktype'];
        $data['taskitems_taskclass']=$request['taskitems_taskclass']?$request['taskitems_taskclass']:0;
        $data['taskitems_modetype']=$request['taskitems_modetype']?$request['taskitems_modetype']:0;
        $data['taskitems_title']=$request['taskitems_title'];
        $data['fucmodule_branch']=$request['fucmodule_branch'];
        $data['taskitems_content']=$request['taskitems_content'];
        $data['taskitems_url']=$request['taskitems_url'];
        $data['taskitems_isbook']=$request['taskitems_isbook'];
        $data['taskitems_diamondprice']=$request['taskitems_diamondprice'];

        $data['taskitems_playtype']=$request['taskitems_playtype'];//玩法ID

        $data['item_updatatime']=time();

        switch ($request['taskitems_tasktype']){
            case 0:
                if(!isset($request['media_id']) || $request['media_id'] == ''){
                    ajax_return(array('error' => 1,'errortip' => "KCTV视频必须设置!","bakfuntion"=>"warningFromTip"));
                }
                break;
            case 1:
                if(!isset($request['steps_id']) || $request['steps_id'] == ''){
                    ajax_return(array('error' => 1,'errortip' => "复习关卡必须设置!","bakfuntion"=>"warningFromTip"));
                }
                break;
            case 2:
                if(!isset($request['testpaper_id']) || $request['testpaper_id'] == ''){
                    ajax_return(array('error' => 1,'errortip' => "真题试卷必须设置!","bakfuntion"=>"warningFromTip"));
                }
                break;
            case 4:
                if(!isset($request['readbooks_id']) || $request['readbooks_id'] == ''){
                    ajax_return(array('error' => 1,'errortip' => "阅读图书必须设置!","bakfuntion"=>"warningFromTip"));
                }
                break;
            case 6:
                if(!isset($request['audiorecords_id']) || $request['audiorecords_id']==''){
                    ajax_return(array('error' => 1,'errortip' => "趣配音必须设置!","bakfuntion"=>"warningFromTip"));
                }
                break;
//            case 8:
//                if(!isset($request['picbooks_id']) || $request['picbooks_id']==''){
//                    ajax_return(array('error' => 1,'errortip' => "阅读绘本必须设置!","bakfuntion"=>"warningFromTip"));
//                }
//                break;
            case 9:
                if(isset($request['isall']) && $request['isall']!='' && $request['isall']==0 && (!isset($request['textbookcate_id']) || $request['textbookcate_id']==0 || $request['textbookcate_id']=='' )){
                    ajax_return(array('error' => 1,'errortip' => "堡贝乐主题必须设置!","bakfuntion"=>"warningFromTip"));
                }
                break;
        }


        if($this->Show_css->updateData("app_taskitems","taskitems_id='{$request['taskitems_id']}'",$data)){

            $table_name='';
            $data=array();
            $data['taskitems_id']=$request['taskitems_id'];

            switch ($request['taskitems_tasktype']){
                case 0:
                    $table_name='app_taskitems_media';
                    $data['media_id']=$request['media_id'];
                    $data['can_drag']=$request['can_drag'];
                    break;
                case 1:
                    $table_name='app_taskitems_steps';
                    $data['steps_id']=$request['steps_id'];
                    break;
                case 2:
                    $table_name='app_taskitems_testpaper';
                    $data['testpaper_id']=$request['testpaper_id'];
                    break;
                case 4:
                    $table_name='app_taskitems_readbooks';
                    $data['readbooks_id']=$request['readbooks_id'];
                    $data['plate_ids']=$request['plate_ids'];
                    break;
                case 6:
                    $table_name='app_taskitems_audiorecords';
                    $data['audiorecords_id']=$request['audiorecords_id'];
                    break;
                case 8:

                    if(array_intersect($request['plate1'],$request['plate2'])){
                        ajax_return(array('error' => 1,'errortip' => "任务板块与常驻包括不可存在交集!","bakfuntion"=>"warningFromTip"));

                    }

                    $table_name='app_taskitems_picbooks';
//                    $data['picbooks_id']=$request['picbooks_id'];
                    if (isset($request['plate1']) && count($request['plate1']) > 0) {
                        $idStr='';
                        foreach ($request['plate1'] as $v) {
                            if($idStr){
                                $idStr.=','.$v;
                            }else{
                                $idStr=$v;
                            }
                        }

                        $data['plate_ids']=$idStr;
                    }

                    if (isset($request['plate2']) && count($request['plate2']) > 0) {
                        $idStr='';
                        foreach ($request['plate2'] as $v) {
                            if($idStr){
                                $idStr.=','.$v;
                            }else{
                                $idStr=$v;
                            }
                        }

                        $data['perplate_ids']=$idStr;
                    }
                    break;
                case 9:
                    if(isset($request['isall']) && $request['isall']!='' && $request['isall']=='0' && isset($request['textbookcate_id']) && $request['textbookcate_id']!=='' ){
                        $table_name='app_taskitems_ekidbook';
                        $data['textbookcate_id']=$request['textbookcate_id'];
                        $data['steps_id']=$request['steps_id'];
                        $this->Show_css->insertData("app_taskitems_ekidbook",$data);
                    }
                    break;
                case 10:
                    $table_name='app_taskitems_blhclass';
                    $data['class_id']=$request['class_id'];
                    $data['can_drag']=$request['can_drag'];
                    break;
            }

            if($table_name!=''){
                if($this->Show_css->getFieldOne($table_name,"taskitems_id","taskitems_id='{$request['taskitems_id']}'")){
                    $this->Show_css->updateData($table_name,"taskitems_id='{$request['taskitems_id']}'",$data);
                }else{
                    $this->Show_css->insertData($table_name,$data);
                }
            }

            $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"修改任务明细，ID：".$request['taskitems_id']);
            ajax_return(array('error' => 0,'errortip' => "修改成功!","bakfuntion"=>"successFromTip","bakurl"=>"/{$this->u}/TaskItem?times_id={$request['times_id']}&site_id={$request['site_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "修改失败!","bakfuntion"=>"errormotify"));
        }

    }

    //提交处理机制
    function DelItemAction()
    {
        $request = Input('get.','','trim,addslashes');
        $list_id = Input('get.taskitems_id',0);
        $taskitemOne =  $this->Show_css->selectOne("select taskitems_id from  eas_classes_tasks_learnitems  where  taskitems_id='{$list_id}'");
        if($taskitemOne){
            ajax_return(array('error' => 1,'errortip' => "该任务已使用,无法删除!","bakfuntion"=>"okmotify") );
        }

        if($this->Show_css->delData('app_taskitems',"taskitems_id='{$list_id}'")){
            $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"删除数据任务清单，ID：".$list_id);
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"okmotify") );
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"errormotify"));
        }
    }

    function AtapplyPlateAction()
    {
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;


        switch ($request['plate_id']){

            case 1:
                $data = array();
                $data['taskitems_id'] = $request['taskitems_id'];
                $data['picbooks_id'] = $request['picbooks_id'];
                $data['plate_id'] = $request['plate_id'];
                $data['audio_id'] = $request['id'];

                $this->Show_css->delData("app_taskitems_picaudio","taskitems_id='{$request['taskitems_id']}' and picbooks_id='{$request['picbooks_id']}' and plate_id='{$request['plate_id']}'");


                if ($Show_css->insertData('app_taskitems_picaudio', $data)) {
                    ajax_return(array('error' => 0, 'errortip' => "提交成功!", "bakfuntion" => "successFromTip"));
                } else {
                    ajax_return(array('error' => 1, 'errortip' => "提交失败!", "bakfuntion" => "dangerFromTip"));
                }

                break;
            case 2:
                $data = array();
                $data['taskitems_id'] = $request['taskitems_id'];
                $data['picbooks_id'] = $request['picbooks_id'];
                $data['plate_id'] = $request['plate_id'];
                $data['pages_id'] = $request['id'];

                if ($Show_css->insertData('app_taskitems_picpages', $data)) {
                    ajax_return(array('error' => 0, 'errortip' => "提交成功!", "bakfuntion" => "successFromTip"));
                } else {
                    ajax_return(array('error' => 1, 'errortip' => "提交失败!", "bakfuntion" => "dangerFromTip"));
                }

                break;
            case 3:
                $data = array();
                $data['taskitems_id'] = $request['taskitems_id'];
                $data['picbooks_id'] = $request['picbooks_id'];
                $data['plate_id'] = $request['plate_id'];
                $data['words_id'] = $request['id'];

                if ($Show_css->insertData('app_taskitems_picwords', $data)) {
                    ajax_return(array('error' => 0, 'errortip' => "提交成功!", "bakfuntion" => "successFromTip"));
                } else {
                    ajax_return(array('error' => 1, 'errortip' => "提交失败!", "bakfuntion" => "dangerFromTip"));
                }

                break;
            case 4:
                $data = array();
                $data['taskitems_id'] = $request['taskitems_id'];
                $data['picbooks_id'] = $request['picbooks_id'];
                $data['plate_id'] = $request['plate_id'];
                $data['pages_id'] = $request['id'];

                if ($Show_css->insertData('app_taskitems_picpages', $data)) {
                    ajax_return(array('error' => 0, 'errortip' => "提交成功!", "bakfuntion" => "successFromTip"));
                } else {
                    ajax_return(array('error' => 1, 'errortip' => "提交失败!", "bakfuntion" => "dangerFromTip"));
                }

                break;
            case 5:
                $data = array();
                $data['taskitems_id'] = $request['taskitems_id'];
                $data['picbooks_id'] = $request['picbooks_id'];
                $data['plate_id'] = $request['plate_id'];
                $data['exercises_id'] = $request['id'];

                if ($Show_css->insertData('app_taskitems_picexercises', $data)) {
                    ajax_return(array('error' => 0, 'errortip' => "提交成功!", "bakfuntion" => "successFromTip"));
                } else {
                    ajax_return(array('error' => 1, 'errortip' => "提交失败!", "bakfuntion" => "dangerFromTip"));
                }

                break;
            case 6:
                $data = array();
                $data['taskitems_id'] = $request['taskitems_id'];
                $data['picbooks_id'] = $request['picbooks_id'];
                $data['plate_id'] = $request['plate_id'];
                $data['pages_id'] = $request['id'];

                if ($Show_css->insertData('app_taskitems_picpages', $data)) {
                    ajax_return(array('error' => 0, 'errortip' => "提交成功!", "bakfuntion" => "successFromTip"));
                } else {
                    ajax_return(array('error' => 1, 'errortip' => "提交失败!", "bakfuntion" => "dangerFromTip"));
                }

                break;
        }




    }

    //活动-学校 (删除)  参与设置
    function AtapplydelPlateAction()
    {
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;

        switch ($request['plate_id']){

            case 1:

                $this->Show_css->delData("app_taskitems_picaudio","taskitems_id='{$request['taskitems_id']}' and picbooks_id='{$request['picbooks_id']}' and plate_id='{$request['plate_id']}' and audio_id='{$request['id']}'");

                ajax_return(array('error' => 0, 'errortip' => "提交成功!", "bakfuntion" => "successFromTip"));

                break;

            case 2:

                $this->Show_css->delData("app_taskitems_picpages","taskitems_id='{$request['taskitems_id']}' and picbooks_id='{$request['picbooks_id']}' and plate_id='{$request['plate_id']}' and pages_id='{$request['id']}'");

                ajax_return(array('error' => 0, 'errortip' => "提交成功!", "bakfuntion" => "successFromTip"));

                break;

            case 3:

                $this->Show_css->delData("app_taskitems_picwords","taskitems_id='{$request['taskitems_id']}' and picbooks_id='{$request['picbooks_id']}' and plate_id='{$request['plate_id']}' and words_id='{$request['id']}'");

                ajax_return(array('error' => 0, 'errortip' => "提交成功!", "bakfuntion" => "successFromTip"));

                break;

            case 4:

                $this->Show_css->delData("app_taskitems_picpages","taskitems_id='{$request['taskitems_id']}' and picbooks_id='{$request['picbooks_id']}' and plate_id='{$request['plate_id']}' and pages_id='{$request['id']}'");

                ajax_return(array('error' => 0, 'errortip' => "提交成功!", "bakfuntion" => "successFromTip"));

                break;

            case 5:

                $this->Show_css->delData("app_taskitems_picexercises","taskitems_id='{$request['taskitems_id']}' and picbooks_id='{$request['picbooks_id']}' and plate_id='{$request['plate_id']}' and exercises_id='{$request['id']}'");

                ajax_return(array('error' => 0, 'errortip' => "提交成功!", "bakfuntion" => "successFromTip"));

                break;

            case 6:

                $this->Show_css->delData("app_taskitems_picpages","taskitems_id='{$request['taskitems_id']}' and picbooks_id='{$request['picbooks_id']}' and plate_id='{$request['plate_id']}' and pages_id='{$request['id']}'");

                ajax_return(array('error' => 0, 'errortip' => "提交成功!", "bakfuntion" => "successFromTip"));

                break;
        }

        $Show_css->delData('pro_sales_fucmodule', "fucmodule_id='{$request['fucmodule_id']}' and plan_id='{$request['plan_id']}'");

        ajax_return(array('error' => 0, 'errortip' => "提交成功!", "bakfuntion" => "successFromTip"));

    }


    function batchSetPlateAction()
    {
        $request = Input('post.','','trim,addslashes');


        switch ($request['plate_id']){
            case 1:
                $str='audio_id';
                $tableName='app_taskitems_picaudio';
                break;
            case 2:
                $str='pages_id';
                $tableName='app_taskitems_picpages';
                break;
            case 3:
                $str='words_id';
                $tableName='app_taskitems_picwords';
                break;
            case 4:
                $str='pages_id';
                $tableName='app_taskitems_picpages';
                break;
            case 5:
                $str='exercises_id';
                $tableName='app_taskitems_picexercises';
                break;
            case 6:
                $str='pages_id';
                $tableName='app_taskitems_picpages';
                break;
        }


        if (isset($request['tab_list']) && count($request['tab_list']) > 0) {
            if ($request['type'] == 1) {
                foreach ($request['tab_list'] as $v) {
                    if (!$this->Show_css->getFieldOne($tableName, 'taskitems_id', "taskitems_id={$request['taskitems_id']} and picbooks_id={$request['picbooks_id']} and plate_id={$request['plate_id']} and {$str}='{$v}'")) {
                        $data = array();
                        $data['taskitems_id'] = $request['taskitems_id'];
                        $data['picbooks_id'] = $request['picbooks_id'];
                        $data['plate_id'] = $request['plate_id'];
                        $data[$str] = $v;
                        $this->Show_css->insertData($tableName, $data);
                    }
                }
                ajax_return(array('error' => 0, 'errortip' => "批量设置成功!", "bakfuntion" => "refreshpage"));
            } elseif ($request['type'] == 2) {
                foreach ($request['tab_list'] as $v) {
                    $this->Show_css->delData($tableName, "taskitems_id={$request['taskitems_id']} and picbooks_id={$request['picbooks_id']} and plate_id={$request['plate_id']} and {$str}='{$v}'");
                }
                ajax_return(array('error' => 0, 'errortip' => "批量取消成功!", "bakfuntion" => "refreshpage"));
            }
        }
    }





    function batchWorkStutaskView(){
        $this->c="ajax";
        $request = Input('post.','','trim,addslashes');

//        ajax_return(array('error' => 1,'errortip' => "暂不开放!","bakfuntion"=>"errormotify"));


        if(!isset($request['course_id']) || $request['course_id'] == '0' || $request['course_id'] == ''){
            ajax_return(array('error' => 1,'errortip' => "请选择复制到的班别!","bakfuntion"=>"errormotify"));
        }
        if($request['times_id'] == '0'){
            ajax_return(array('error' => 1,'errortip' => "请选择复制课时!","bakfuntion"=>"errormotify"));
        }
        if(!$this->Show_css->getFieldOne("eas_course_times","times_id","times_id='{$request['times_id']}'")){
            ajax_return(array('error' => 1,'errortip' => "请选择班别对应的课次!","bakfuntion"=>"errormotify"));
        }

        $courseOne=$this->Show_css->getFieldOne("eas_course","course_branch,course_mold","course_id='{$request['course_id']}'");

        $Show_css = $this->Show_css;
        $edwhere =" 1";
        if(isset($request['tab_list']) && count($request['tab_list'])>0){
            $chooseid = "";
            foreach($request['tab_list'] as $chick_var){
                $chooseid .= "'{$chick_var}',";
            }

            $idrange = substr($chooseid,0,-1);

            $edwhere .= " and taskitems_id in ({$idrange})";
        }else{
            ajax_return(array('error' => 1,'errortip' => "未选择任何任务!","bakfuntion"=>"errormotify"));
        }
        $copylist = $Show_css->getList("app_taskitems",$edwhere);

        if($copylist){
            foreach($copylist as $copyvar){
                $data = array();
                $data['course_branch'] = $courseOne['course_branch'];
                $data['textbook_id'] = $copyvar['textbook_id'];
                $data['times_id'] = $request['times_id'];
                $data['taskitems_sort'] = $copyvar['taskitems_sort'];
                if($courseOne['course_mold']==1){
                    $data['taskitems_week'] = $copyvar['taskitems_week'];
                }
                $data['taskitems_tasktype'] = $copyvar['taskitems_tasktype'];
                $data['taskitems_taskclass'] = $copyvar['taskitems_taskclass'];
                $data['taskitems_modetype'] = $copyvar['taskitems_modetype'];
                $data['taskitems_playtype'] = $copyvar['taskitems_playtype'];
                $data['taskitems_title'] = addslashes($copyvar['taskitems_title']);
                $data['taskitems_content'] =  addslashes($copyvar['taskitems_content']);
                $data['taskitems_url'] = $copyvar['taskitems_url'];
                $data['taskitems_isbook'] = $copyvar['taskitems_isbook'];
                $data['taskitems_putaway'] = $copyvar['taskitems_putaway'];
                $data['taskitems_diamondprice'] = $copyvar['taskitems_diamondprice'];
                $data['taskitems_goldcoin'] = $copyvar['taskitems_goldcoin'];
                $data['item_createtime'] = time();
                $data['item_updatatime'] = time();
                $taskitems_id=$this->Show_css->insertData("app_taskitems",$data);

                $data=array();
                $data['taskitems_id']=$taskitems_id;

                switch ($copyvar['taskitems_tasktype']){
                    case 0:
                        $one=$this->Show_css->getFieldOne("app_taskitems_media","media_id","taskitems_id='{$copyvar['taskitems_id']}'");

                        $data['media_id']=$one['media_id'];
                        $this->Show_css->insertData("app_taskitems_media",$data);
                        break;
                    case 1:
                        $one=$this->Show_css->getFieldOne("app_taskitems_steps","steps_id","taskitems_id='{$copyvar['taskitems_id']}'");
                        $data['steps_id']=$one['steps_id'];
                        $this->Show_css->insertData("app_taskitems_steps",$data);
                        break;
                    case 2:
                        $one=$this->Show_css->getFieldOne("app_taskitems_testpaper","testpaper_id","taskitems_id='{$copyvar['taskitems_id']}'");
                        $data['testpaper_id']=$one['testpaper_id'];
                        $this->Show_css->insertData("app_taskitems_testpaper",$data);
                        break;
                    case 4:
                        $one=$this->Show_css->getFieldOne("app_taskitems_readbooks","readbooks_id","taskitems_id='{$copyvar['taskitems_id']}'");
                        $data['readbooks_id']=$one['readbooks_id'];
                        $this->Show_css->insertData("app_taskitems_readbooks",$data);
                        break;
                    case 6:
                        $one=$this->Show_css->getFieldOne("app_taskitems_audiorecords","audiorecords_id","taskitems_id='{$copyvar['taskitems_id']}'");
                        $data['audiorecords_id']=$one['audiorecords_id'];
                        $this->Show_css->insertData("app_taskitems_audiorecords",$data);
                        break;
                    case 8:
                        $one=$this->Show_css->getFieldOne("app_taskitems_picbooks","picbooks_id","taskitems_id='{$copyvar['taskitems_id']}'");
                        $data['picbooks_id']=$one['picbooks_id'];
                        $this->Show_css->insertData("app_taskitems_picbooks",$data);
                        break;
                    case 8:
                        $one=$this->Show_css->getFieldOne("app_taskitems_ekidbook","textbookcate_id","taskitems_id='{$copyvar['taskitems_id']}'");
                        $data['textbookcate_id']=$one['textbookcate_id'];
                        $this->Show_css->insertData("app_taskitems_ekidbook",$data);
                        break;

                }
            }
        }
        ajax_return(array('error' => 0,'errortip' => "复制任务明细成功!","bakfuntion"=>"okmotify","bakurl"=>"/TaskItems/TaskItem?times_id={$request['times_id']}&site_id={$request['site_id']}"));
    }



    function AmendHourView()
    {

        $request = Input('get.', '', 'trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        $classCatcodeOne = $Show_css->getOne("eas_course", "course_id='{$request['course_id']}'");
        $smarty->assign("dataVar", $classCatcodeOne);

        $smarty->assign("act", "Edit");

        $this->Viewhtm = $this->router->getController() . "/" . "AmendHour.htm";
    }

    //增加系统功能
    function EditView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;
        $categoryList = $this->Show_css->getFieldquery('eas_code_coursecat', 'coursecat_branch,coursecat_cnname', '');
        $this->smarty->assign("categoryList", $categoryList);

        $classCatcodeOne = $Show_css->getOne("eas_course", "course_id='{$request['course_id']}'");
        $smarty->assign("dataVar", $classCatcodeOne);

        $smarty->assign("act", "Edit");

        $this->Viewhtm = $this->router->getController() . "/" . "Manage.htm";
    }


    //提交处理机制
    function EditAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        if ($request['course_cnname'] == '' || $request['coursecat_branch'] == '' || $request['course_hours'] == '' || $request['coursecat_branch'] == '') {
            ajax_return(array('error' => 1, 'errortip' => "班别名称／班别编号／班别课时数量／所属班种必须设置!", "bakfuntion" => "warningFromTip"));
        }
        $data = array();
        $data['course_cnname'] = $request['course_cnname'];
        $data['coursecat_branch'] = $request['coursecat_branch'];
        $data['course_mold'] = $request['course_mold'];
        $data['course_branch'] = $request['course_branch'];
        $data['course_hours'] = $request['course_hours'];
        $data['course_series'] = $request['course_series'];
        $data['course_way'] = $request['course_way'];
        $data['fromchannel'] = $request['fromchannel'];
        $data['course_isforbidden'] = $request['course_isforbidden'];
        $data['course_kctvopen'] = $request['course_kctvopen'];
        $data['course_assist_task'] = $request['course_assist_task'];
        $data['course_assist_tasknum'] = intval($request['course_assist_tasknum']);
        $data['course_kctvopen'] = $request['course_kctvopen'];
        if ($this->Show_css->updateData("eas_course", "course_id = '{$request['course_id']}'", $data)) {
            $this->Recordweblog($request['site_id'], $this->Module['module_id'], $this->c, "修改班别");
            ajax_return(array('error' => 0, 'errortip' => "修改成功!", "bakfuntion" => "successFromTip", "bakurl" => "/{$this->u}?site_id={$request['site_id']}"));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "修改失败!", "bakfuntion" => "errormotify"));
        }
    }




    function AddHourView()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $ClasscodeOne = $this->Show_css->getOne("eas_course", "course_id='{$request['course_id']}'");
        $surveyList = $this->Show_css->selectClear("select  survey_id, survey_name from  app_survey  where  '1' ");
        $datatype = array();
        $datatype['hour_type'] = $request['hour_type'];
        $this->smarty->assign("surveyList", $surveyList);
        $this->smarty->assign("datatype", $datatype);
        $this->smarty->assign("ClasscodeOne", $ClasscodeOne);
        $this->smarty->assign("act", "AddHour");
    }

    //提交处理机制
    function AddHourAction()
    {
        $request = Input('post.', '', 'trim,addslashes');
        if ($request['hour_name'] == '' || $request['hour_sort'] == '') {
            ajax_return(array('error' => 1, 'errortip' => "课时名称／课时排序必须设置!", "bakfuntion" => "warningFromTip"));
        }
        if ($request['hour_type'] != 1 && $request['hour_type'] != 0) {
            ajax_return(array('error' => 1, 'errortip' => "请选择任务类型", "bakfuntion" => "warningFromTip"));
        }

        $classcodeOne = $this->Show_css->getFieldOne("eas_course", "course_branch", "course_id='{$request['course_id']}'");

        $data = array();
        $data['course_id'] = $request['course_id'];
        $data['hour_name'] = $request['hour_name'];
        $data['hour_sort'] = $request['hour_sort'];
        $data['hour_branch'] = $classcodeOne['course_branch'] . '_' . $request['hour_sort'];
        $data['hour_target'] = $request['hour_target'];
        $data['hour_preview'] = $request['hour_preview'];
        $data['hour_task'] = $request['hour_task'];
        $data['survey_id'] = $request['survey_id'];
        $data['hour_type'] = $request['hour_type'];
        $data['hour_createtime'] = time();
        if ($this->Show_css->insertData("eas_course_times", $data)) {
            $this->Recordweblog($request['site_id'], $this->Module['module_id'], $this->c, "课别ID{$request['course_id']}：新增课时");
            ajax_return(array('error' => 0, 'errortip' => "新增课时成功!", "bakfuntion" => "successFromTip", "bakurl" => "/{$this->u}/Hour?site_id={$request['site_id']}&course_id={$request['course_id']}&hour_type={$data['hour_type']}"));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "新增课时失败!", "bakfuntion" => "dangerFromTip"));
        }
    }


    //生成教学进度
    function GeneratedHourAction()
    {
        $request = Input('get.', '', 'trim,addslashes');
        $ClasscodeOne = $this->Show_css->getOne("eas_course", "course_id='{$request['course_id']}'");

        if ($request['hour_type'] <> 1 && $request['hour_type'] <> 0) {
            ajax_return(array('error' => 0, 'errortip' => "请选择任务类型!", "bakfuntion" => "okmotify"));
        }

        if ($ClasscodeOne['course_times'] == '0') {
            ajax_return(array('error' => 1, 'errortip' => "进度数为0不可以自动生成!", "bakfuntion" => "errormotify"));
        } else {
            $times_num = floor($ClasscodeOne['course_hours'] / $ClasscodeOne['course_times']);
            for ($hour = 1; $hour <= $ClasscodeOne['course_times']; $hour++) {
                $hourName = "Week {$hour}";
                if (!$this->Show_css->getFieldOne("eas_course_times", "times_id", "course_branch='{$ClasscodeOne['course_branch']}' and times_sort = '{$hour}'")) {
                    $data = array();
                    $data['course_branch'] = $ClasscodeOne['course_branch'];
                    $data['times_name'] = $hourName;
                    $data['times_hours'] = $times_num;
                    $data['times_branch'] = $ClasscodeOne['course_branch'] . '_' . $hour;
                    $data['times_sort'] = $hour;
                    $data['times_updatetime'] = time();
                    $data['times_createtime'] = time();
                    $this->Show_css->insertData("eas_course_times", $data);
                }else{
                    $data = array();
                    $data['times_name'] = $hourName;
                    $data['times_hours'] = $times_num;
                    $data['times_branch'] = $ClasscodeOne['course_branch'] . '_' . $hour;
                    $data['times_updatetime'] = time();
                    $this->Show_css->updateData("eas_course_times","course_branch='{$ClasscodeOne['course_branch']}' and times_sort = '{$hour}'", $data);
                }
            }
            ajax_return(array('error' => 0, 'errortip' => "自动生成成功!", "bakfuntion" => "okmotify"));
        }
    }

    function SetPicBooksView()
    {
        $request = Input('get.','','trim,addslashes');
        $datawhere = "1";
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (q.picbooks_branch like '%{$request['keyword']}%' or q.picbooks_title like '%{$request['keyword']}%') ";
            $datatype['keyword'] = $request['keyword'];
        }

        $sql="SELECT q.*,
            ( SELECT COUNT( b.picbooks_id )
            FROM app_picbooks_times AS b
            WHERE b.times_id = '{$request['id']}' AND b.picbooks_id = q.picbooks_id ) AS thestatus,
            ( SELECT b.pic_sort
            FROM app_picbooks_times AS b
            WHERE b.times_id = '{$request['id']}' AND b.picbooks_id = q.picbooks_id ) AS pic_sort
            FROM app_picbooks AS q
            WHERE {$datawhere}
            ORDER BY thestatus DESC,q.picbooks_id asc";

        $dataList = $Show_css->select($sql);
        $smarty->assign("dataList", $dataList);

        $times_id= $request['id'];
        $smarty->assign("times_id", $times_id);
        $smarty->assign("datatype",$datatype);
    }

    function AtapplyAction()
    {
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $data = array();
        $data['times_id']= $request['tem_times_id'];
        $data['picbooks_id']= $request['id'];
        $Show_css->insertData("app_picbooks_times",$data);

        ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"successFromTip"));

    }

    function AtapplydelAction()
    {
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;

        if($Show_css->delData('app_picbooks_times',"picbooks_id='{$request['id']}' and times_id='{$request['tem_times_id']}'")){
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"successFromTip"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "提交失败!","bakfuntion"=>"dangerFromTip"));
        }
    }

    function batchSetAction()
    {
        $request = Input('post.','','trim,addslashes');
        if (isset($request['tab_list']) && count($request['tab_list']) > 0) {
            if ($request['type'] == 1) {
                foreach ($request['tab_list'] as $v) {

                    if (!$this->Show_css->getFieldOne('app_picbooks_times', 'times_id', "times_id={$request['times_id']} and picbooks_id='{$v}'")) {
                        $data = array();
                        $data['times_id'] = $request['times_id'];
                        $data['picbooks_id'] = $v;
                        $this->Show_css->insertData('app_picbooks_times', $data);
                    }
                }
                ajax_return(array('error' => 0, 'errortip' => "批量设置成功!", "bakfuntion" => "refreshpage"));
            } elseif ($request['type'] == 2) {
                foreach ($request['tab_list'] as $v) {
                    $this->Show_css->delData('app_picbooks_times', "times_id={$request['times_id']} and picbooks_id='{$v}'");
                }
                ajax_return(array('error' => 0, 'errortip' => "批量取消成功!", "bakfuntion" => "refreshpage"));
            }
        }
    }

    function AtapplyItemAction()
    {
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $data = array();
        $data['taskitems_id']= $request['taskitems_id'];
        $data['item_id']= $request['id'];
        $Show_css->insertData("app_taskitems_blhitem",$data);

        ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"successFromTip"));

    }

    function AtapplydelItemAction()
    {
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;

        if($Show_css->delData('app_taskitems_blhitem',"item_id='{$request['id']}' and taskitems_id='{$request['taskitems_id']}'")){
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"successFromTip"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "提交失败!","bakfuntion"=>"dangerFromTip"));
        }
    }

    function batchSetItemAction()
    {
        $request = Input('post.','','trim,addslashes');
        if (isset($request['tab_list']) && count($request['tab_list']) > 0) {
            if ($request['type'] == 1) {
                foreach ($request['tab_list'] as $v) {

                    if (!$this->Show_css->getFieldOne('app_taskitems_blhitem', 'item_id', "taskitems_id={$request['taskitems_id']} and item_id='{$v}'")) {
                        $data = array();
                        $data['taskitems_id'] = $request['taskitems_id'];
                        $data['item_id'] = $v;
                        $this->Show_css->insertData('app_taskitems_blhitem', $data);
                    }
                }
                ajax_return(array('error' => 0, 'errortip' => "批量设置成功!", "bakfuntion" => "refreshpage"));
            } elseif ($request['type'] == 2) {
                foreach ($request['tab_list'] as $v) {
                    $this->Show_css->delData('app_taskitems_blhitem', "taskitems_id={$request['taskitems_id']} and item_id='{$v}'");
                }
                ajax_return(array('error' => 0, 'errortip' => "批量取消成功!", "bakfuntion" => "refreshpage"));
            }
        }
    }

    function EditBlhItemAction(){
        $request = Input('get.','','trim,addslashes');
        $data = array();
        $data[$request['field']] = $request['value'];
        if($this->Show_css->updateData("app_taskitems_blhitem","item_id = '{$request['id']}' and taskitems_id='{$request['field1']}'",$data)){
            ajax_return(array('error' => 0,'errortip' => "修改成功!"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "修改失败!"));
        }
    }


    function EditPicSortAction(){
        $request = Input('get.','','trim,addslashes');
        $data = array();
        $data[$request['field']] = $request['value'];
        if($this->Show_css->updateData("app_picbooks_times","picbooks_id = '{$request['id']}' and times_id='{$request['field1']}'",$data)){
            ajax_return(array('error' => 0,'errortip' => "修改成功!"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "修改失败!"));
        }
    }

    //课时 -- 勋章列表
    function TimesMedalView(){
        $request = Input('get.', '', 'trim,addslashes');
        $stutaskOne = $this->Show_css->selectOne("select c.course_cnname,c.course_branch,h.times_name,h.times_id from eas_course_times as h,eas_course as c
WHERE h.course_branch = c.course_branch and h.times_id = '{$request['times_id']}' ");
        $this->smarty->assign("stutaskOne", $stutaskOne);

        $datawhere = "1";
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (m.medal_title like '%{$request['keyword']}%' or t.times_name like '%{$request['keyword']}%' or t.times_branch like '%{$request['keyword']}%') ";
            $datatype['keyword'] = $request['keyword'];
        }

        $dataList = $this->Show_css->selectClear("SELECT m.*,t.times_name,t.times_branch  
                 FROM eas_course_medal AS m
                 LEFT JOIN eas_course_times AS t ON m.times_id = t.times_id 
                 WHERE {$datawhere} AND m.course_branch='{$stutaskOne['course_branch']}' ORDER BY t.times_sort ASC");
        $this->smarty->assign("dataList", $dataList);

        $this->smarty->assign("datatype", $datatype);
    }
    //课时 -- 添加勋章
    function AddTimesMedalView(){
        $request = Input('get.', '', 'trim,addslashes');
        $stutaskOne = $this->Show_css->selectOne("select c.course_cnname,c.course_branch,h.times_name,h.times_id from eas_course_times as h,eas_course as c
WHERE h.course_branch = c.course_branch and h.times_id = '{$request['times_id']}' ");
        $this->smarty->assign("stutaskOne", $stutaskOne);

        $timeslist = $this->Show_css->selectClear("select times_id,times_name,times_branch from eas_course_times where course_branch='{$stutaskOne['course_branch']}' order by times_sort ASC ");
        $this->smarty->assign("timeslist", $timeslist);

        $this->smarty->assign("act", "AddTimesMedal");
        $this->Viewhtm = $this->router->getController() . "/" . "ManageTimesMedal.htm";
    }
    //课时 -- 添加勋章
    function AddTimesMedalAction(){
        $request = Input('post.','','trim,addslashes');

        if($this->Show_css->selectOne("select medal_id from eas_course_medal where times_id = '{$request['times_id']}'")){
            ajax_return(array('error' => 1, 'errortip' => "该课次已存在勋章，不能重复添加!", "bakfuntion" => "warningFromTip"));
        }

        $CourseOne = $this->Show_css->selectOne("select c.course_cnname,c.course_branch,h.times_name,h.times_id from eas_course_times as h,eas_course as c
WHERE h.course_branch = c.course_branch and h.times_id = '{$request['times_id']}' ");
        $this->smarty->assign("CourseOne", $CourseOne);

        if ($request['medal_title'] == '') {
            ajax_return(array('error' => 1, 'errortip' => "勋章名称必须设置!", "bakfuntion" => "warningFromTip"));
        }
        if ($request['times_id'] == '') {
            ajax_return(array('error' => 1, 'errortip' => "课次必须设置!", "bakfuntion" => "warningFromTip"));
        }
        $data = array();
        $data['medal_title'] = $request['medal_title'];
        $data['medal_mainimg'] = $request['medal_mainimg'];
        $data['medal_shadowimg'] = $request['medal_shadowimg'];
//        $data['medal_mainframeimg'] = $request['medal_mainframeimg'];
//        $data['medal_shadowframeimg'] = $request['medal_shadowframeimg'];
        $data['times_id'] = $request['times_id'];
        $data['course_branch'] = $CourseOne['course_branch'];
        $data['medal_createtime'] = time();
        if ($this->Show_css->insertData("eas_course_medal", $data)) {
            $this->Recordweblog($request['site_id'], $this->Module['module_id'], $this->c, "新增勋章");
            ajax_return(array('error' => 0, 'errortip' => "新增成功!", "bakfuntion" => "successFromTip", "bakurl" => "/{$this->u}/TimesMedal?times_id={$CourseOne['times_id']}&site_id={$request['site_id']}"));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "新增失败!", "bakfuntion" => "dangerFromTip"));
        }
    }
    //课时 -- 编辑勋章
    function EditTimesMedalView(){
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        $stutaskOne = $this->Show_css->selectOne("select c.course_cnname,c.course_branch,h.times_name,h.times_id from eas_course_times as h,eas_course as c
WHERE h.course_branch = c.course_branch and h.times_id = '{$request['times_id']}' ");
        $this->smarty->assign("stutaskOne", $stutaskOne);

        $timeslist = $this->Show_css->selectClear("select times_id,times_name,times_branch from eas_course_times where course_branch='{$stutaskOne['course_branch']}' order by times_sort ASC ");
        $this->smarty->assign("timeslist", $timeslist);

        $medalOne = $Show_css->getOne("eas_course_medal", "medal_id='{$request['medal_id']}'");
        $smarty->assign("dataVar", $medalOne);

        $smarty->assign("act", "EditTimesMedal");
        $this->Viewhtm = $this->router->getController() . "/" . "ManageTimesMedal.htm";
    }
    //课时 -- 编辑勋章
    function EditTimesMedalAction(){
        $request = Input('post.','','trim,addslashes');

        if($this->Show_css->selectOne("select medal_id from eas_course_medal where times_id = '{$request['times_id']}' and medal_id <> '{$request['medal_id']}' ")){
            ajax_return(array('error' => 1, 'errortip' => "该课次已存在勋章，不能重复添加!", "bakfuntion" => "warningFromTip"));
        }

        $CourseOne = $this->Show_css->selectOne("select c.course_cnname,c.course_branch,h.times_name,h.times_id from eas_course_times as h,eas_course as c
WHERE h.course_branch = c.course_branch and h.times_id = '{$request['times_id']}' ");
        $this->smarty->assign("CourseOne", $CourseOne);

        if ($request['medal_title'] == '') {
            ajax_return(array('error' => 1, 'errortip' => "勋章名称必须设置!", "bakfuntion" => "warningFromTip"));
        }
        if ($request['times_id'] == '') {
            ajax_return(array('error' => 1, 'errortip' => "课次必须设置!", "bakfuntion" => "warningFromTip"));
        }

        $data = array();
        $data['medal_title'] = $request['medal_title'];
        $data['medal_mainimg'] = $request['medal_mainimg'];
        $data['medal_shadowimg'] = $request['medal_shadowimg'];
//        $data['medal_mainframeimg'] = $request['medal_mainframeimg'];
//        $data['medal_shadowframeimg'] = $request['medal_shadowframeimg'];
        $data['times_id'] = $request['times_id'];
        $data['course_branch'] = $CourseOne['course_branch'];
        $data['medal_updatetime'] = time();
        if ($this->Show_css->updateData("eas_course_medal", "medal_id = '{$request['medal_id']}'", $data)) {
            $this->Recordweblog($request['site_id'], $this->Module['module_id'], $this->c, "修改勋章");
            ajax_return(array('error' => 0, 'errortip' => "修改成功!", "bakfuntion" => "successFromTip", "bakurl" => "/{$this->u}/TimesMedal?times_id={$CourseOne['times_id']}&site_id={$request['site_id']}"));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "修改失败!", "bakfuntion" => "errormotify"));
        }
    }
    //课时 -- 提交处理机制
    function DelTimesMedalAction()
    {
        $request = Input('get.','','trim,addslashes');
        $list_id = Input('get.medal_id',0,'trim,addslashes');
        if($this->Show_css->getFieldOne("app_student_medallog","medallog_id","medal_id='{medal_id}'")){
            ajax_return(array('error' => 1, 'errortip' => "已有学生解锁该勋章,不可删除!", "bakfuntion" => "errormotify"));
        }

        if ($this->Show_css->delData('eas_course_medal', "medal_id='{$list_id}'")) {
            $this->Recordweblog($request['site_id'], $this->Module['module_id'], $this->c, "删除勋章");
            ajax_return(array('error' => 0, 'errortip' => "提交成功!", "bakfuntion" => "refreshpage" ));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "新增失败!", "bakfuntion" => "errormotify"));
        }
    }

    //班级-课时-勋章导入
    function ImportClassMedalView(){
        $request = Input('get.', '', 'trim,addslashes');
        $CourseOne = $this->Show_css->getOne("eas_course", "course_id='{$request['course_id']}'");
        $this->smarty->assign("CourseOne", $CourseOne);
    }
    //班级-课时-勋章导入
    function ImportClassMedalExcelView(){
        $request = Input('post.', '', 'trim,addslashes');
        if($_FILES['uploadFile']['name'] == '' || $_FILES['uploadFile']['error'] == '1'){
            $PlayInfoVar = array();
            $PlayInfoVar['times_id'] = "导入出错";
            $PlayInfoVar['medal_title'] = "导入出错";
            $PlayInfoVar['error'] = "1";
            $PlayInfoVar['errortip'] = "文件不存在";
            $PlayInfo[] = $PlayInfoVar;
            $this->smarty->assign("PlayInfo", $PlayInfo);
            exit;
        }

        if($request['course_id'] == '0' || $request['course_id'] == ''){
            $PlayInfoVar = array();
            $PlayInfoVar['times_id'] = "班级参数缺失";
            $PlayInfoVar['medal_title'] = "班级参数缺失";
            $PlayInfoVar['error'] = "1";
            $PlayInfoVar['errortip'] = "班级参数缺失";
            $PlayInfo[] = $PlayInfoVar;
        }else{
            $fileType = array('xls','csv','xlsx');
            $uploadfile = new \Webfile($_FILES['uploadFile'],$files_dir='../static/file', $size = 2097152*10,$fileType);
            $up_file = $uploadfile->upload();

            $CourseOne = $this->Show_css->getOne("eas_course", "course_id='{$request['course_id']}'");

            $PlayInfo = array();
            if($up_file && $uploadfile->updatastatus) {
                $ExeclName = array();
                $ExeclName['课时ID'] = "times_id";
                $ExeclName['新勋章名称'] = "medal_title";
                $ExeclName['勋章亮图URL'] = "medal_mainimg";
                $ExeclName['勋章灰图URL'] = "medal_shadowimg";

                $WorkerList = execl_to_array($up_file, $ExeclName);
                unset($WorkerList[0]);

                if ($WorkerList) {
                    foreach ($WorkerList as $WorkerrVar) {
                        if ($WorkerrVar['课时ID'] !== '' && $WorkerrVar['新勋章名称'] !== '' && $WorkerrVar['勋章亮图URL'] !== '' && $WorkerrVar['勋章灰图URL'] !== '' ) {
                            $workersList[] = $WorkerrVar;
                        } else {
                            $PlayInfoVar = array();
                            $PlayInfoVar['times_id'] = $WorkerrVar['times_id'];
                            $PlayInfoVar['medal_title'] = $WorkerrVar['medal_title'];
                            $PlayInfoVar['error'] = "1";
                            $PlayInfoVar['errortip'] = "导入信息不完整";
                            $PlayInfo[] = $PlayInfoVar;
                        }
                    }
                }
                if (count($workersList) > 1000) {
                    ajax_return(array('error' => 1, 'errortip' => "导入数量不能大于1000!", "bakfuntion" => "errormotify"));
                }
                if($workersList) {
                    foreach ($workersList as $workersVar) {
                        $PlayInfoVar = array();
                        $PlayInfoVar['times_id'] = $workersVar['times_id'];
                        $PlayInfoVar['medal_title'] = trim($workersVar['medal_title']);
                        if(!$this->Show_css->getFieldOne("eas_course_times","times_id","times_id = '{$workersVar['times_id']}' and course_branch = '{$CourseOne['course_branch']}' ")){
                            $PlayInfoVar = array();
                            $PlayInfoVar['times_id'] = $workersVar['times_id'];
                            $PlayInfoVar['medal_title'] = trim($workersVar['medal_title']);
                            $PlayInfoVar['error'] = "1";
                            $PlayInfoVar['errortip'] = "{$CourseOne['course_cnname']}课时信息不存在";
                            $PlayInfo[] = $PlayInfoVar;
                            continue;
                        }
                        if($this->Show_css->getFieldOne("eas_course_medal","medal_id","times_id = '{$workersVar['times_id']}' and course_branch = '{$CourseOne['course_branch']}' ")){
                            $PlayInfoVar = array();
                            $PlayInfoVar['times_id'] = $workersVar['times_id'];
                            $PlayInfoVar['medal_title'] = trim($workersVar['medal_title']);
                            $PlayInfoVar['error'] = "1";
                            $PlayInfoVar['errortip'] = "该课时勋章信息已存在";
                            $PlayInfo[] = $PlayInfoVar;
                            continue;
                        }

                        $data = array();
                        $data['course_branch'] = $CourseOne['course_branch'];
                        $data['times_id'] = $workersVar['times_id'];
                        $data['medal_title'] = $workersVar['medal_title'];
                        $data['medal_mainimg'] = $workersVar['medal_mainimg'];
                        $data['medal_shadowimg'] = $workersVar['medal_shadowimg'];
                        $data['medal_createtime'] = time();
                        if ($this->Show_css->insertData("eas_course_medal", $data)) {
                            $PlayInfoVar['error'] = "0";
                            $PlayInfoVar['errortip'] = "导入成功";
                        }else{
                            $PlayInfoVar['error'] = "1";
                            $PlayInfoVar['errortip'] = "导入失败";
                        }
                        $PlayInfo[] = $PlayInfoVar;
                    }
                }

            }else{
                $PlayInfoVar = array();
                $PlayInfoVar['times_id'] = "导入出错";
                $PlayInfoVar['medal_title'] = "导入出错";
                $PlayInfoVar['error'] = "1";
                $PlayInfoVar['errortip'] = "文件不存在,数据问题";
                $PlayInfo[] = $PlayInfoVar;
            }
        }
        $this->smarty->assign("PlayInfo", $PlayInfo);
    }



    //课程勋章列表
    function MedalView(){
        $request = Input('get.', '', 'trim,addslashes');
        $CourseOne = $this->Show_css->getOne("eas_course", "course_id='{$request['course_id']}'");
        $this->smarty->assign("CourseOne", $CourseOne);

        $datawhere = "1";
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (m.medal_title like '%{$request['keyword']}%' or t.times_name like '%{$request['keyword']}%' or t.times_branch like '%{$request['keyword']}%') ";
            $datatype['keyword'] = $request['keyword'];
        }

        $dataList = $this->Show_css->selectClear("SELECT m.*,t.times_name,t.times_branch  
                 FROM eas_course_medal AS m
                 LEFT JOIN eas_course_times AS t ON m.times_id = t.times_id 
                 WHERE {$datawhere} AND m.course_branch='{$CourseOne['course_branch']}' ORDER BY t.times_sort ASC");
        $this->smarty->assign("dataList", $dataList);

        $this->smarty->assign("datatype", $datatype);
    }

    //添加勋章
    function AddMedalView(){
        $request = Input('get.', '', 'trim,addslashes');
        $CourseOne = $this->Show_css->getOne("eas_course", "course_id='{$request['course_id']}'");
        $this->smarty->assign("CourseOne", $CourseOne);

        $timeslist = $this->Show_css->selectClear("select times_id,times_name,times_branch from eas_course_times where course_branch='{$CourseOne['course_branch']}' order by times_sort ASC ");
        $this->smarty->assign("timeslist", $timeslist);

        $this->smarty->assign("act", "AddMedal");
        $this->Viewhtm = $this->router->getController() . "/" . "ManageMedal.htm";
    }
    //添加勋章
    function AddMedalAction(){
        $request = Input('post.','','trim,addslashes');

        if($this->Show_css->selectOne("select medal_id from eas_course_medal where times_id = '{$request['times_id']}'")){
            ajax_return(array('error' => 1, 'errortip' => "该课次已存在勋章，不能重复添加!", "bakfuntion" => "warningFromTip"));
        }
        $CourseOne = $this->Show_css->getOne("eas_course", "course_id='{$request['course_id']}'");
        $this->smarty->assign("CourseOne", $CourseOne);

        if ($request['medal_title'] == '') {
            ajax_return(array('error' => 1, 'errortip' => "勋章名称必须设置!", "bakfuntion" => "warningFromTip"));
        }
        if ($request['times_id'] == '') {
            ajax_return(array('error' => 1, 'errortip' => "课次必须设置!", "bakfuntion" => "warningFromTip"));
        }
        $data = array();
        $data['medal_title'] = $request['medal_title'];
        $data['medal_mainimg'] = $request['medal_mainimg'];
        $data['medal_shadowimg'] = $request['medal_shadowimg'];
//        $data['medal_mainframeimg'] = $request['medal_mainframeimg'];
//        $data['medal_shadowframeimg'] = $request['medal_shadowframeimg'];
        $data['times_id'] = $request['times_id'];
        $data['course_branch'] = $CourseOne['course_branch'];
        $data['medal_createtime'] = time();
        if ($this->Show_css->insertData("eas_course_medal", $data)) {
            $this->Recordweblog($request['site_id'], $this->Module['module_id'], $this->c, "新增勋章");
            ajax_return(array('error' => 0, 'errortip' => "新增成功!", "bakfuntion" => "successFromTip", "bakurl" => "/{$this->u}/Medal?course_id={$CourseOne['course_id']}&site_id={$request['site_id']}"));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "新增失败!", "bakfuntion" => "dangerFromTip"));
        }
    }

    //编辑勋章
    function EditMedalView(){
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        $CourseOne = $this->Show_css->getOne("eas_course", "course_id='{$request['course_id']}'");
        $this->smarty->assign("CourseOne", $CourseOne);

        $timeslist = $this->Show_css->selectClear("select times_id,times_name,times_branch from eas_course_times where course_branch='{$CourseOne['course_branch']}' order by times_sort ASC ");
        $this->smarty->assign("timeslist", $timeslist);

        $medalOne = $Show_css->getOne("eas_course_medal", "medal_id='{$request['medal_id']}'");
        $smarty->assign("dataVar", $medalOne);

        $smarty->assign("act", "EditMedal");
        $this->Viewhtm = $this->router->getController() . "/" . "ManageMedal.htm";
    }

    function EditMedalAction(){
        $request = Input('post.','','trim,addslashes');

        if($this->Show_css->selectOne("select medal_id from eas_course_medal where times_id = '{$request['times_id']}' and medal_id <> '{$request['medal_id']}' ")){
            ajax_return(array('error' => 1, 'errortip' => "该课次已存在勋章，不能重复添加!", "bakfuntion" => "warningFromTip"));
        }
        $CourseOne = $this->Show_css->getOne("eas_course", "course_id='{$request['course_id']}'");
        $this->smarty->assign("CourseOne", $CourseOne);

        if ($request['medal_title'] == '') {
            ajax_return(array('error' => 1, 'errortip' => "勋章名称必须设置!", "bakfuntion" => "warningFromTip"));
        }
        if ($request['times_id'] == '') {
            ajax_return(array('error' => 1, 'errortip' => "课次必须设置!", "bakfuntion" => "warningFromTip"));
        }

        $data = array();
        $data['medal_title'] = $request['medal_title'];
        $data['medal_mainimg'] = $request['medal_mainimg'];
        $data['medal_shadowimg'] = $request['medal_shadowimg'];
//        $data['medal_mainframeimg'] = $request['medal_mainframeimg'];
//        $data['medal_shadowframeimg'] = $request['medal_shadowframeimg'];
        $data['times_id'] = $request['times_id'];
        $data['course_branch'] = $CourseOne['course_branch'];
        $data['medal_updatetime'] = time();
        if ($this->Show_css->updateData("eas_course_medal", "medal_id = '{$request['medal_id']}'", $data)) {
            $this->Recordweblog($request['site_id'], $this->Module['module_id'], $this->c, "修改勋章");
            ajax_return(array('error' => 0, 'errortip' => "修改成功!", "bakfuntion" => "successFromTip", "bakurl" => "/{$this->u}/Medal?course_id={$CourseOne['course_id']}&site_id={$request['site_id']}"));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "修改失败!", "bakfuntion" => "errormotify"));
        }
    }
    //提交处理机制
    function DelMedalAction()
    {
        $request = Input('get.','','trim,addslashes');
        $list_id = Input('get.medal_id',0,'trim,addslashes');
        if($this->Show_css->getFieldOne("app_student_medallog","medallog_id","medal_id='{medal_id}'")){
            ajax_return(array('error' => 1, 'errortip' => "已有学生解锁该勋章,不可删除!", "bakfuntion" => "errormotify"));
        }

        if ($this->Show_css->delData('eas_course_medal', "medal_id='{$list_id}'")) {
            $this->Recordweblog($request['site_id'], $this->Module['module_id'], $this->c, "删除勋章");
            ajax_return(array('error' => 0, 'errortip' => "提交成功!", "bakfuntion" => "refreshpage" ));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "新增失败!", "bakfuntion" => "errormotify"));
        }
    }



    //魔术方法
    public function __call($name, $arguments)
    {
        $this->smarty->assign("errorTip", "Calling object method '$name' " . implode(', ', $arguments) . "\n");
        $this->Viewhtm = "under.htm";
    }

    //魔术函数
    function __destruct()
    {
        $site_id = Input('get.site_id', 0,'trim,addslashes');

        if ($this->c == 'Wait') {
            $this->smarty->assign("u", $this->router->getController());
            $this->smarty->assign("t", $this->router->getUrl());
            $this->smarty->assign("Viewhtm", $this->Viewhtm);
            $websites = $this->Show_css->getOne("cms_websites", "site_id='{$site_id}'");
            $this->smarty->assign("websites", $websites);
            if ($site_id) {
                if ($this->UserLogin['user_type'] == '1' and $this->UserLogin['user_limitsinc'] == '1') {
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id WHERE a.site_id = '{$site_id}' ORDER BY m.module_weight ASC ");
                } else {
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id LEFT JOIN cms_websites_func as c ON c.site_id = a.site_id and c.module_id = a.module_id WHERE a.site_id = '{$site_id}' and c.user_id = '{$this->UserLogin['user_id']}' ORDER BY m.module_weight ASC ");
                }
                // print_r($moduleList);die;
                $this->smarty->assign("moduleList", $moduleList);
                $this->display("websiteindex.htm");
            } else {
                $this->display("index.htm");
            }
        }
        exit;
    }
}