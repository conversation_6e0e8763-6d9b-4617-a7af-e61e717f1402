<?php
/**
 * ============================================================================
 * 版权所有 : https://www.mohism.cn
 * 网站地址 : https://www.mohism.cn
 * <AUTHOR> Zhugong Qi
 * Date: 2016/12/15
 * Time: 15:45
 */

namespace Work\Controller\Manage;


class AppReadbooksController extends viewTpl{
    public $data;
    public $u;
    public $t;
    public $c;
    public $iuser;
    public $Viewhtm;

    //预加载处理类
    function __construct() {
        parent::__construct ();
        if(!$this->check_login()){
            $this->LoginView();
        }
        if($this->check_login()){
            $this->smarty->assign("iuser", $this->UserLogin);
            $this->iuser = $this->UserLogin;
        }
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->Viewhtm = $this->router->getController()."/".$this->router->getUrl().".htm";

        $moduleOne = $this->Show_css->getOne("cms_module","module_mark='{$this->u}'");
        $this->smarty->assign("moduleOne",$moduleOne);
    }
    //主页
    function HomeView()
    {
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $datawhere = "1";
        $pageurl = "/{$this->u}/{$this->t}?";
        if ($request['site_id']) {
            $pageurl .= "&site_id={$request['site_id']}";
        }

        $keyword=$request['keyword'];

        $datatype = array();
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and r.readbooks_title like '%{$keyword}%' or r.readbooks_url like '%{$keyword}%'";
            $pageurl .= "&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }

        if(isset($request['readbooks_language']) && $request['readbooks_language'] !==''){
            $datawhere .= " and r.readbooks_language = '{$request['readbooks_language']}'";
            $pageurl .="&readbooks_language={$request['readbooks_language']}";
            $datatype['readbooks_language'] = $request['readbooks_language'];
        }

        if(isset($request['readbooks_agetype']) && $request['readbooks_agetype'] !==''){
            $datawhere .= " and r.readbooks_agetype = '{$request['readbooks_agetype']}'";
            $pageurl .="&readbooks_agetype={$request['readbooks_agetype']}";
            $datatype['readbooks_agetype'] = $request['readbooks_agetype'];
        }

        if(isset($request['readbooks_class']) && $request['readbooks_class'] !==''){
            $datawhere .= " and r.readbooks_class = '{$request['readbooks_class']}'";
            $pageurl .="&readbooks_class={$request['readbooks_class']}";
            $datatype['readbooks_class'] = $request['readbooks_class'];
        }

        if(isset($request['is_overdue']) && $request['is_overdue'] !==''){
            $now=date("Y-m-d",time());
            if($request['is_overdue']==1){
                $datawhere .= " and r.readbooks_stoptime < '{$now}'";
            }elseif($request['is_overdue']==2){
                $datawhere .= " and r.readbooks_stoptime >= '{$now}'";
            }
            $pageurl .="&is_overdue={$request['is_overdue']}";
            $datatype['is_overdue'] = $request['is_overdue'];
        }

        $sql = "SELECT r.*,v.list_name as readbooks_agetypename,t.list_name as readbooks_classname,a.extend_textReference
                ,ifnull((select 1 from app_readbooks_audio as x where x.readbooks_id=r.readbooks_id limit 0,1),0) as is_audio
                ,ifnull((select 1 from app_readbooks_question as x where x.readbooks_id=r.readbooks_id limit 0,1),0) as is_question
                ,ifnull((select 1 from app_readbooks_challenge as x where x.readbooks_id=r.readbooks_id limit 0,1),0) as is_challenge
                ,ifnull((select 1 from app_readbooks_summarizations as x where x.readbooks_id=r.readbooks_id limit 0,1),0) as is_summarizations
                FROM app_readbooks as r 
                left join cms_variablelist as v ON r.readbooks_agetype = v.list_parameter and v.variable_id = '3' 
                left join cms_variablelist as t ON r.readbooks_class = t.list_parameter and t.variable_id = '1'  
                left join app_readbooks_extend as a on a.readbooks_id=r.readbooks_id
                where {$datawhere} order by r.readbooks_id DESC";

        $db_nums = $Show_css->select("SELECT COUNT(r.readbooks_id) FROM app_readbooks as r where {$datawhere}");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums[0][0];

        $datalist = $Show_css->dbwherePage($sql,$allnum,'10',$pageurl.'&p=',$p,'10','2');

        $smarty->assign("pagelist",$datalist['pages']);//筛选信息

        $smarty->assign("dataList",$datalist['cont']);
        $smarty->assign("datatype",$datatype);
    }
    //增加系统功能
    function AddView()
    {
        $this->smarty->assign("act","Add");
        $request = Input('get.','','trim,addslashes');

        $textbookOne['plate_idArray']=['1','2','3','4'];

        $plateList=[
            '0'=>['plate_id'=>'1','plate_name'=>'核心词汇'],
            '1'=>['plate_id'=>'2','plate_name'=>'伴读动画'],
            '2'=>['plate_id'=>'3','plate_name'=>'听故事'],
            '3'=>['plate_id'=>'4','plate_name'=>'亲子互动'],
        ];

        $this->smarty->assign("dataVar",$textbookOne );
        $this->smarty->assign("plateList",$plateList );

        $this->Viewhtm = $this->router->getController()."/"."Manage.htm";
    }
    //提交处理机制
    function AddAction()
    {
        $request = Input('post.','','trim,addslashes');
        if(!isset($request['readbooks_class']) || $request['readbooks_title'] == '' || $request['readbooks_url'] == '' || $request['readbooks_agetype'] == ''){
            ajax_return(array('error' => 1,'errortip' => "分类/名称/路径/年龄段必须设置!","bakfuntion"=>"warningFromTip"));
        }
        if($request['readbooks_language'] == ''){
            ajax_return(array('error' => 1,'errortip' => "中英文必须设置!","bakfuntion"=>"warningFromTip"));
        }
        if($request['readbooks_stoptime'] == '' || $request['readbooks_goldcorrect'] == ''){
            ajax_return(array('error' => 1,'errortip' => "到期日期/金币数量必须设置!","bakfuntion"=>"warningFromTip"));
        }



        $data = array();
        $data['readbooks_language'] = $request['readbooks_language'];
        $data['readbooks_class'] = $request['readbooks_class'];
        $data['readbooks_title'] = $request['readbooks_title'];
        $bookOne=$this->Show_css->getFieldOne("app_readbooks","readbooks_id","readbooks_title='{$data['readbooks_title']}'");
        if($bookOne){
            ajax_return(array('error' => 1,'errortip' => "书名已存在!","bakfuntion"=>"warningFromTip"));
        }
        $data['readbooks_remark'] = $request['readbooks_remark'];
        $data['readbooks_coverimg'] = $request['readbooks_coverimg'];
        $data['readbooks_url'] = $request['readbooks_url'];
        $data['readbooks_audioUrl'] = $request['readbooks_audioUrl'];
        $data['readbooks_starttime'] = $request['readbooks_starttime'];
        $data['readbooks_stoptime'] = $request['readbooks_stoptime'];
        $data['readbooks_agetype'] = $request['readbooks_agetype'];
        $data['readbooks_goldcorrect'] = $request['readbooks_goldcorrect'];
        $data['readbooks_sort'] = $request['readbooks_sort'];
        $data['readbooks_isRecommend'] = $request['readbooks_isRecommend'];
        if(isset($request['readbooks_stoptime']) && $request['readbooks_goldcorrect'] != ''){
            $data['readbooks_addgoldtime'] = $request['readbooks_addgoldtime'];
        }

        if (isset($request['plate']) && count($request['plate']) > 0) {
            $idStr='';
            foreach ($request['plate'] as $v) {
                if($idStr){
                    $idStr.=','.$v;
                }else{
                    $idStr=$v;
                }
            }

            $data['readbooks_plateids']=$idStr;
        }


        $data['readbooks_createtime'] = time();
        if($readbooks_id=$this->Show_css->insertData("app_readbooks",$data)){

            $this->recordPlatformInfo($request['readbooks_url'],$readbooks_id);

            $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"新增图书数据");
            ajax_return(array('error' => 0,'errortip' => "新增成功!","bakfuntion"=>"successFromTip","bakurl"=>"/{$this->u}?site_id={$request['site_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"dangerFromTip"));
        }
    }
    //增加系统功能
    function EditView()
    {

        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        $sql = "select a.*
                ,b.readbooks_bookAlbum 
                ,b.extend_categoryTitle 
                ,b.extend_gradeTitle 
                ,b.extend_authorTitle 
                ,b.extend_illustratorTitle 
                ,b.extend_publisherTitle 
                ,b.extend_publishYear 
                ,b.extend_pageAmount 
                ,b.extend_textReference 
                from app_readbooks as a 
                left join app_readbooks_extend as b on b.readbooks_id=a.readbooks_id 
                where a.readbooks_id='{$request['id']}'";

        $textbookOne = $Show_css->selectOne($sql);
        $this->recordPlatformInfo($textbookOne['readbooks_url'],$textbookOne['readbooks_id']);
//        if($textbookOne['readbooks_json']=='NULL' || !$textbookOne['readbooks_json']){
//
//            $this->recordPlatformInfo($textbookOne['readbooks_url'],$textbookOne['readbooks_id']);
//        }

        if($textbookOne['readbooks_plateids']){
            $textbookOne['plate_idArray']=explode(",",$textbookOne['readbooks_plateids']);
        }else{
            $textbookOne['plate_idArray']=array();
        }

        $plateList=[
            '0'=>['plate_id'=>'1','plate_name'=>'核心词汇'],
            '1'=>['plate_id'=>'2','plate_name'=>'伴读动画'],
            '2'=>['plate_id'=>'3','plate_name'=>'听故事'],
            '3'=>['plate_id'=>'4','plate_name'=>'亲子互动'],
        ];


        $smarty->assign("plateList",$plateList);

        $smarty->assign("dataVar", $textbookOne);

        $smarty->assign("act", "Edit");

        $this->Viewhtm = $this->router->getController()."/"."Manage.htm";
    }
    //提交处理机制
    function EditAction()
    {
        $request = Input('post.','','trim,addslashes');
        if(!isset($request['readbooks_class']) || $request['readbooks_title'] == '' || $request['readbooks_url'] == '' || $request['readbooks_agetype'] == ''){
            ajax_return(array('error' => 1,'errortip' => "分类/名称/路径/年龄段必须设置!","bakfuntion"=>"warningFromTip"));
        }

        $data = array();
        $data['readbooks_language'] = $request['readbooks_language'];
        $data['readbooks_class'] = $request['readbooks_class'];
        $data['readbooks_title'] = $request['readbooks_title'];
        $bookOne=$this->Show_css->getFieldOne("app_readbooks","readbooks_id,readbooks_title","readbooks_title='{$data['readbooks_title']}'");
        if($bookOne){
            if($bookOne['readbooks_id'] ==$request['readbooks_id']){

            }else{
                ajax_return(array('error' => 1,'errortip' => "书名已存在!","bakfuntion"=>"warningFromTip"));
            }
        }
        $data['readbooks_remark'] = $request['readbooks_remark'];
        $data['readbooks_coverimg'] = $request['readbooks_coverimg'];
        $data['readbooks_url'] = $request['readbooks_url'];
        $data['readbooks_audioUrl'] = $request['readbooks_audioUrl'];
        $data['readbooks_starttime'] = $request['readbooks_starttime'];
        $data['readbooks_stoptime'] = $request['readbooks_stoptime'];
        $data['readbooks_agetype'] = $request['readbooks_agetype'];
        $data['readbooks_goldcorrect'] = $request['readbooks_goldcorrect'];
        $data['readbooks_sort'] = $request['readbooks_sort'];
        $data['readbooks_isRecommend'] = $request['readbooks_isRecommend'];
        if(isset($request['readbooks_stoptime']) && $request['readbooks_goldcorrect'] != ''){
            $data['readbooks_addgoldtime'] = $request['readbooks_addgoldtime'];
        }

        if (isset($request['plate']) && count($request['plate']) > 0) {
            $idStr='';
            foreach ($request['plate'] as $v) {
                if($idStr){
                    $idStr.=','.$v;
                }else{
                    $idStr=$v;
                }
            }

            $data['readbooks_plateids']=$idStr;
        }

        if($this->Show_css->updateData("app_readbooks","readbooks_id = '{$request['readbooks_id']}'",$data)){
            $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"修改图书数据，ID：".$request['readbooks_id']);
            ajax_return(array('error' => 0,'errortip' => "修改成功!","bakfuntion"=>"successFromTip","bakurl"=>"/{$this->u}?site_id={$request['site_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "修改失败!","bakfuntion"=>"errormotify"));
        }
    }


    function recordPlatformInfo($readbooks_url,$readbooks_id){

        $pos = strpos($readbooks_url, '=');
        $book_id = substr($readbooks_url, $pos+1);

        if($pos && $book_id>0){
            $paramarray=array();
            $paramarray['grant_type']='client_credentials';
            $paramarray['client_id']='kidcastle';
            $paramarray['client_secret']='g4Ef~fBq8sFpxTjeV7fbjZ6A';
            $paramarray['scope']='user_info';

            $getInfo = request_by_curl("https://thirdparty-api.bookteller.cn/oauth2/client_token", dataEncode($paramarray),"GET");

            $getInfoArray=json_decode($getInfo,1);

            $url="https://thirdparty-api.bookteller.cn/books/".$book_id;

            $paramarray=array();
            $paramarray['clientToken']=$getInfoArray['data']['client_token'];
            $bookInfo = request_by_curl($url, '',"GET",$paramarray);

            $data=array();
            $data['readbooks_json']=addslashes($bookInfo);
            $this->Show_css->updateData("app_readbooks","readbooks_id='{$readbooks_id}'",$data);

            $this->updateOneReadbooks(json_decode($bookInfo,1)['data']['book'],$getInfoArray['data']['client_token'],$bookInfo);
        }
    }


    //提交处理机制
    function DelAction()
    {
        $request = Input('get.','','trim,addslashes');
        $list_id = Input('get.id',0,'trim,addslashes');
        if($this->Show_css->delData('app_readbooks',"readbooks_id='{$list_id}'")){
            $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"删除图书数据，ID：".$list_id);
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"okmotify","bakurl"=>"/{$this->u}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"errormotify"));
        }
    }

    function AudioVoListView()
    {
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        if(!isset($request['readbooks_id']) || $request['readbooks_id']==''){
            ajax_return(array('error' => 1,'errortip' => "请选择图书!","bakfuntion"=>"warningFromTip"));
        }else{

            $datawhere = " a.readbooks_id='{$request['readbooks_id']}'";

            $smarty->assign("readbooks_id",$request['readbooks_id']);
        }


        $pageurl = "/{$this->u}/{$this->t}?";
        if ($request['site_id']) {
            $pageurl .= "&site_id={$request['site_id']}";
        }

        $sql = "SELECT a.*
                FROM app_readbooks_audio as a 
                where {$datawhere} 
                order by a.bookPageNumber asc";

        $db_nums = $Show_css->select("SELECT COUNT(a.audio_id) FROM app_readbooks_audio as a where {$datawhere}");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums[0][0];

        $datalist = $Show_css->dbwherePage($sql,$allnum,'30',$pageurl.'&p=',$p,'10','2');

        $smarty->assign("pagelist",$datalist['pages']);//筛选信息

        $smarty->assign("dataList",$datalist['cont']);
    }

    function AddAudioView(){
        $request = Input('get.','','trim,addslashes');
        $smarty = $this->smarty;

        $readbooksOne=$this->Show_css->getOne("app_readbooks","readbooks_id='{$request['readbooks_id']}'");

        $smarty->assign("dataVar", $readbooksOne);

        $smarty->assign("act", "AddAudio");

        $this->Viewhtm = $this->router->getController()."/"."AudioManage.htm";
    }

    function AddAudioAction()
    {
        $request = Input('post.','','trim,addslashes');

        if(!isset($request['readbooks_id']) || $request['readbooks_id']==''){
            ajax_return(array('error' => 1,'errortip' => "请选择图书!","bakfuntion"=>"warningFromTip"));
        }
        $data = array();
        $data['readbooks_id'] = $request['readbooks_id'];
        $data['bookPageNumber'] = $request['bookPageNumber'];
        $data['bookImageUrl'] = $request['bookImageUrl'];
        $data['bookMp3Url'] = $request['bookMp3Url'];
        $data['bookTextPlain'] = addslashes($request['bookTextPlain']);

        if($audio_id=$this->Show_css->insertData("app_readbooks_audio",$data)){
            $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"新增图书翻页数据，ID：".$audio_id);
            ajax_return(array('error' => 0,'errortip' => "新增成功!","bakfuntion"=>"successFromTip","bakurl"=>"/{$this->u}/AudioVoList?readbooks_id={$request['readbooks_id']}&site_id={$request['site_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"errormotify"));
        }
    }


    function AudioEditView(){
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        $audioOne = $Show_css->getOne("app_readbooks_audio", "audio_id='{$request['id']}'");


        $smarty->assign("dataVar", $audioOne);

        $smarty->assign("act", "AudioEdit");

        $this->Viewhtm = $this->router->getController()."/"."AudioManage.htm";
    }

    function AudioEditAction()
    {
        $request = Input('post.','','trim,addslashes');

        if(!isset($request['audio_id']) || $request['audio_id']==''){

            ajax_return(array('error' => 1,'errortip' => "请选择编辑资源!","bakfuntion"=>"warningFromTip"));
        }

        $data = array();
        $data['bookPageNumber'] = $request['bookPageNumber'];
        $data['bookImageUrl'] = $request['bookImageUrl'];
        $data['bookMp3Url'] = $request['bookMp3Url'];
        $data['bookTextPlain'] = addslashes($request['bookTextPlain']);

        if($this->Show_css->updateData("app_readbooks_audio","audio_id = '{$request['audio_id']}'",$data)){
            $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"修改图书翻页数据，ID：".$request['audio_id']);
            ajax_return(array('error' => 0,'errortip' => "修改成功!","bakfuntion"=>"successFromTip","bakurl"=>"/{$this->u}/AudioVoList?readbooks_id={$request['readbooks_id']}&site_id={$request['site_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "修改失败!","bakfuntion"=>"errormotify"));
        }
    }

    function DelAudioAction()
    {
        $request = Input('get.','','trim,addslashes');

        $list_id = Input('get.id',0,'trim,addslashes');

        if($this->Show_css->delData('app_readbooks_audio',"audio_id='{$list_id}'")){
            $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"删除图书翻页数据，ID：".$list_id);
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"okmotify","bakurl"=>"/{$this->u}/AudioVoList?readbooks_id={$request['readbooks_id']}&site_id={$request['site_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"errormotify"));
        }
    }


    function QuestionListView()
    {
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        if(!isset($request['readbooks_id']) || $request['readbooks_id']==''){
            ajax_return(array('error' => 1,'errortip' => "请选择图书!","bakfuntion"=>"warningFromTip"));
        }

        $datawhere = " a.readbooks_id='{$request['readbooks_id']}'";

        $smarty->assign("readbooks_id",$request['readbooks_id']);

        $pageurl = "/{$this->u}/{$this->t}?";
        if ($request['site_id']) {
            $pageurl .= "&site_id={$request['site_id']}";
        }

        $sql = "SELECT a.*
                FROM app_readbooks_question as a 
                where {$datawhere} ";

        $db_nums = $Show_css->select("SELECT COUNT(a.question_id) FROM app_readbooks_question as a where {$datawhere}");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums[0][0];

        $datalist = $Show_css->dbwherePage($sql,$allnum,'30',$pageurl.'&p=',$p,'10','2');

        $smarty->assign("pagelist",$datalist['pages']);//筛选信息

        $smarty->assign("dataList",$datalist['cont']);
    }

    function AddQuestionView(){
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        $readbooksOne=$this->Show_css->getOne("app_readbooks","readbooks_id='{$request['readbooks_id']}'");

        $smarty->assign("dataVar", $readbooksOne);

        $smarty->assign("act", "AddQuestion");

        $this->Viewhtm = $this->router->getController()."/"."QuestionManage.htm";
    }

    function AddQuestionAction(){
        $request = Input('post.','','trim,addslashes');

        if(!isset($request['readbooks_id']) || $request['readbooks_id']==''){
            ajax_return(array('error' => 1,'errortip' => "请选择图书!","bakfuntion"=>"warningFromTip"));
        }

        $data = array();
        $data['readbooks_id'] = $request['readbooks_id'];
        $data['question_name'] = addslashes($request['question_name']);

        if($question_id=$this->Show_css->insertData("app_readbooks_question",$data)){
            $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"新增图书问题数据，ID：".$question_id);
            ajax_return(array('error' => 0,'errortip' => "新增成功!","bakfuntion"=>"successFromTip","bakurl"=>"/{$this->u}/QuestionList?readbooks_id={$request['readbooks_id']}&site_id={$request['site_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"errormotify"));
        }
    }

    function DelQuestionAction(){
        $request = Input('get.','','trim,addslashes');
        $list_id = Input('get.id',0,'trim,addslashes');
        if($this->Show_css->delData('app_readbooks_question',"question_id='{$list_id}'")){
            $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"删除图书问题数据，ID：".$list_id);
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"okmotify","bakurl"=>"/{$this->u}/QuestionList?readbooks_id={$request['readbooks_id']}&site_id={$request['site_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"errormotify"));
        }
    }

    function QuestionEditView(){
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        $dateOne = $Show_css->getOne("app_readbooks_question", "question_id='{$request['id']}'");


        $smarty->assign("dataVar", $dateOne);

        $smarty->assign("act", "QuestionEdit");

        $this->Viewhtm = $this->router->getController()."/"."QuestionManage.htm";
    }

    function QuestionEditAction()
    {
        $request = Input('post.','','trim,addslashes');

        if(!isset($request['question_id']) || $request['question_id']==''){

            ajax_return(array('error' => 1,'errortip' => "请选择编辑资源!","bakfuntion"=>"warningFromTip"));
        }

        $data = array();
        $data['question_name'] = addslashes($request['question_name']);

        if($this->Show_css->updateData("app_readbooks_question","question_id = '{$request['question_id']}'",$data)){
            $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"修改图书问题数据，ID：".$request['question_id']);
            ajax_return(array('error' => 0,'errortip' => "修改成功!","bakfuntion"=>"successFromTip","bakurl"=>"/{$this->u}/QuestionList?readbooks_id={$request['readbooks_id']}&site_id={$request['site_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "修改失败!","bakfuntion"=>"errormotify"));
        }
    }

    function ChallengeListView()
    {
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        if(!isset($request['readbooks_id']) || $request['readbooks_id']==''){
            ajax_return(array('error' => 1,'errortip' => "请选择图书!","bakfuntion"=>"warningFromTip"));
        }

        $datawhere = " a.readbooks_id='{$request['readbooks_id']}'";

        $smarty->assign("readbooks_id",$request['readbooks_id']);

        $pageurl = "/{$this->u}/{$this->t}?";
        if ($request['site_id']) {
            $pageurl .= "&site_id={$request['site_id']}";
        }

        $sql = "SELECT a.*
                FROM app_readbooks_challenge as a 
                where {$datawhere} ";

        $db_nums = $Show_css->select("SELECT COUNT(a.challenge_id) FROM app_readbooks_challenge as a where {$datawhere}");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums[0][0];

        $datalist = $Show_css->dbwherePage($sql,$allnum,'30',$pageurl.'&p=',$p,'10','2');

        $smarty->assign("pagelist",$datalist['pages']);//筛选信息

        $smarty->assign("dataList",$datalist['cont']);
    }


    function AddChallengeView(){
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        $readbooksOne=$this->Show_css->getOne("app_readbooks","readbooks_id='{$request['readbooks_id']}'");

        $smarty->assign("dataVar", $readbooksOne);

        $smarty->assign("act", "AddChallenge");

        $this->Viewhtm = $this->router->getController()."/"."ChallengeManage.htm";
    }


    function AddChallengeAction(){
        $request = Input('post.','','trim,addslashes');

        if(!isset($request['readbooks_id']) || $request['readbooks_id']==''){
            ajax_return(array('error' => 1,'errortip' => "请选择图书!","bakfuntion"=>"warningFromTip"));
        }

        $data = array();
        $data['readbooks_id'] = $request['readbooks_id'];
        $data['challenge_name'] = addslashes($request['challenge_name']);

        if($challenge_id=$this->Show_css->insertData("app_readbooks_challenge",$data)){
            $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"新增图书挑战数据，ID：".$challenge_id);
            ajax_return(array('error' => 0,'errortip' => "新增成功!","bakfuntion"=>"successFromTip","bakurl"=>"/{$this->u}/ChallengeList?readbooks_id={$request['readbooks_id']}&site_id={$request['site_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"errormotify"));
        }
    }

    function DelChallengeAction(){
        $request = Input('get.','','trim,addslashes');
        $list_id = Input('get.id',0,'trim,addslashes');
        if($this->Show_css->delData('app_readbooks_challenge',"challenge_id='{$list_id}'")){
            $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"删除图书挑战数据，ID：".$list_id);
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"okmotify","bakurl"=>"/{$this->u}/ChallengeList?readbooks_id={$request['readbooks_id']}&site_id={$request['site_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"errormotify"));
        }
    }

    function ChallengeEditView(){
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        $dateOne = $Show_css->getOne("app_readbooks_challenge", "challenge_id='{$request['id']}'");


        $smarty->assign("dataVar", $dateOne);

        $smarty->assign("act", "ChallengeEdit");

        $this->Viewhtm = $this->router->getController()."/"."ChallengeManage.htm";
    }

    function ChallengeEditAction()
    {
        $request = Input('post.','','trim,addslashes');

        if(!isset($request['challenge_id']) || $request['challenge_id']==''){

            ajax_return(array('error' => 1,'errortip' => "请选择编辑资源!","bakfuntion"=>"warningFromTip"));
        }

        $data = array();
        $data['challenge_name'] = addslashes($request['challenge_name']);

        if($this->Show_css->updateData("app_readbooks_challenge","challenge_id = '{$request['challenge_id']}'",$data)){
            $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"修改图书挑战数据，ID：".$request['challenge_id']);
            ajax_return(array('error' => 0,'errortip' => "修改成功!","bakfuntion"=>"successFromTip","bakurl"=>"/{$this->u}/ChallengeList?readbooks_id={$request['readbooks_id']}&site_id={$request['site_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "修改失败!","bakfuntion"=>"errormotify"));
        }
    }



    function SummarizationsListView()
    {
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        if(!isset($request['readbooks_id']) || $request['readbooks_id']==''){
            ajax_return(array('error' => 1,'errortip' => "请选择图书!","bakfuntion"=>"warningFromTip"));
        }

        $datawhere = " a.readbooks_id='{$request['readbooks_id']}'";

        $smarty->assign("readbooks_id",$request['readbooks_id']);

        $pageurl = "/{$this->u}/{$this->t}?";
        if ($request['site_id']) {
            $pageurl .= "&site_id={$request['site_id']}";
        }

        $sql = "SELECT a.*
                FROM app_readbooks_summarizations as a 
                where {$datawhere} ";

        $db_nums = $Show_css->select("SELECT COUNT(a.summarizations_id) FROM app_readbooks_summarizations as a where {$datawhere}");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums[0][0];

        $datalist = $Show_css->dbwherePage($sql,$allnum,'30',$pageurl.'&p=',$p,'10','2');

        $smarty->assign("pagelist",$datalist['pages']);//筛选信息

        $smarty->assign("dataList",$datalist['cont']);
    }

    function AddSummarizationsView(){
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        $readbooksOne=$this->Show_css->getOne("app_readbooks","readbooks_id='{$request['readbooks_id']}'");

        $smarty->assign("dataVar", $readbooksOne);

        $smarty->assign("act", "AddSummarizations");

        $this->Viewhtm = $this->router->getController()."/"."SummarizationsManage.htm";
    }

    function AddSummarizationsAction(){
        $request = Input('post.','','trim,addslashes');

        if(!isset($request['readbooks_id']) || $request['readbooks_id']==''){
            ajax_return(array('error' => 1,'errortip' => "请选择图书!","bakfuntion"=>"warningFromTip"));
        }

        $data = array();
        $data['readbooks_id'] = $request['readbooks_id'];
        $data['englishTitle'] = addslashes($request['englishTitle']);
        $data['chineseTitle'] = addslashes($request['chineseTitle']);
        $data['phoneticTitle'] = addslashes($request['phoneticTitle']);
        $data['expressionType'] = $request['expressionType'];

        if($summarizations_id=$this->Show_css->insertData("app_readbooks_summarizations",$data)){
            $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"新增图书亲子互动数据，ID：".$summarizations_id);
            ajax_return(array('error' => 0,'errortip' => "新增成功!","bakfuntion"=>"successFromTip","bakurl"=>"/{$this->u}/SummarizationsList?readbooks_id={$request['readbooks_id']}&site_id={$request['site_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"errormotify"));
        }
    }

    function DelSummarizationsAction(){
        $request = Input('get.','','trim,addslashes');
        $list_id = Input('get.id',0,'trim,addslashes');
        if($this->Show_css->delData('app_readbooks_summarizations',"summarizations_id='{$list_id}'")){
            $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"删除图书亲子互动数据，ID：".$list_id);
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"okmotify","bakurl"=>"/{$this->u}/SummarizationsList?readbooks_id={$request['readbooks_id']}&site_id={$request['site_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"errormotify"));
        }
    }

    function SummarizationsEditView(){
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        $dateOne = $Show_css->getOne("app_readbooks_summarizations", "summarizations_id='{$request['id']}'");


        $smarty->assign("dataVar", $dateOne);

        $smarty->assign("act", "SummarizationsEdit");

        $this->Viewhtm = $this->router->getController()."/"."SummarizationsManage.htm";
    }

    function SummarizationsEditAction()
    {
        $request = Input('post.','','trim,addslashes');

        if(!isset($request['summarizations_id']) || $request['summarizations_id']==''){

            ajax_return(array('error' => 1,'errortip' => "请选择编辑资源!","bakfuntion"=>"warningFromTip"));
        }

        $data = array();
        $data['englishTitle'] = addslashes($request['englishTitle']);
        $data['chineseTitle'] = addslashes($request['chineseTitle']);
        $data['phoneticTitle'] = addslashes($request['phoneticTitle']);
        $data['expressionType'] = $request['expressionType'];

        if($this->Show_css->updateData("app_readbooks_summarizations","summarizations_id = '{$request['summarizations_id']}'",$data)){
            $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"修改图书亲子互动数据，ID：".$request['summarizations_id']);
            ajax_return(array('error' => 0,'errortip' => "修改成功!","bakfuntion"=>"successFromTip","bakurl"=>"/{$this->u}/SummarizationsList?readbooks_id={$request['readbooks_id']}&site_id={$request['site_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "修改失败!","bakfuntion"=>"errormotify"));
        }
    }




    function updateOneReadbooks($bookOne,$client_token,$bookInfo=array(),$is_Recommend=0){

        if(APPVER == 'TW'){
            $languageArray=array('zh_tw','zh_cn','en');

            $TraditionalModel = new \Model\TraditionalModel();
        }else{
            $languageArray=array('zh_cn','en');
        }

        if(in_array($bookOne['languageCode'],$languageArray) || SITE_URL=='chevady.cn'){
            $readbooksOne=$this->Show_css->getFieldOne("app_readbooks","readbooks_id,readbooks_json,readbooks_class,readbooks_agetype","readbooks_bookid='{$bookOne['bookId']}'");

            if($is_Recommend==1){

                if(!$readbooksOne){
                    $data=array();
                    $data['readbooks_language']=$bookOne['languageCode']=='en'?1:0;
                    $data['readbooks_iscomplex']=$bookOne['languageCode']=='zh_cn'?0:1;
                    $data['readbooks_isRecommend']=$is_Recommend;
                    $data['readbooks_title']=addslashes($bookOne['bookTitle']);
                    $data['readbooks_coverimg']=$bookOne['bookAlbum'];
                    $data['readbooks_url']="https://apish.bookteller.com.cn/kidcastle?id=".$bookOne['bookId'];
                    if($readbooksOne){
                        $this->Show_css->updateData("app_readbooks","readbooks_bookid='{$bookOne['bookId']}'",$data);

                        $readbooks_id=$readbooksOne['readbooks_id'];
                    }else{
                        $data['readbooks_bookid']=$bookOne['bookId'];
                        $data['readbooks_createtime']=time();
                        $readbooks_id=$this->Show_css->insertData("app_readbooks",$data);
                    }

                    $data=array();
                    $data['readbooks_id']=$readbooks_id;
                    $data['readbooks_bookAlbum']=$bookOne['bookAlbum'];
                    $data['extend_categoryTitle']=addslashes($bookOne['categoryTitle']);
                    $data['extend_gradeTitle']=addslashes($bookOne['gradeTitle']);
                    $data['extend_authorTitle']=addslashes($bookOne['authorTitle']);
                    $data['extend_illustratorTitle']=addslashes($bookOne['illustratorTitle']);
                    $data['extend_publisherTitle']=addslashes($bookOne['publisherTitle']);
                    $data['extend_publishYear']=addslashes($bookOne['publishYear']);
                    $data['extend_pageAmount']=$bookOne['pageAmount'];
                    if(!$this->Show_css->getFieldOne("app_readbooks_extend","extend_id","readbooks_id='{$readbooks_id}'")){
                        $this->Show_css->insertData("app_readbooks_extend",$data);
                    }else{
                        $this->Show_css->updateData("app_readbooks_extend","readbooks_id='{$readbooks_id}'",$data);
                    }
                }else{
                    $data=array();
                    $data['readbooks_isRecommend']=$is_Recommend;
                    $this->Show_css->updateData("app_readbooks","readbooks_bookid='{$bookOne['bookId']}'",$data);
                    $readbooks_id=$readbooksOne['readbooks_id'];
                }

                if(!$this->Show_css->getFieldOne("app_readbooks_audio","audio_id","readbooks_id='{$readbooks_id}'")){
                    $Model = new \Model\Stuappapi\LibraryModel();
                    $tem_data=array();
                    $tem_data['readbooks_id']=$readbooks_id;
                    $oneInfo = $Model->getReadBooksOne($tem_data);

                    if($oneInfo['txtarr'] && $oneInfo['imgurl'] && $oneInfo['mp3url']){

                        foreach($oneInfo['txtarr'] as $key=>$audioOne){

                            $data=array();
                            $data['readbooks_id']=$readbooks_id;
                            $data['bookPageNumber']=$key;
                            $data['bookImageUrl']=$oneInfo['imgurl'][$key];
                            $data['bookMp3Url']=$oneInfo['mp3url'][$key];
                            $data['bookTextPlain']=addslashes(implode(chr(10),$audioOne[0]));
                            $this->Show_css->insertData("app_readbooks_audio",$data);
                        }

                    }
                }

            }else{
                if(!$readbooksOne){

                    if(!$bookInfo){
                        $url="https://thirdparty-api.bookteller.cn/books/".$bookOne['bookId'];

                        $paramarray=array();
                        $paramarray['clientToken']=$client_token;
                        $bookInfo = request_by_curl($url, '',"GET",$paramarray);
                    }

                    if($bookInfo){

                        $bookItem=json_decode($bookInfo,1)['data']['book'];
                        if($bookItem['bookId']>0){
                            $data=array();
                            $data['readbooks_language']=$bookItem['languageCode']=='en'?1:0;
                            $data['readbooks_iscomplex']=$bookItem['languageCode']=='zh_cn'?0:1;
                            $data['readbooks_title']=addslashes($bookItem['bookTitle']);
                            $data['readbooks_remark']=addslashes($bookItem['bookDescription']);
//                    $data['readbooks_coverimg']=$this->uploadPicToLocal($bookItem['bookAlbum']);
                            $data['readbooks_coverimg']=$bookItem['bookAlbum'];
                            $data['readbooks_url']='https://apish.bookteller.com.cn/kidcastle?id='.$bookItem['bookId'];
                            $data['readbooks_audioUrl']=addslashes($bookItem['bookVideoUrl']);
                            $data['readbooks_stoptime']=$bookItem['expiredDate'];
                            $data['readbooks_createtime']=time();
                            $data['readbooks_json']=addslashes($bookInfo);
                            $data['readbooks_bookid']=$bookItem['bookId'];
                            $data['readbooks_isRecommend']=$is_Recommend;

                            if(APPVER == 'TW' && $bookItem['languageCode']=='zh_tw'){
                                if($bookItem['tagTitle'] && $bookItem['tagTitle']!=''){
                                    $bookItem['tagTitle']=$TraditionalModel->gb2312_big5($bookItem['tagTitle']);
                                }


                                if($bookItem['ageTitle'] && $bookItem['ageTitle']!=''){
                                    $bookItem['ageTitle']=$TraditionalModel->gb2312_big5($bookItem['ageTitle']);
                                }
                            }

                            if($this->Show_css->getFieldOne("cms_variablelist","list_id","variable_id=1 and list_parameter='{$bookItem['tagId']}'")){
                                $data['readbooks_class']=$bookItem['tagId'];
                                if($bookItem['tagId']>0){
                                    $v_data=array();
                                    $v_data['list_name']=$bookItem['tagTitle'];
                                    $this->Show_css->updateData("cms_variablelist","variable_id=1 and list_parameter='{$bookItem['tagId']}'",$v_data);
                                }
                            }else{
                                $data['readbooks_class']=$bookItem['tagId']+10;
                                if(!$this->Show_css->getFieldOne("cms_variablelist","list_id","variable_id=1 and list_parameter='{$data['readbooks_class']}'") && $bookItem['tagId']>0){
                                    $v_data=array();
                                    $v_data['variable_id']=1;
                                    $v_data['list_name']=$bookItem['tagTitle'];
                                    $v_data['list_parameter']=$data['readbooks_class'];
                                    $v_data['list_weight']=$data['readbooks_class'];
                                    $this->Show_css->insertData("cms_variablelist",$v_data);
                                }
                            }

                            if($this->Show_css->getFieldOne("cms_variablelist","list_id","variable_id=3 and list_parameter='{$bookItem['ageId']}'")){
                                $data['readbooks_agetype']=$bookItem['ageId'];
                                if($bookItem['ageId']>0){
                                    $v_data=array();
                                    $v_data['list_name']=$bookItem['ageTitle'];
                                    $this->Show_css->updateData("cms_variablelist","variable_id=3 and list_parameter='{$bookItem['ageId']}'",$v_data);
                                }
                            }else{
                                $data['readbooks_agetype']=$bookItem['ageId']+10;

                                if(!$this->Show_css->getFieldOne("cms_variablelist","list_id","variable_id=3 and list_parameter='{$data['readbooks_agetype']}'")  && $bookItem['ageId']>0){
                                    $v_data=array();
                                    $v_data['variable_id']=3;
                                    $v_data['list_name']=$bookItem['ageTitle'];
                                    $v_data['list_parameter']=$data['readbooks_agetype'];
                                    $v_data['list_weight']=$data['readbooks_agetype'];
                                    $this->Show_css->insertData("cms_variablelist",$v_data);

                                }
                            }



                            $readbooks_id=$this->Show_css->insertData("app_readbooks",$data);
                        }else{
                            return false;
                        }

                    }
                }
                else{
                    $bookItem=json_decode($readbooksOne['readbooks_json'],1)['data']['book'];

                    if(!$bookItem){
                        $url="https://thirdparty-api.bookteller.cn/books/".$bookOne['bookId'];

                        $paramarray=array();
                        $paramarray['clientToken']=$client_token;
                        $bookInfo = request_by_curl($url, '',"GET",$paramarray);
                        $bookItem=json_decode($bookInfo,1)['data']['book'];
                    }

                    if($bookItem['bookVideoUrl']!=''){
                        $data=array();
                        $data['readbooks_audioUrl']=addslashes($bookItem['bookVideoUrl']);
                        $data['readbooks_remark']=addslashes($bookItem['bookDescription']);
                        $data['readbooks_isRecommend']=$is_Recommend;
                        $this->Show_css->updateData("app_readbooks","readbooks_bookid='{$bookOne['bookId']}'",$data);
                    }

                    $newData=array();

                    if(APPVER == 'TW' && $bookItem['languageCode']=='zh_tw'){
                        if($bookItem['tagTitle'] && $bookItem['tagTitle']!=''){
                            $bookItem['tagTitle']=$TraditionalModel->gb2312_big5($bookItem['tagTitle']);
                        }


                        if($bookItem['ageTitle'] && $bookItem['ageTitle']!=''){
                            $bookItem['ageTitle']=$TraditionalModel->gb2312_big5($bookItem['ageTitle']);
                        }
                    }


                    if($readbooksOne['readbooks_class']==$bookItem['tagId']){

                        if($this->Show_css->getFieldOne("cms_variablelist","list_id","variable_id=1 and list_parameter='{$readbooksOne['readbooks_class']}' and list_name<>'{$bookItem['tagTitle']}'")){
                            $v_data=array();
                            $v_data['list_name']=$bookItem['tagTitle'];
                            $this->Show_css->updateData("cms_variablelist","variable_id=1 and list_parameter='{$readbooksOne['readbooks_class']}'",$v_data);

                        }

                    }else{
                        if($readbooksOne['readbooks_class']!=($bookItem['tagId']+10) && $bookItem['tagId']>0){
                            $newData['readbooks_class']=$bookItem['tagId']+10;

                            if(!$this->Show_css->getFieldOne("cms_variablelist","list_id","variable_id=1 and list_parameter='{$newData['readbooks_class']}'")){
                                $v_data=array();
                                $v_data['variable_id']=1;
                                $v_data['list_name']=$bookItem['tagTitle'];
                                $v_data['list_parameter']=$newData['readbooks_class'];
                                $v_data['list_weight']=$newData['readbooks_class'];
                                $this->Show_css->insertData("cms_variablelist",$v_data);

                            }
                        }
                    }




                    if($readbooksOne['readbooks_agetype']==$bookItem['ageId']){
                        if($this->Show_css->getFieldOne("cms_variablelist","list_id","variable_id=3 and list_parameter='{$readbooksOne['readbooks_agetype']}' and list_name<>'{$bookItem['ageTitle']}'")){
                            $v_data=array();
                            $v_data['list_name']=$bookItem['ageTitle'];
                            $this->Show_css->updateData("cms_variablelist","variable_id=3 and list_parameter='{$readbooksOne['readbooks_agetype']}'",$v_data);

                        }
                    }else{
                        if($readbooksOne['readbooks_agetype']!=($bookItem['ageId']+10) && $bookItem['ageId']>0){
                            $newData['readbooks_agetype']=$bookItem['ageId']+10;

                            if(!$this->Show_css->getFieldOne("cms_variablelist","list_id","variable_id=3 and list_parameter='{$newData['readbooks_agetype']}'")){
                                $v_data=array();
                                $v_data['variable_id']=3;
                                $v_data['list_name']=$bookItem['ageTitle'];
                                $v_data['list_parameter']=$newData['readbooks_agetype'];
                                $v_data['list_weight']=$newData['readbooks_agetype'];
                                $this->Show_css->insertData("cms_variablelist",$v_data);

                            }
                        }
                    }

                    if($bookInfo){
                        $newData['readbooks_json']=addslashes($bookInfo);
                    }

                    if($newData){
                        $this->Show_css->updateData("app_readbooks","readbooks_bookid='{$bookOne['bookId']}'",$newData);
                    }

                    $readbooks_id=$readbooksOne['readbooks_id'];

                }

                if($bookItem && $readbooks_id>0){
                    $data=array();
                    $data['readbooks_id']=$readbooks_id;
                    $data['readbooks_bookAlbum']=$bookItem['bookAlbum'];
                    $data['extend_categoryTitle']=addslashes($bookItem['categoryTitle']);
                    $data['extend_gradeTitle']=addslashes($bookItem['gradeTitle']);
                    $data['extend_authorTitle']=addslashes($bookItem['authorTitle']);
                    $data['extend_illustratorTitle']=addslashes($bookItem['illustratorTitle']);
                    $data['extend_publisherTitle']=addslashes($bookItem['publisherTitle']);
                    $data['extend_publishYear']=addslashes($bookItem['publishYear']);
                    $data['extend_pageAmount']=$bookItem['pageAmount'];
                    $data['extend_textReference']=addslashes($bookItem['textReference']);
                    if(!$this->Show_css->getFieldOne("app_readbooks_extend","extend_id","readbooks_id='{$readbooks_id}'")){
                        $this->Show_css->insertData("app_readbooks_extend",$data);
                    }else{
                        $this->Show_css->updateData("app_readbooks_extend","readbooks_id='{$readbooks_id}'",$data);
                    }

                    if($bookItem['audioVoList']){
                        if(!$this->Show_css->getFieldOne("app_readbooks_audio","audio_id","readbooks_id='{$readbooks_id}'")) {
                            foreach ($bookItem['audioVoList'] as $audioOne) {
                                $data = array();
                                $data['readbooks_id'] = $readbooks_id;
                                $data['id'] = $audioOne['id'];
                                $data['bookPageNumber'] = $audioOne['bookPageNumber'];
                                $data['bookImageUrl'] = addslashes($audioOne['bookImageUrl']);
                                $data['bookMp3Url'] = addslashes($audioOne['bookMp3Url']);
                                $data['bookTextPlain'] = addslashes($audioOne['bookTextPlain']);
                                if (!$this->Show_css->getFieldOne("app_readbooks_audio", "audio_id", "readbooks_id='{$readbooks_id}' and id='{$audioOne['id']}'")) {

//                        $data['audio_bookImageUrl']=$this->uploadPicToLocal($audioOne['bookImageUrl']);
//                        $data['audio_bookMp3Url']=$this->uploadPicToLocal($audioOne['bookMp3Url']);
                                    $this->Show_css->insertData("app_readbooks_audio", $data);
                                } else {
                                    $this->Show_css->updateData("app_readbooks_audio", "readbooks_id='{$readbooks_id}' and id='{$audioOne['id']}'", $data);
                                }
                            }
                        }
                    }

                    if($bookItem['questionReferenceList'] && !$this->Show_css->getFieldOne("app_readbooks_question","question_id","readbooks_id='{$readbooks_id}'")){

                        foreach($bookItem['questionReferenceList'] as $questionOne){
                            $data=array();
                            $data['readbooks_id']=$readbooks_id;
                            $data['question_name']=addslashes($questionOne);
                            $this->Show_css->insertData("app_readbooks_question",$data);
                        }
                    }

                    if($bookItem['challengeList'] && !$this->Show_css->getFieldOne("app_readbooks_challenge","challenge_id","readbooks_id='{$readbooks_id}'")){

                        foreach($bookItem['challengeList'] as $challengeOne){
                            $data=array();
                            $data['readbooks_id']=$readbooks_id;
                            $data['challenge_name']=addslashes($challengeOne);
                            $this->Show_css->insertData("app_readbooks_challenge",$data);
                        }
                    }

                    if($bookItem['expressions'] && !$this->Show_css->getFieldOne("app_readbooks_summarizations","summarizations_id","readbooks_id='{$readbooks_id}'")){

                        foreach($bookItem['expressions'] as $key=>$summarizationsOne){
                            if($summarizationsOne['englishTitle']!='' || $summarizationsOne['chineseTitle']!='' || $summarizationsOne['phoneticTitle']!=''){
                                $data=array();
                                $data['readbooks_id']=$readbooks_id;
                                $data['englishTitle']=addslashes($summarizationsOne['englishTitle']);
                                $data['chineseTitle']=addslashes($summarizationsOne['chineseTitle']);
                                $data['phoneticTitle']=addslashes($summarizationsOne['phoneticTitle']);
                                $data['expressionType']=addslashes($summarizationsOne['expressionType']);
                                $data['summarizations']=addslashes($bookItem[$key]);
                                $this->Show_css->insertData("app_readbooks_summarizations",$data);
                            }
                        }
                    }
                }
            }




        }
    }

    function updateAllBooksAction(){

        $request = Input('get.','','trim,addslashes');

        $sql="UPDATE app_readbooks
SET readbooks_bookid = REPLACE(readbooks_url, 'http://apish.bookteller.com.cn/kidcastle?id=', '') 
WHERE readbooks_bookid = ''";
        $this->Show_css->select($sql);


        $sql="UPDATE app_readbooks
SET readbooks_bookid = REPLACE(readbooks_url, 'https://apish.bookteller.com.cn/kidcastle?id=', '') 
WHERE readbooks_bookid = ''";
        $this->Show_css->select($sql);

        $paramarray=array();
        $paramarray['grant_type']='client_credentials';
        $paramarray['client_id']='kidcastle';
        $paramarray['client_secret']='g4Ef~fBq8sFpxTjeV7fbjZ6A';
        $paramarray['scope']='user_info';

        $getInfo = request_by_curl("https://thirdparty-api.bookteller.cn/oauth2/client_token", dataEncode($paramarray),"GET");

        $getInfoArray=json_decode($getInfo,1);

        if($getInfoArray['data']){
            $client_token=$getInfoArray['data']['client_token'];

            $url="https://thirdparty-api.bookteller.cn/books/simple?pageNumber=1&pageSize=10000";

            $paramarray=array();
            $paramarray['clientToken']=$client_token;
            $bookList = json_decode(request_by_curl($url, '',"GET",$paramarray),1);

            if($bookList){
                foreach($bookList['data']['bookList'] as $bookOne){

                    $this->updateOneReadbooks($bookOne,$client_token);

                }

                ajax_return(array('error' => 0,'errortip' => "更新成功!","bakfuntion"=>"okmotify","bakurl"=>"/{$this->u}"));

            }else{
                ajax_return(array('error' => 0,'errortip' => "更新成功!","bakfuntion"=>"okmotify","bakurl"=>"/{$this->u}"));

            }
        }else{
            ajax_return(array('error' => 1,'errortip' => "更新失败!","bakfuntion"=>"okmotify","bakurl"=>"/{$this->u}"));

        }



    }

    function updateRecommendAction(){

        $request = Input('get.','','trim,addslashes');

        $sql="UPDATE app_readbooks
SET readbooks_bookid = REPLACE(readbooks_url, 'http://apish.bookteller.com.cn/kidcastle?id=', '') 
WHERE readbooks_bookid = ''";
        $this->Show_css->select($sql);


        $sql="UPDATE app_readbooks
SET readbooks_bookid = REPLACE(readbooks_url, 'https://apish.bookteller.com.cn/kidcastle?id=', '') 
WHERE readbooks_bookid = ''";
        $this->Show_css->select($sql);


        $sql="UPDATE app_readbooks set readbooks_isRecommend=0 ";
        $this->Show_css->select($sql);

        $paramarray=array();
        $paramarray['grant_type']='client_credentials';
        $paramarray['client_id']='kidcastle';
        $paramarray['client_secret']='g4Ef~fBq8sFpxTjeV7fbjZ6A';
        $paramarray['scope']='user_info';

        $getInfo = request_by_curl("https://thirdparty-api.bookteller.cn/oauth2/client_token", dataEncode($paramarray),"GET");

        $getInfoArray=json_decode($getInfo,1);

        if($getInfoArray['data']){
            $client_token=$getInfoArray['data']['client_token'];

            $url="https://thirdparty-api.bookteller.cn/books/recommend/kidcastle";

            $paramarray=array();
            $paramarray['clientToken']=$client_token;
            $bookList = json_decode(request_by_curl($url, '',"GET",$paramarray),1);
            if($bookList){
                foreach($bookList['data']['bookList'] as $bookOne){

                    $this->updateOneReadbooks($bookOne,$client_token,array(),1);
                }

                ajax_return(array('error' => 0,'errortip' => "更新成功!","bakfuntion"=>"okmotify","bakurl"=>"/{$this->u}"));

            }else{
                ajax_return(array('error' => 0,'errortip' => "更新成功!","bakfuntion"=>"okmotify","bakurl"=>"/{$this->u}"));

            }
        }else{
            ajax_return(array('error' => 1,'errortip' => "更新失败!","bakfuntion"=>"okmotify","bakurl"=>"/{$this->u}"));

        }



    }


    function uploadPicToLocal($url){

        $size = getimagesize($url);

        $path = parse_url($url, PHP_URL_PATH);

        $info = pathinfo($path);

        $name='bookAlbum.'.$info['extension'];

        $localPath = 'importexcel/pic/bookAlbum.jpg';
        $imageData = file_get_contents($url);
        file_put_contents($localPath, $imageData);

        $tem=['name'=>$name,'type'=>$size['mime'],'tmp_name'=>$localPath];

        $FILES["ossfile"]=$tem;

        $imglink = UpOssFile($FILES);

        return $imglink;
    }



    //魔术方法
    public function __call($name, $arguments) {
        $this->smarty->assign("errorTip", "Calling object method '$name' ". implode(', ', $arguments). "\n");
        $this->Viewhtm = "under.htm";
    }
    //魔术函数
    function __destruct()
    {
        $site_id = Input('get.site_id',0,'trim,addslashes');

        if ($this->c =='Wait') {
            $this->smarty->assign("u", $this->router->getController());
            $this->smarty->assign("t", $this->router->getUrl());
            $this->smarty->assign("Viewhtm", $this->Viewhtm);
            $websites = $this->Show_css->getOne("cms_websites","site_id='{$site_id}'");
            $this->smarty->assign("websites",$websites);
            if($site_id){
                if($this->UserLogin['user_type'] == '1' and $this->UserLogin['user_limitsinc'] == '1'){
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id WHERE a.site_id = '{$site_id}' ORDER BY m.module_weight ASC ");
                }else{
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id LEFT JOIN cms_websites_func as c ON c.site_id = a.site_id and c.module_id = a.module_id WHERE a.site_id = '{$site_id}' and c.user_id = '{$this->UserLogin['user_id']}' ORDER BY m.module_weight ASC ");
                }
                // print_r($moduleList);die;
                $this->smarty->assign("moduleList", $moduleList);
                $this->display("websiteindex.htm");
            }else{
                $this->display("index.htm");
            }
        }
        exit;
    }
}