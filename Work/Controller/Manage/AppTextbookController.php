<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2018/10/3
 * Time: 12:30
 */

namespace Work\Controller\Manage;


class AppTextbookController extends viewTpl{
    public $data;
    public $u;
    public $t;
    public $c;
    public $iuser;
    public $Viewhtm;

    //预加载处理类
    function __construct() {
        parent::__construct ();
        if(!$this->check_login()){
            $this->LoginView();
        }
        if($this->check_login()){
            $this->smarty->assign("iuser", $this->UserLogin);
            $this->iuser = $this->UserLogin;
        }
        $this->u = $this->router->getController();
        $this->t = $this->router->getUrl();
        $this->c = $this->router->getAction();
        $this->Viewhtm = $this->router->getController()."/".$this->router->getUrl().".htm";

        $moduleOne = $this->Show_css->getOne("cms_module","module_mark='{$this->u}'");
        $this->smarty->assign("moduleOne",$moduleOne);
    }
    //主页
    function HomeView()
    {
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $datawhere = "1";
        $pageurl = "/{$this->u}/{$this->t}?";
        if ($request['site_id']) {
            $pageurl .= "&site_id={$request['site_id']}";
        }

        $datatype = array();
        if(isset($request['keyword']) && $request['keyword'] !==''){
            $datawhere .= " and (t.textbook_branch like '%{$request['keyword']}%' or t.textbook_cnname like '%{$request['keyword']}%' or t.textbook_enname like '%{$request['keyword']}%' or t.textbook_branch like '%{$request['keyword']}%' )";
            $pageurl .="&keyword={$request['keyword']}";
            $datatype['keyword'] = $request['keyword'];
        }

        if(isset($request['course_id']) && $request['course_id'] !== '0'){
            $datawhere .= " and cl.course_id = '{$request['course_id']}' ";
            $pageurl .="&course_id={$request['course_id']}";
            $datatype['course_id'] = $request['course_id'];

            $classcodeOne = $this->Show_css->getFieldOne("eas_course","course_cnname","course_id = '{$request['course_id']}'");
            $datatype['course_cnname'] = $classcodeOne['course_cnname'];
        }

        if(isset($request['textbook_class']) && $request['textbook_class'] !== '0' && $request['textbook_class'] != ''){
            $datawhere .= " and t.textbook_class = '{$request['textbook_class']}' ";
            $pageurl .="&textbook_class={$request['textbook_class']}";
            $datatype['textbook_class'] = $request['textbook_class'];
        }

        $sql = "SELECT t.*,(SELECT count(unit_id) FROM app_textbook_unit as u WHERE u.textbook_id = t.textbook_id) as unitnums
 FROM app_textbook as t left join app_textbook_learning as l on t.textbook_id = l.textbook_id left join eas_course as cl on cl.course_branch = l.course_branch
 where {$datawhere} and t.textbook_type='0' GROUP BY t.textbook_id order by t.textbook_id DESC";

        $db_nums = $Show_css->selectOne("
select count(*) as countnums1 from
(SELECT 
    COUNT( t.textbook_id ) AS countnums 
FROM
    app_textbook AS t left join app_textbook_learning as l on t.textbook_id = l.textbook_id left join eas_course as cl on cl.course_branch = l.course_branch where {$datawhere} and t.textbook_type='0' GROUP BY t.textbook_id) as aaa");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums['countnums1'];

        $datalist = $Show_css->dbwherePage($sql,$allnum,'15',$pageurl.'&p=',$p,'10','2');

        $smarty->assign("pagelist",$datalist['pages']);//筛选信息

        $smarty->assign("dataList",$datalist['cont']);
        $smarty->assign("datatype",$datatype);
    }

    function AdaptquestionView(){
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        $unitOne = $Show_css->getOne("app_textbook_unit", "unit_id='{$request['unit_id']}'");
        $smarty->assign("unitOne", $unitOne);
        $textbookOne = $this->Show_css->getOne("app_textbook","textbook_id = '{$unitOne['textbook_id']}'");
        $smarty->assign("textbookOne", $textbookOne);


        $datawhere = "1";
        $datatype = array();
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and c.category_name like '%{$request['keyword']}%' || q.question_id like '%{$request['keyword']}%' || q.question_pid like '%{$request['keyword']}%' ";
            $datatype['keyword'] = $request['keyword'];
        }

        if (isset($request['ability_id']) && $request['ability_id'] !== '') {
            $datawhere .= " and q.ability_id = '{$request['ability_id']}'";
            $datatype['ability_id'] = $request['ability_id'];
        }

        if (isset($request['category_id']) && $request['category_id'] !== '') {
            $datawhere .= " and q.category_id = '{$request['category_id']}'";
            $datatype['category_id'] = $request['category_id'];
        }

        if (isset($request['textbook_id']) && $request['textbook_id'] !== '') {
            $datawhere .= " and q.textbook_id = '{$request['textbook_id']}'";
            $datatype['textbook_id'] = $request['textbook_id'];

            $textbookOne = $this->Show_css->getFieldOne("app_textbook","textbook_cnname","textbook_id = '{$request['textbook_id']}'");
            $datatype['textbook_cnname'] = $textbookOne['textbook_cnname'];
        }

        if (isset($request['son_unit_id']) && $request['son_unit_id'] !== '') {
            $datawhere .= " and q.unit_id = '{$request['son_unit_id']}'";
            $datatype['son_unit_id'] = $request['son_unit_id'];

            $unitOne = $this->Show_css->getFieldOne("app_textbook_unit","unit_name","unit_id = '{$request['son_unit_id']}'");
            $datatype['unit_name'] = $unitOne['unit_name'];
        }

        if (isset($request['thestatus']) && $request['thestatus'] !== '') {
            $datawhere .= " and ( SELECT COUNT( c.check_id ) FROM app_textbook_check AS c WHERE c.unit_id = '{$request['unit_id']}' AND c.question_id = q.question_id ) = '{$request['thestatus']}'";
            $datatype['thestatus'] = $request['thestatus'];
        }


        $dataList = $Show_css->select("SELECT q.question_id,q.question_pid,q.question_audio,q.question_titleimg,q.question_readtext,q.question_analysis,q.question_correct,
                q.pattern_id,q.category_id,q.father_id,p.pattern_name,a.ability_name,c.category_name,ti.tips_cnname
,(SELECT COUNT(c.check_id) FROM app_textbook_check AS c WHERE c.unit_id = '{$request['unit_id']}' and c.question_id = q.question_id) AS adapttatus
            FROM
                app_testing_question AS q
                LEFT JOIN app_testing_pattern AS p ON q.pattern_id = p.pattern_id
                LEFT JOIN app_testing_ability AS a ON a.ability_id = q.ability_id
                LEFT JOIN app_testing_category AS c ON c.category_id = q.category_id
                LEFT JOIN app_testing_tips AS ti ON ti.tips_id = q.tips_id
            WHERE {$datawhere} AND q.category_id <> 22 AND q.father_id = 0 ORDER BY q.question_pid DESC limit 0,50");
        $smarty->assign("dataList",$dataList);
        $smarty->assign("datatype",$datatype);
    }


    function batchSetQuestionAction()
    {
        $request = Input('post.','','trim,addslashes');
        if (isset($request['tab_list']) && count($request['tab_list']) > 0) {
            if ($request['type'] == 1) {
                foreach ($request['tab_list'] as $v) {
                    if (!$this->Show_css->getFieldOne('app_textbook_check', 'check_id', "textbook_id='{$request['textbook_id']}' and unit_id='{$request['unit_id']}' and question_id='{$v}'")) {
                        $data = array();
                        $data['textbook_id'] = $request['textbook_id'];
                        $data['unit_id'] = $request['unit_id'];
                        $data['question_id'] = $v;
                        $this->Show_css->insertData('app_textbook_check', $data);
                    }

                    $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"题目{$v}适配教材ID:{$request['textbook_id']}适配单元编号：{$request['unit_id']}");
                }
                ajax_return(array('error' => 0, 'errortip' => "批量设置成功!", "bakfuntion" => "refreshpage"));
            } elseif ($request['type'] == 2) {
                foreach ($request['tab_list'] as $v) {
                    $this->Show_css->delData('app_textbook_check', "textbook_id='{$request['textbook_id']}' and unit_id='{$request['unit_id']}' and question_id='{$v}'");
                    $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"题目{$v}取消适配教材ID:{$request['textbook_id']}适配单元编号：{$request['unit_id']}");
                }
                ajax_return(array('error' => 0, 'errortip' => "批量取消成功!", "bakfuntion" => "refreshpage"));
            }
        }
    }

    //修改全量测试
    function changeFulltestAction(){
        $request = Input('get.','','trim,addslashes');
        $textbookOne = $this->Show_css->getOne("app_textbook","textbook_id='{$request['id']}'");
        $data = array();
        if($textbookOne['textbook_fulltest'] == '1'){
            $data['textbook_fulltest'] = "0";
        }else{
            $data['textbook_fulltest'] = "1";
        }
        if($this->Show_css->updateData("app_textbook","textbook_id = '{$request['id']}'",$data)){
            if($data['textbook_fulltest'] == '1'){
                $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"修改教材全量测试状态，ID:{$request['id']}");
                ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"okmotify","state"=>"1"));
            }else{
                $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"修改教材全量测试状态，ID:{$request['id']}");
                ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"okmotify","state"=>"0"));
            }
        }else{
            ajax_return(array('error' => 1,'errortip' => "修改失败!","bakfuntion"=>"errormotify"));
        }
    }

    function AtapplyAction()
    {
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;

        $data = array();
        $data['textbook_id'] = $request['textbook_id'];
        $data['unit_id']      = $request['unit_id'];
        $data['question_id'] = $request['question_id'];
        if ($Show_css->insertData("app_textbook_check", $data)) {
            $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"题目{$request['question_id']}适配教材ID:{$request['textbook_id']}适配单元编号：{$request['unit_id']}");
            ajax_return(array('error' => 0, 'errortip' => "提交成功!", "bakfuntion" => "successFromTip"));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "提交失败!", "bakfuntion" => "dangerFromTip"));
        }
    }

    //活动-学校 (删除)  参与设置
    function AtapplydelAction()
    {
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        if ($Show_css->delData('app_textbook_check', "unit_id='{$request['unit_id']}' and question_id='{$request['question_id']}'")) {
            $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"题目{$request['question_id']}取消适配单元编号：{$request['unit_id']}");
            ajax_return(array('error' => 0, 'errortip' => "删除成功!", "bakfuntion" => "successFromTip"));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "删除失败!", "bakfuntion" => "dangerFromTip"));
        }
    }

    //增加系统功能
    function AddView()
    {
        $this->smarty->assign("act","Add");
        $this->Viewhtm = $this->router->getController()."/"."Manage.htm";
    }
    //提交处理机制
    function AddAction()
    {
        $request = Input('post.','','trim,addslashes');
        if($request['textbook_showname'] == '' || $request['textbook_cnname'] == ''){
            ajax_return(array('error' => 1,'errortip' => "教材APP显示名称、中文名称必须设置!","bakfuntion"=>"warningFromTip"));
        }
        if(isset($request['textbook_abbreviation']) && $request['textbook_abbreviation'] !== ''){
            if($this->Show_css->getFieldOne("app_textbook","textbook_id","textbook_abbreviation='{$request['textbook_abbreviation']}'")){
                ajax_return(array('error' => 1,'errortip' => "简称重复","bakfuntion"=>"warningFromTip"));
            }
        }

        $data = array();
        $data['fromchannel'] = '';
        $data['textbook_showname'] = $request['textbook_showname'];
        $data['textbook_cnname'] = $request['textbook_cnname'];
        $data['textbook_enname'] = $request['textbook_enname'];
        $data['textbook_abbreviation'] = $request['textbook_abbreviation'];
        $data['textbook_coverminimg'] = $request['textbook_coverminimg'];
        $data['textbook_covermaximg'] = $request['textbook_covermaximg'];
        $data['textbook_tcovermaximg'] = $request['textbook_tcovermaximg'];
        $data['textbook_tcoverminimg'] = $request['textbook_tcoverminimg'];
        $data['textbook_mapcoverimg'] = $request['textbook_mapcoverimg'];
        $data['textbook_class'] = $request['textbook_class'];
        $data['textbook_sort'] = $request['textbook_sort'];
        $data['textbook_label'] = $request['textbook_label'];
        $data['textbook_star'] = $request['textbook_star'];
        $data['textbook_level'] = $request['textbook_level']?$request['textbook_level']:0;;
        $data['textbook_isexam'] = $request['textbook_isexam'];
        $data['textbook_language'] = $request['textbook_language'];
        $data['textbook_createtime'] = time();
        if($this->Show_css->insertData("app_textbook",$data)){
            $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"新增教材数据");
            ajax_return(array('error' => 0,'errortip' => "新增成功!","bakfuntion"=>"successFromTip","bakurl"=>"/{$this->u}?site_id={$request['site_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"dangerFromTip"));
        }
    }
    //增加系统功能
    function EditView()
    {

        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        $textbookOne = $Show_css->getOne("app_textbook", "textbook_id='{$request['id']}'");
        $smarty->assign("dataVar", $textbookOne);

        $smarty->assign("act", "Edit");

        $this->Viewhtm = $this->router->getController()."/"."Manage.htm";
    }
    //提交处理机制
    function EditAction()
    {
        $request = Input('post.','','trim');
        if($request['textbook_showname'] == '' || $request['textbook_cnname'] == ''){
            ajax_return(array('error' => 1,'errortip' => "教材APP显示名称、中文名称必须设置!","bakfuntion"=>"warningFromTip"));
        }
        if(isset($request['textbook_abbreviation']) && $request['textbook_abbreviation'] !== '')
        {
            $textOne=$this->Show_css->getFieldOne("app_textbook","textbook_abbreviation","textbook_id='{$request['textbook_id']}'");
            if($textOne['textbook_abbreviation']!=$request['textbook_abbreviation']){
                if($this->Show_css->getFieldOne("app_textbook","textbook_id","textbook_abbreviation='{$request['textbook_abbreviation']}'")){
                    ajax_return(array('error' => 1,'errortip' => "简称重复","bakfuntion"=>"warningFromTip"));
                }
            }
        }
        $data = array();
//        $data['fromchannel'] = '';
        $data['textbook_showname'] = $request['textbook_showname'];
        $data['textbook_cnname'] = $request['textbook_cnname'];
        $data['textbook_enname'] = $request['textbook_enname'];
        $data['textbook_abbreviation'] = $request['textbook_abbreviation'];
        $data['textbook_coverminimg'] = $request['textbook_coverminimg'];
        $data['textbook_covermaximg'] = $request['textbook_covermaximg'];
        $data['textbook_tcovermaximg'] = $request['textbook_tcovermaximg'];
        $data['textbook_tcoverminimg'] = $request['textbook_tcoverminimg'];
        $data['textbook_mapcoverimg'] = $request['textbook_mapcoverimg'];
        $data['textbook_class'] = $request['textbook_class'];
        $data['textbook_isexam'] = $request['textbook_isexam'];
        $data['textbook_sort'] = $request['textbook_sort'];
        $data['textbook_label'] = $request['textbook_label'];
        $data['textbook_star'] = $request['textbook_star']?$request['textbook_star']:0;
        $data['textbook_level'] = $request['textbook_level']?$request['textbook_level']:0;
        $data['textbook_language'] = $request['textbook_language'];
        $data['textbook_updatatime'] = time();
        if($this->Show_css->updateData("app_textbook","textbook_id = '{$request['textbook_id']}'",$data)){
            $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"修改教材数据,ID:".$request['textbook_id']);
            ajax_return(array('error' => 0,'errortip' => "修改成功!","bakfuntion"=>"successFromTip","bakurl"=>"/{$this->u}?site_id={$request['site_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "修改失败!","bakfuntion"=>"errormotify"));
        }
    }
    //提交处理机制
    function DelAction()
    {
        $request = Input('get.','','trim,addslashes');
        $list_id = Input('get.id',0,'trim,addslashes');
        if($this->Show_css->delData('app_textbook',"textbook_id='{$list_id}'")){
            $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"删除教材数据，ID：".$list_id);
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"okmotify","bakurl"=>"/{$this->u}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"errormotify"));
        }
    }

    //提交处理机制
    function PicbookDelAction()
    {
        $request = Input('get.','','trim,addslashes');
        $list_id = Input('get.id',0,'trim,addslashes');
        $isset = $this->Show_css->getFieldOne("eas_course_times_minibookpage_readparts","readparts_id","minibookpage_id = '{$list_id}'");

        if(!$isset){
            if($this->Show_css->delData('app_textbook_minibookpage',"minibookpage_id='{$list_id}'")){
                $this->Show_css->delData('app_textbook_minibookpage_paragraph',"minibookpage_id='{$list_id}'");
                $this->Show_css->delData('app_textbook_minibookpage_paragraph_sentence',"minibookpage_id='{$list_id}'");
                $this->Show_css->delData('app_textbook_minibookpage_paragraph_word',"minibookpage_id='{$list_id}'");
                $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"删除读书检核数据，ID：".$list_id);
                ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"okmotify","bakurl"=>"/{$this->u}"));
            }else{
                ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"errormotify"));
            }
        }else{
            ajax_return(array('error' => 1,'errortip' => "已被使用，无法删除!","bakfuntion"=>"errormotify"));
        }


    }

    //编辑单元
    function UnitEditView(){
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        $textbookOne = $this->Show_css->getOne("app_textbook", "textbook_id='{$request['textbook_id']}'");
        $this->smarty->assign("textbookOne", $textbookOne);

        $unitOne = $Show_css->getOne("app_textbook_unit", "unit_id='{$request['unit_id']}'");
        $smarty->assign("dataVar", $unitOne);

        $smarty->assign("act", "UnitEdit");
        $this->Viewhtm = $this->router->getController() . "/" . "UnitEdit.htm";
    }
    //提交处理机制
    function UnitEditAction()
    {
        $request = Input('post.','','trim');

        $data = array();
        $data['unit_name'] = addslashes($request['unit_name']);
        $data['unit_sort'] = $request['unit_sort'];
        $data['unit_img'] = $request['unit_img'];
        if($this->Show_css->updateData("app_textbook_unit","textbook_id = '{$request['textbook_id']}' and unit_id = '{$request['unit_id']}' ",$data)){
            $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"修改教材单元信息,unit_id:".$request['unit_id']);
            ajax_return(array('error' => 0,'errortip' => "修改成功!","bakfuntion"=>"successFromTip","bakurl"=>"/{$this->u}/Unit?textbook_id={$request['textbook_id']}&site_id={$request['site_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "修改失败!","bakfuntion"=>"errormotify"));
        }
    }




    //提交处理机制
    function DelUnitAction()
    {
        $request = Input('get.','','trim,addslashes');
        $list_id = Input('get.unit_id',0);

        if($this->Show_css->getFieldOne("app_testing_question","question_id","unit_id='{$list_id}'")){
            ajax_return(array('error' => 1,'errortip' => "单元存在题目,不可删除!","bakfuntion"=>"errormotify"));
        }

        if($this->Show_css->delData('app_textbook_unit',"unit_id='{$list_id}'")){
            $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"删除教材单元数据，ID：".$list_id);
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"okmotify","bakurl"=>"/{$this->u}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"errormotify"));
        }
    }


    function UnitView()
    {
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        $smarty->assign("act", "Unit");

        $textbookOne = $Show_css->getOne("app_textbook", "textbook_id='{$request['textbook_id']}'");
        $smarty->assign("textbookOne", $textbookOne);

        $unitList = $this->Show_css->getList('app_textbook_unit',"textbook_id='{$textbookOne['textbook_id']}' order by unit_sort asc");
        if($unitList){
            foreach($unitList as &$val){
                $unit_testingjson=json_decode($val['unit_testingjson'],true);
                $val['unit_num']=$unit_testingjson['maxnumber'];
            }
        }
        $this->smarty->assign('unitList',$unitList);
    }
    //修改单元是否发布
    function changeIssueAction(){
        $request = Input('get.','','trim,addslashes');
        $textbookOne = $this->Show_css->getOne("app_textbook_unit","unit_id='{$request['id']}'");
        $data = array();
        if($textbookOne['unit_issue'] == '1'){
            $data['unit_issue'] = "0";
        }else{
            $data['unit_issue'] = "1";
        }
        if($this->Show_css->updateData("app_textbook_unit","unit_id = '{$request['id']}'",$data)){
            if($data['unit_issue'] == '1'){
                $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"修改教材单元是否发布状态，ID:{$request['id']}");
                ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"okmotify","state"=>"1"));
            }else{
                $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"修改教材单元是否发布状态，ID:{$request['id']}");
                ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"okmotify","state"=>"0"));
            }
        }else{
            ajax_return(array('error' => 1,'errortip' => "修改失败!","bakfuntion"=>"errormotify"));
        }
    }

    function UnitAction()
    {
        $request = Input('post.','','trim,addslashes');
        //附加选项列表
        $testbookOne=$this->Show_css->getFieldOne("app_textbook","textbook_class","textbook_id='{$request['textbook_id']}'");
        if(count($request['unit_name']) > 0 && $request['unit_name'][0] !=''){
            for($basei=0;$basei < count($request['unit_name']);$basei++){
                $baselist = array();
                $baselist['unit_name'] = $request['unit_name'][$basei];
                $baselist['unit_sort'] = $request['unit_sort'][$basei];
                $baselist['textbook_id'] = $request['textbook_id'];
                $data=array();
                if($testbookOne['textbook_class']==1){
                    if(isset($request['unit_num'][$basei]) && $request['unit_num'][$basei]!=''){
                        $data['maxnumber'] = $request['unit_num'][$basei];
                    }else{
                        $data['maxnumber']=20;
                    }
                }else{
//                    $data['maxnumber']="5";
                    $data['maxnumber']="10";// 云效任务ID：QAPP-437 要求改为10
                }
                $data['genre']="0";
                $data['detail']=array();
                $baselist['unit_testingjson']=json_encode($data);
                $this->Show_css->insertData('app_textbook_unit',$baselist);
            }
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"okmotify","bakurl"=>"/{$this->u}/{$this->c}?textbook_id=".$request['textbook_id']));
        }else{
            ajax_return(array('error' => 1,'errortip' => "提交成功!","bakfuntion"=>"okmotify"));
        }
    }

    function ErrorpicView(){
        $request = Input('get.','','trim,addslashes');

        $textbookOne = $this->Show_css->getOne("app_textbook", "textbook_id='{$request['textbook_id']}'");
        $this->smarty->assign("textbookOne", $textbookOne);

        $errorList = $this->Show_css->selectClear("SELECT q.* FROM app_testing_answers AS a, app_testing_question AS q
WHERE a.question_id = q.question_id AND a.answers_optionimg <> '' AND a.answers_optionimg NOT LIKE '%examine%' AND q.textbook_id = '{$request['textbook_id']}'
GROUP BY q.question_id");
        $this->smarty->assign("dataList", $errorList);
    }

    function PicBookView(){
        $request = Input('get.','','trim,addslashes');

        $textbookOne = $this->Show_css->getOne("app_textbook", "textbook_id='{$request['textbook_id']}'");
        $this->smarty->assign("textbookOne", $textbookOne);

        $PicBookList = $this->Show_css->selectClear("SELECT p.*,FROM_UNIXTIME(p.minibookpage_createtime,'%Y-%m-%d') as minibookpage_createtime,(select count(i.paragraph_id) from app_textbook_minibookpage_paragraph as i where i.minibookpage_id = p.minibookpage_id) as pnum,(select count(w.word_id) from app_textbook_minibookpage_paragraph_word as w where w.minibookpage_id = p.minibookpage_id) as wnum,(select count(s.sentence_id) from app_textbook_minibookpage_paragraph_sentence as s where s.minibookpage_id = p.minibookpage_id) as snum
            ,ifnull((select 1 from app_textbook_minibookpage_paragraph as x where x.minibookpage_id=p.minibookpage_id and x.paragraph_isshow=1 limit 0,1),0) as paragraph_isshow
            FROM app_textbook_minibookpage AS p
            WHERE p.textbook_id = '{$request['textbook_id']}' order by p.minibookpage_sort ASC");

        $this->smarty->assign("dataList", $PicBookList);
    }

    function ChargerDevAction(){
        $request = Input('get.','','trim,addslashes');

        $sql = "select ifnull((select 1 from app_textbook_minibookpage_paragraph as x where x.minibookpage_id=a.minibookpage_id and x.paragraph_isshow=1 limit 0,1),0) as paragraph_isshow
                from app_textbook_minibookpage as a 
                where a.minibookpage_id='{$request['id']}'";

        $minibookpageOne=$this->Show_css->selectOne($sql);

        $data = array();
        if($minibookpageOne['paragraph_isshow'] == '1'){
            $sql="update app_textbook_minibookpage_paragraph set paragraph_isshow=0 where minibookpage_id='{$request['id']}'";
        }else{
            $sql="update app_textbook_minibookpage_paragraph set paragraph_isshow=1 where minibookpage_id='{$request['id']}'";
        }

        $this->Show_css->selectClear($sql);
        $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"修改显示状态，ID:{$request['id']}");
        ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"okmotify","state"=>(1-$minibookpageOne['paragraph_isshow'])));
    }


    function ParagraphView(){
        $request = Input('get.','','trim,addslashes');

        $picbookOne = $this->Show_css->getOne("app_textbook_minibookpage", "minibookpage_id='{$request['minibookpage_id']}'");
        $this->smarty->assign("picbookOne", $picbookOne);

        $paragraphList = $this->Show_css->selectClear("SELECT p.*,FROM_UNIXTIME(p.paragraph_createtime,'%Y-%m-%d') as paragraph_createtime
 FROM app_textbook_minibookpage_paragraph AS p
WHERE p.minibookpage_id = '{$request['minibookpage_id']}' order by paragraph_sort ASC");
        $this->smarty->assign("dataList", $paragraphList);
    }

    function sentenceView(){
        $request = Input('get.','','trim,addslashes');

        $picbookOne = $this->Show_css->getOne("app_textbook_minibookpage_paragraph", "paragraph_id='{$request['paragraph_id']}'");
        $this->smarty->assign("picbookOne", $picbookOne);

        $sentenceList = $this->Show_css->selectClear("SELECT p.*
 FROM app_textbook_minibookpage_paragraph_sentence AS p
WHERE p.paragraph_id = '{$request['paragraph_id']}'");
        $this->smarty->assign("dataList", $sentenceList);
    }

    function wordView(){
        $request = Input('get.','','trim,addslashes');

        $picbookOne = $this->Show_css->getOne("app_textbook_minibookpage_paragraph", "paragraph_id='{$request['paragraph_id']}'");
        $this->smarty->assign("picbookOne", $picbookOne);

        $wordList = $this->Show_css->selectClear("SELECT p.*
 FROM app_textbook_minibookpage_paragraph_word AS p
WHERE p.paragraph_id = '{$request['paragraph_id']}'");
        $this->smarty->assign("dataList", $wordList);
    }

    function ExportAction(){

        $request = Input('get.','','trim,addslashes');
        $textbookOne = $this->Show_css->getOne("app_textbook", "textbook_id={$request['textbook_id']}");
        $this->smarty->assign("textbookOne", $textbookOne);

        $dateexcelarray = $this->Show_css->selectClear("SELECT q.* FROM app_testing_answers AS a, app_testing_question AS q
WHERE a.question_id = q.question_id AND a.answers_optionimg <> '' AND a.answers_optionimg NOT LIKE '%examine%' AND q.textbook_id = {$request['textbook_id']}
GROUP BY q.question_id");

        $outexceldate=array();
        if($dateexcelarray) {
            $outexceldate = array();
            foreach ($dateexcelarray as $dateexcelvar) {
                $datearray = array();
                $datearray['question_pid'] = $dateexcelvar['question_pid'];
                $outexceldate[] = $datearray;
            }
        }
        $excelheader = array("题目编号");
        $excelfileds = array('question_pid');
        query_to_excel($excelheader,$outexceldate,$excelfileds,"{$textbookOne['textbook_cnname']}题目错图明细.xls");

        ajax_return(array('error' => 0,'errortip' => "导出完毕!","bakfuntion"=>"okmotify"));
    }

    function CustomizedView(){
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;
        $abilityList=$this->Show_css->selectClear("select q.ability_id,ta.ability_name,(select COUNT(tq.question_id) from app_testing_question as tq where tq.ability_id=ta.ability_id and tq.unit_id='{$request['unit_id']}') as num from app_testing_question as q
left join app_testing_ability as ta on ta.ability_id=q.ability_id
left join app_textbook_unit as tu on tu.unit_id=q.unit_id
where tu.unit_id='{$request['unit_id']}' GROUP BY q.ability_id");

        $textbooke=$this->Show_css->selectOne("select * from app_textbook_unit where unit_id='{$request['unit_id']}'");

        $unit_testingjson=json_decode($textbooke['unit_testingjson'],true);
        $this->smarty->assign('abilityList',$abilityList);
        $smarty->assign("act", "Customized");
        $smarty->assign("unit_id", $request['unit_id']);
        $smarty->assign("textbook_id", $request['textbook_id']);
        $smarty->assign("unit_testingjson", $unit_testingjson['maxnumber']);
        $smarty->assign("listjson", $unit_testingjson['detail']);
    }



    function reupTeAction(){
        $request = Input('get.','','trim,addslashes');

        $data=array();
        $data['textbook_restatus']= 0;


        if($this->Show_css->updateData("app_textbook","textbook_id='{$request['textbook_id']}'",$data)){
            ajax_return(array('error' => 0,'errortip' => "上架成功!","bakfuntion"=>"okmotify","bakurl"=>"/{$this->u}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "提交失败!","bakfuntion"=>"errormotify"));
        }

    }


    function redownTeAction(){
        $request = Input('get.','','trim,addslashes');

        $data=array();
        $data['textbook_restatus']= 1;


        if($this->Show_css->updateData("app_textbook","textbook_id='{$request['textbook_id']}'",$data)){
            ajax_return(array('error' => 0,'errortip' => "下架成功!","bakfuntion"=>"okmotify","bakurl"=>"/{$this->u}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "提交失败!","bakfuntion"=>"errormotify"));
        }

    }



    function reupAction(){
        $request = Input('get.','','trim,addslashes');

        $data=array();
        $data['paragraph_isoff']= 0;


        if($this->Show_css->updateData("app_textbook_minibookpage_paragraph","paragraph_id='{$request['paragraph_id']}'",$data)){
            ajax_return(array('error' => 0,'errortip' => "上架成功!","bakfuntion"=>"okmotify","bakurl"=>"/{$this->u}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "提交失败!","bakfuntion"=>"errormotify"));
        }

    }


    function redownAction(){
        $request = Input('get.','','trim,addslashes');

        $data=array();
        $data['paragraph_isoff']= 1;


        if($this->Show_css->updateData("app_textbook_minibookpage_paragraph","paragraph_id='{$request['paragraph_id']}'",$data)){
            ajax_return(array('error' => 0,'errortip' => "下架成功!","bakfuntion"=>"okmotify","bakurl"=>"/{$this->u}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "提交失败!","bakfuntion"=>"errormotify"));
        }

    }




    function CustomizedAction(){
        $request = Input('post.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;
        $total=0;
        if($request['num'][0]){
            foreach($request['num'] as $key=>$val){
                $total=$total+$val;
            }

            if($total!=$request['maxnumber']){
                ajax_return(array('error' => 1,'errortip' => "能力总数需要和题目数量一致","bakfuntion"=>"warningFromTip"));
            }

            $json=array();
            foreach($request['ability_id'] as $key=>$val){
                $json[$key]['ability_id']=$val;
                $json[$key]['num']=$request['num'][$key];
            }
            $data=array();
            $data['maxnumber']=$request['maxnumber'];
            $data['genre']="1";
            $data['detail']=$json;
            $textbookData['unit_testingjson']=json_encode($data);
            if($this->Show_css->updateData("app_textbook_unit", "unit_id = '{$request['unit_id']}'", $textbookData)){
                ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"successFromTip","bakurl"=>"/{$this->u}?site_id={$request['site_id']}"));
            }else{
                ajax_return(array('error' => 1,'errortip' => "提交失败!","bakfuntion"=>"dangerFromTip"));
            }
        }else{

            $unitOne=$this->Show_css->getFieldOne("app_textbook_unit","textbook_id","unit_id='{$request['unit_id']}'");
            if($request['maxnumber']<=0){
                ajax_return(array('error' => 1,'errortip' => "题目数量错误","bakfuntion"=>"warningFromTip"));
            }
            $data=array();
            $data['maxnumber']=$request['maxnumber'];
            $data['genre']="0";
            $data['detail']=array();
            $textbookData['unit_testingjson']=json_encode($data);
            if($this->Show_css->updateData("app_textbook_unit", "unit_id = '{$request['unit_id']}'", $textbookData)){
                ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"successFromTip","bakurl"=>"/{$this->u}/Unit?textbook_id={$unitOne['textbook_id']}&site_id={$request['site_id']}"));
            }else{
                ajax_return(array('error' => 1,'errortip' => "提交失败!","bakfuntion"=>"dangerFromTip"));
            }

        }




    }

    function EditUnitListAction(){
        $request = Input('get.','','trim,addslashes');
        $data = array();
        if($request['field']=='unit_num'){
            $tem_data=array();
            $tem_data['maxnumber']=$request['value'];
            $tem_data['genre']="0";
            $tem_data['detail']=array();
            $data['unit_testingjson'] = json_encode($tem_data);
        }else{
            $data[$request['field']] = $request['value'];
        }
        if($this->Show_css->updateData("app_textbook_unit","unit_id = '{$request['id']}'",$data)){
            ajax_return(array('error' => 0,'errortip' => "修改成功!"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "修改失败!"));
        }
    }

    function SetClasscodeView()
    {
        $request = Input('get.','','trim,addslashes');

        $datawhere = '1';
        $datatype = array();
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and c.course_cnname like '%{$request['keyword']}%' || c.course_branch like '%{$request['keyword']}%' ";
            $datatype['keyword'] = $request['keyword'];
        }
        if (isset($request['coursecat_branch']) && $request['coursecat_branch'] !== '') {
            $datawhere .= " and c.coursecat_branch = '{$request['coursecat_branch']}'";
            $datatype['coursecat_branch'] = $request['coursecat_branch'];
        }

//        $dataList = $this->Show_css->select("select c.*,ca.coursecat_cnname from eas_course as c left join eas_code_coursecat as ca on c.coursecat_branch = ca.coursecat_branch WHERE {$datawhere} order by c.course_branch ASC");
//        foreach($dataList as &$v) {
//            if($this->Show_css->getFieldOne('app_textbook_learning','textbook_id',"textbook_id={$request['textbook_id']} and course_branch='{$v['course_branch']}'")){
//                $v['status'] = 1;
//            }else {
//                $v['status'] = 0;
//            }
//        }
//        usort($dataList,function($a,$b){
//           if($a['status'] == $b['status'])return 0;
//            return $a['status'] > $b['status']?-1:1;
//        });

        $dataList = $this->Show_css->select("SELECT c.*,cc.coursecat_cnname,
                (SELECT COUNT(b.learning_id) FROM app_textbook_learning AS b WHERE b.textbook_id = '{$request['textbook_id']}' AND b.course_branch = c.course_branch) AS status
                 FROM eas_course AS c 
                 LEFT JOIN eas_code_coursecat AS cc ON cc.coursecat_branch = c.coursecat_branch
                 WHERE {$datawhere} ORDER BY status DESC,c.course_branch ASC");

        $catcodeList = $this->Show_css->getFieldquery('eas_code_coursecat', 'coursecat_branch,coursecat_cnname', "", "order by catcode_id ASC");
        $this->smarty->assign("catcodeList", $catcodeList);
        $this->smarty->assign('dataList',$dataList);
        $this->smarty->assign('textbook_id',$request['textbook_id']);
        $this->smarty->assign("datatype",$datatype);
    }

    function SetClassAction()
    {
        $request = Input('get.','','trim,addslashes');
        $data = array();
        $data['textbook_id'] = $request['textbook_id'];
        $data['course_branch'] = $request['course_branch'];
        $this->Show_css->insertData('app_textbook_learning',$data);

        $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"教材{$request['textbook_id']}适配班别：{$request['course_branch']}");
        ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"successFromTip"));

    }

    function UnSetClassAction()
    {
        $request = Input('get.','','trim,addslashes');
        if($this->Show_css->delData('app_textbook_learning',"textbook_id={$request['textbook_id']}  and course_branch='{$request['course_branch']}'")){
            $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"教材{$request['textbook_id']}取消适配班别：{$request['course_branch']}");
            ajax_return(array('error' => 0,'errortip' => "提交成功!","bakfuntion"=>"successFromTip"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "提交失败!","bakfuntion"=>"dangerFromTip"));
        }
    }

    function batchSetClassAction()
    {
        $request = Input('post.','','trim,addslashes');
        if(isset($request['tab_list']) && count($request['tab_list'])>0){
            if($request['type']==1) {
                foreach($request['tab_list'] as $v) {
                    if(!$this->Show_css->getFieldOne('app_textbook_learning','textbook_id',"textbook_id={$request['textbook_id']} and course_branch='{$v}'")) {
                        $data = array();
                        $data['textbook_id'] = $request['textbook_id'];
                        $data['course_branch'] = $v;
                        $this->Show_css->insertData('app_textbook_learning',$data);
                    }
                    $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"教材{$request['textbook_id']}适配班别：{$request['course_branch']}");
                }
                ajax_return(array('error' => 0,'errortip' => "批量设置成功!","bakfuntion"=>"refreshpage"));
            }elseif($request['type']==2) {
                foreach($request['tab_list'] as $v) {
                    $this->Show_css->delData('app_textbook_learning',"textbook_id={$request['textbook_id']}  and course_branch='{$v}'");
                    $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"教材{$request['textbook_id']}取消适配班别：{$request['course_branch']}");
                }
                ajax_return(array('error' => 0,'errortip' => "批量取消成功!","bakfuntion"=>"refreshpage"));
            }
        }
    }


    function PictureView()
    {
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        if(!isset($request['p'])){
            $p = '1';
        }else{
            $p= $request['p'];
        }

        $pageurl = "/{$this->u}/{$this->t}?textbook_id={$request['textbook_id']}";
        if ($request['site_id']) {
            $pageurl .= "&site_id={$request['site_id']}";
        }

        $sql = "select t.*
                from app_textbook_picture as t
                where t.textbook_id='{$request['textbook_id']}'
                order by t.picture_sort asc
                ";

        $db_nums = $Show_css->selectClear("select t.picture_id
                from app_textbook_picture as t
                where t.textbook_id='{$request['textbook_id']}'");//相关条件下的总记录数COUNT(*)
        $allnum = $db_nums?count($db_nums):0;

        $datalist = $Show_css->dbwherePage($sql,$allnum,'15',$pageurl.'&p=',$p,'10','2');

        $smarty->assign("pagelist",$datalist['pages']);//筛选信息
        $smarty->assign("textbook_id", $request['textbook_id']);
        $smarty->assign("dataList",$datalist['cont']);
    }

    function AddPicView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->smarty->assign("act","AddPic");

        $this->smarty->assign("textbook_id", $request['textbook_id']);
        $this->Viewhtm = $this->router->getController()."/"."PicManage.htm";
    }

    function AddPicBookView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->smarty->assign("act","AddPicBook");

        $this->smarty->assign("textbook_id", $request['textbook_id']);

        $this->Viewhtm = $this->router->getController()."/"."PicBookManage.htm";
    }

    function AddParagraphView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->smarty->assign("act","AddParagraph");

        $this->smarty->assign("minibookpage_id", $request['minibookpage_id']);

        $this->Viewhtm = $this->router->getController()."/"."ParagraphManage.htm";
    }

    function AddsentenceView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->smarty->assign("act","Addsentence");

        $this->smarty->assign("minibookpage_id", $request['minibookpage_id']);
        $this->smarty->assign("paragraph_id", $request['paragraph_id']);


        $this->Viewhtm = $this->router->getController()."/"."sentenceManage.htm";
    }

    function AddwordView()
    {
        $request = Input('get.','','trim,addslashes');
        $this->smarty->assign("act","Addword");

        $this->smarty->assign("minibookpage_id", $request['minibookpage_id']);
        $this->smarty->assign("paragraph_id", $request['paragraph_id']);


        $this->Viewhtm = $this->router->getController()."/"."wordManage.htm";
    }

    function AddPicAction(){
        $request = Input('post.','','trim,addslashes');
        if($request['picture_url'] == '' || $request['picture_url'] == ''){
            ajax_return(array('error' => 1,'errortip' => "图片地址必须填写!","bakfuntion"=>"warningFromTip"));
        }

        if($request['picture_sort'] == '' || $request['picture_sort'] == ''){
            ajax_return(array('error' => 1,'errortip' => "图片排序必须填写!","bakfuntion"=>"warningFromTip"));
        }

        $pictureOne=$this->Show_css->getFieldOne("app_textbook_picture","picture_id","textbook_id='{$request['textbook_id']}' and picture_sort='{$request['picture_sort']}'");

        if($pictureOne){
            ajax_return(array('error' => 1,'errortip' => "同一本教材不可存在相同排序!","bakfuntion"=>"warningFromTip"));
        }

        $data = array();
        $data['picture_sort'] = $request['picture_sort'];
        $data['textbook_id'] = $request['textbook_id'];
        $data['picture_url'] = $request['picture_url'];
        $data['picture_content'] = $request['picture_content']?$request['picture_content']:'';
        $data['picture_audio'] = $request['picture_audio']?$request['picture_audio']:'';
        $data['picture_createtime'] = time();
        if($this->Show_css->insertData("app_textbook_picture",$data)){
            $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"新增教材图片数据");
            ajax_return(array('error' => 0,'errortip' => "新增成功!","bakfuntion"=>"successFromTip","bakurl"=>"/{$this->u}/Picture?textbook_id={$request['textbook_id']}&site_id={$request['site_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"dangerFromTip"));
        }
    }

    function AddPicBookAction(){
        $request = Input('post.','','trim,addslashes');

        $pictureOne=$this->Show_css->getFieldOne("app_textbook_minibookpage","minibookpage_id","textbook_id='{$request['textbook_id']}' and minibookpage_sort='{$request['minibookpage_sort']}'");

        if($pictureOne){
            ajax_return(array('error' => 1,'errortip' => "同一本教材不可存在相同排序!","bakfuntion"=>"warningFromTip"));
        }

        $data = array();
        $data['minibookpage_sort'] = $request['minibookpage_sort'];
        $data['textbook_id'] = $request['textbook_id'];
        $data['minibookpage_img'] = $request['minibookpage_img'];
        $data['minibookpage_class'] = $request['minibookpage_class'];
        $data['minibookpage_type'] = $request['minibookpage_type'];
        $data['minibookpage_createtime'] = time();
        if($this->Show_css->insertData("app_textbook_minibookpage",$data)){
            $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"新增图册图片数据");
            ajax_return(array('error' => 0,'errortip' => "新增成功!","bakfuntion"=>"successFromTip","bakurl"=>"/{$this->u}/PicBook?textbook_id={$request['textbook_id']}&site_id={$request['site_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"dangerFromTip"));
        }
    }

    function AddsentenceAction(){
        $request = Input('post.','','trim,addslashes');

        $data = array();
        $data['paragraph_sentence'] = $request['paragraph_sentence'];
        $data['minibookpage_id'] = $request['minibookpage_id'];
        $data['paragraph_id'] = $request['paragraph_id'];
        if($this->Show_css->insertData("app_textbook_minibookpage_paragraph_sentence",$data)){
            $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"新增核心单词");
            ajax_return(array('error' => 0,'errortip' => "新增成功!","bakfuntion"=>"successFromTip","bakurl"=>"/{$this->u}/sentence?paragraph_id={$request['paragraph_id']}&site_id={$request['site_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"dangerFromTip"));
        }
    }

    function AddwordAction(){
        $request = Input('post.','','trim,addslashes');

        $data = array();
        $data['paragraph_word'] = $request['paragraph_word'];
        $data['minibookpage_id'] = $request['minibookpage_id'];
        $data['paragraph_id'] = $request['paragraph_id'];
        if($this->Show_css->insertData("app_textbook_minibookpage_paragraph_word",$data)){
            $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"新增核心单词");
            ajax_return(array('error' => 0,'errortip' => "新增成功!","bakfuntion"=>"successFromTip","bakurl"=>"/{$this->u}/word?paragraph_id={$request['paragraph_id']}&site_id={$request['site_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"dangerFromTip"));
        }
    }

    function AddParagraphAction(){
        $request = Input('post.','','trim,addslashes');

        $pictureOne=$this->Show_css->getFieldOne("app_textbook_minibookpage_paragraph","paragraph_id","minibookpage_id='{$request['minibookpage_id']}' and paragraph_sort='{$request['paragraph_sort']}'");

        if($pictureOne){
            ajax_return(array('error' => 1,'errortip' => "不可存在相同排序!","bakfuntion"=>"warningFromTip"));
        }

        $data = array();
        $data['paragraph_sort'] = $request['paragraph_sort'];
        $data['minibookpage_id'] = $request['minibookpage_id'];
        $data['paragraph_img'] = $request['paragraph_img'];
        $data['paragraph_content'] = $request['paragraph_content'];
        $data['paragraph_audiourl'] = $request['paragraph_audiourl'];
        $data['paragraph_isshow'] = $request['paragraph_isshow'];
        $data['paragraph_class'] = $request['paragraph_class'];
        $data['paragraph_type'] = $request['paragraph_type'];
        $data['paragraph_createtime'] = time();
        if($pid = $this->Show_css->insertData("app_textbook_minibookpage_paragraph",$data)){
            if(count($request['paragraph_word']) > 0 && $request['paragraph_word'][0] !==''){
                for($basei=0;$basei < count($request['paragraph_word']);$basei++){
                    $baselist = array();
                    $baselist['paragraph_word'] = $request['paragraph_word'][$basei];
                    $baselist['minibookpage_id'] = $request['minibookpage_id'];
                    $baselist['paragraph_id'] = $pid;
                    if($request['paragraph_word'][$basei]){
                        $this->Show_css->insertData('app_textbook_minibookpage_paragraph_word',$baselist);
                    }
                }
            }
            if(count($request['paragraph_sentence']) > 0 && $request['paragraph_sentence'][0] !==''){
                for($basei=0;$basei < count($request['paragraph_sentence']);$basei++){
                    $baselist = array();
                    $baselist['paragraph_sentence'] = $request['paragraph_sentence'][$basei];
                    $baselist['minibookpage_id'] = $request['minibookpage_id'];
                    $baselist['paragraph_id'] = $pid;
                    if($request['paragraph_sentence'][$basei]){
                        $this->Show_css->insertData('app_textbook_minibookpage_paragraph_sentence',$baselist);
                    }
                }
            }
            $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"新增图册段落数据");
            ajax_return(array('error' => 0,'errortip' => "新增成功!","bakfuntion"=>"successFromTip","bakurl"=>"/{$this->u}/Paragraph?minibookpage_id={$request['minibookpage_id']}&site_id={$request['site_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "新增失败!","bakfuntion"=>"dangerFromTip"));
        }
    }

    function EditPicBookView()
    {

        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        $pictureOne = $Show_css->getOne("app_textbook_minibookpage", "minibookpage_id='{$request['minibookpage_id']}'");
        $smarty->assign("dataVar", $pictureOne);
        $this->smarty->assign("textbook_id", $pictureOne['textbook_id']);
        $smarty->assign("act", "EditPicBook");

        $this->Viewhtm = $this->router->getController()."/"."PicBookManage.htm";
    }

    function EditPicView()
    {

        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        $pictureOne = $Show_css->getOne("app_textbook_picture", "picture_id='{$request['id']}'");
        $smarty->assign("dataVar", $pictureOne);
        $this->smarty->assign("textbook_id", $pictureOne['textbook_id']);
        $smarty->assign("act", "EditPic");

        $this->Viewhtm = $this->router->getController()."/"."PicManage.htm";
    }


    function EditParagraphView()
    {

        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        $paragraphOne = $Show_css->getOne("app_textbook_minibookpage_paragraph", "paragraph_id='{$request['paragraph_id']}'");
        $smarty->assign("dataVar", $paragraphOne);
        $this->smarty->assign("paragraph_id", $paragraphOne['paragraph_id']);
        $this->smarty->assign("minibookpage_id", $paragraphOne['minibookpage_id']);
        $smarty->assign("act", "Editparagraph");

        $this->Viewhtm = $this->router->getController()."/"."ParagraphNewManage.htm";
    }

    function EditsentenceView()
    {

        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        $sentenceOne = $Show_css->getOne("app_textbook_minibookpage_paragraph_sentence", "sentence_id='{$request['sentence_id']}'");
        $smarty->assign("dataVar", $sentenceOne);
        $this->smarty->assign("paragraph_id", $sentenceOne['paragraph_id']);
        $this->smarty->assign("minibookpage_id", $sentenceOne['minibookpage_id']);
        $this->smarty->assign("sentence_id", $sentenceOne['sentence_id']);
        $smarty->assign("act", "Editsentence");

        $this->Viewhtm = $this->router->getController()."/"."sentenceManage.htm";
    }

    function EditwordView()
    {

        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        $sentenceOne = $Show_css->getOne("app_textbook_minibookpage_paragraph_word", "word_id='{$request['word_id']}'");
        $smarty->assign("dataVar", $sentenceOne);
        $this->smarty->assign("paragraph_id", $sentenceOne['paragraph_id']);
        $this->smarty->assign("minibookpage_id", $sentenceOne['minibookpage_id']);
        $this->smarty->assign("word_id", $sentenceOne['word_id']);
        $smarty->assign("act", "Editword");

        $this->Viewhtm = $this->router->getController()."/"."wordManage.htm";
    }

    function EditPicBookAction(){
        $request = Input('post.','','trim,addslashes');

        if($request['minibookpage_sort'] == '' || $request['minibookpage_sort'] == ''){
            ajax_return(array('error' => 1,'errortip' => "图片排序必须填写!","bakfuntion"=>"warningFromTip"));
        }

//        $pictureOne=$this->Show_css->getFieldOne("app_textbook_minibookpage","minibookpage_sort","textbook_id='{$request['minibookpage_id']}'");
//
//        if($this->Show_css->getFieldOne("app_textbook_minibookpage","minibookpage_id","textbook_id='{$request['textbook_id']}' and minibookpage_sort='{$request['minibookpage_sort']}' and minibookpage_sort<>'{$pictureOne['minibookpage_sort']}'")){
//            ajax_return(array('error' => 1,'errortip' => "同一本教材不可存在相同排序!","bakfuntion"=>"warningFromTip"));
//        }

        $data = array();
        $data['minibookpage_sort'] = $request['minibookpage_sort'];
        $data['textbook_id'] = $request['textbook_id'];
        $data['minibookpage_img'] = $request['minibookpage_img'];
        $data['minibookpage_class'] = $request['minibookpage_class'];
        $data['minibookpage_type'] = $request['minibookpage_type'];
        if($this->Show_css->updateData("app_textbook_minibookpage","minibookpage_id='{$request['minibookpage_id']}'",$data)){
            $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"编辑教材图片数据");
            ajax_return(array('error' => 0,'errortip' => "编辑成功!","bakfuntion"=>"successFromTip","bakurl"=>"/{$this->u}/PicBook?textbook_id={$request['textbook_id']}&site_id={$request['site_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "编辑失败!","bakfuntion"=>"dangerFromTip"));
        }
    }

    function EditParagraphAction(){
        $request = Input('post.','','trim,addslashes');

        if($request['paragraph_sort'] == '' || $request['paragraph_sort'] == ''){
            ajax_return(array('error' => 1,'errortip' => "图片排序必须填写!","bakfuntion"=>"warningFromTip"));
        }

        $paragraphOne=$this->Show_css->getFieldOne("app_textbook_minibookpage_paragraph","paragraph_sort","paragraph_id='{$request['paragraph_id']}'");

        if($this->Show_css->getFieldOne("app_textbook_minibookpage_paragraph","paragraph_id","minibookpage_id='{$request['minibookpage_id']}' and paragraph_sort='{$request['paragraph_sort']}' and paragraph_sort<>'{$paragraphOne['paragraph_sort']}'")){
            ajax_return(array('error' => 1,'errortip' => "同一本教材不可存在相同排序!","bakfuntion"=>"warningFromTip"));
        }

        $data = array();
        $data['paragraph_sort'] = $request['paragraph_sort'];
        $data['minibookpage_id'] = $request['minibookpage_id'];
        $data['paragraph_img'] = $request['paragraph_img'];
        $data['paragraph_content'] = $request['paragraph_content'];
        $data['paragraph_audiourl'] = $request['paragraph_audiourl'];
        $data['paragraph_isshow'] = $request['paragraph_isshow'];
        $data['paragraph_class'] = $request['paragraph_class'];
        $data['paragraph_type'] = $request['paragraph_type'];
        if($this->Show_css->updateData("app_textbook_minibookpage_paragraph","paragraph_id='{$request['paragraph_id']}'",$data)){
            $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"编辑教材图片数据");
            ajax_return(array('error' => 0,'errortip' => "编辑成功!","bakfuntion"=>"successFromTip","bakurl"=>"/{$this->u}/Paragraph?minibookpage_id={$request['minibookpage_id']}&site_id={$request['site_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "编辑失败!","bakfuntion"=>"dangerFromTip"));
        }
    }

    function EditsentenceAction(){
        $request = Input('post.','','trim,addslashes');

        $data = array();
        $data['paragraph_sentence'] = $request['paragraph_sentence'];
        if($this->Show_css->updateData("app_textbook_minibookpage_paragraph_sentence","sentence_id='{$request['sentence_id']}'",$data)){
            $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"编辑核心句子");
            ajax_return(array('error' => 0,'errortip' => "编辑成功!","bakfuntion"=>"successFromTip","bakurl"=>"/{$this->u}/sentence?paragraph_id={$request['paragraph_id']}&site_id={$request['site_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "编辑失败!","bakfuntion"=>"dangerFromTip"));
        }
    }

    function EditwordAction(){
        $request = Input('post.','','trim,addslashes');

        $data = array();
        $data['paragraph_word'] = $request['paragraph_word'];
        if($this->Show_css->updateData("app_textbook_minibookpage_paragraph_word","word_id='{$request['word_id']}'",$data)){
            $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"编辑核心单词");
            ajax_return(array('error' => 0,'errortip' => "编辑成功!","bakfuntion"=>"successFromTip","bakurl"=>"/{$this->u}/word?paragraph_id={$request['paragraph_id']}&site_id={$request['site_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "编辑失败!","bakfuntion"=>"dangerFromTip"));
        }
    }

    function EditPicAction(){
        $request = Input('post.','','trim,addslashes');
        if($request['picture_url'] == '' || $request['picture_url'] == ''){
            ajax_return(array('error' => 1,'errortip' => "图片地址必须填写!","bakfuntion"=>"warningFromTip"));
        }

        if($request['picture_sort'] == '' || $request['picture_sort'] == ''){
            ajax_return(array('error' => 1,'errortip' => "图片排序必须填写!","bakfuntion"=>"warningFromTip"));
        }

        $pictureOne=$this->Show_css->getFieldOne("app_textbook_picture","picture_sort","picture_id='{$request['picture_id']}'");

        if($this->Show_css->getFieldOne("app_textbook_picture","picture_id","textbook_id='{$request['textbook_id']}' and picture_sort='{$request['picture_sort']}' and picture_sort<>'{$pictureOne['picture_sort']}'")){
            ajax_return(array('error' => 1,'errortip' => "同一本教材不可存在相同排序!","bakfuntion"=>"warningFromTip"));
        }

        $data = array();
        $data['picture_sort'] = $request['picture_sort'];
        $data['textbook_id'] = $request['textbook_id'];
        $data['picture_url'] = $request['picture_url'];
        $data['picture_content'] = $request['picture_content']?$request['picture_content']:'';
        $data['picture_audio'] = $request['picture_audio']?$request['picture_audio']:'';
        if($this->Show_css->updateData("app_textbook_picture","picture_id='{$request['picture_id']}'",$data)){
            $this->Recordweblog($request['site_id'],$this->Module['module_id'],$this->c,"编辑教材图片数据");
            ajax_return(array('error' => 0,'errortip' => "编辑成功!","bakfuntion"=>"successFromTip","bakurl"=>"/{$this->u}/Picture?textbook_id={$request['textbook_id']}&site_id={$request['site_id']}"));
        }else{
            ajax_return(array('error' => 1,'errortip' => "编辑失败!","bakfuntion"=>"dangerFromTip"));
        }
    }

    //课程勋章列表
    function UnitWordsView(){
        $request = Input('get.', '', 'trim,addslashes');
        $unitOne = $this->Show_css->getOne("app_textbook_unit", "unit_id='{$request['unit_id']}'");
        $this->smarty->assign("unitOne", $unitOne);

        $datawhere = " 1 and a.textbook_id = '{$unitOne['textbook_id']}' and a.unit_id='{$request['unit_id']}' ";
        if (isset($request['keyword']) && $request['keyword'] !== '') {
            $datawhere .= " and (a.words_name like '%{$request['keyword']}%') ";
            $datatype['keyword'] = $request['keyword'];
        }

        $dataList = $this->Show_css->selectClear("SELECT a.* 
                 FROM app_textbook_unit_words AS a 
                 WHERE {$datawhere} 
                 ORDER BY a.words_id DESC");
        $this->smarty->assign("dataList", $dataList);

        $this->smarty->assign("datatype", $datatype);
    }

    //添加勋章
    function AddWordsView(){
        $request = Input('get.', '', 'trim,addslashes');
        $unitOne = $this->Show_css->getOne("app_textbook_unit", "unit_id='{$request['unit_id']}'");
        $this->smarty->assign("unitOne", $unitOne);

        $this->smarty->assign("act", "AddWords");
        $this->Viewhtm = $this->router->getController() . "/" . "ManageWords.htm";
    }
    //添加勋章
    function AddWordsAction(){
        $request = Input('post.','','trim,addslashes');

        if ($request['words_name'] == '') {
            ajax_return(array('error' => 1, 'errortip' => "单词文本必须设置!", "bakfuntion" => "warningFromTip"));
        }
        if ($request['words_imgurl'] == '') {
            ajax_return(array('error' => 1, 'errortip' => "单词图片必须设置!", "bakfuntion" => "warningFromTip"));
        }
        if ($request['words_audiourl'] == '') {
            ajax_return(array('error' => 1, 'errortip' => "单词音频必须设置!", "bakfuntion" => "warningFromTip"));
        }
        if ($request['unit_id'] == '') {
            ajax_return(array('error' => 1, 'errortip' => "单元必须设置!", "bakfuntion" => "warningFromTip"));
        }
        if($this->Show_css->selectOne("select words_id from app_textbook_unit_words where unit_id = '{$request['unit_id']}' and words_name = '{$request['words_name']}'  ")){
            ajax_return(array('error' => 1, 'errortip' => "该单元已存在此单词，不能重复添加!", "bakfuntion" => "warningFromTip"));
        }
        $unitOne = $this->Show_css->getOne("app_textbook_unit", "unit_id='{$request['unit_id']}'");
        $this->smarty->assign("unitOne", $unitOne);

        $data = array();
        $data['textbook_id'] = $unitOne['textbook_id'];
        $data['unit_id'] = $request['unit_id'];
        $data['words_name'] = $request['words_name'];
        $data['words_imgurl'] = $request['words_imgurl'];
        $data['words_audiourl'] = $request['words_audiourl'];
        $data['words_isopen'] = $request['words_isopen'];
        $data['words_sort'] = $request['words_sort'];
        $data['words_notes'] = $request['words_notes'];
        $data['words_createtime'] = time();
        if ($this->Show_css->insertData("app_textbook_unit_words", $data)) {
            $this->Recordweblog($request['site_id'], $this->Module['module_id'], $this->c, "新增单元单词");
            ajax_return(array('error' => 0, 'errortip' => "新增成功!", "bakfuntion" => "successFromTip", "bakurl" => "/{$this->u}/UnitWords?unit_id={$unitOne['unit_id']}&site_id={$request['site_id']}"));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "新增失败!", "bakfuntion" => "dangerFromTip"));
        }
    }

    //编辑勋章
    function EditWordsView(){
        $request = Input('get.','','trim,addslashes');
        $Show_css = $this->Show_css;
        $smarty = $this->smarty;

        $unitOne = $this->Show_css->getOne("app_textbook_unit", "unit_id='{$request['unit_id']}'");
        $this->smarty->assign("unitOne", $unitOne);

        $medalOne = $Show_css->getOne("app_textbook_unit_words", "words_id='{$request['words_id']}'");
        $smarty->assign("dataVar", $medalOne);

        $smarty->assign("act", "EditWords");
        $this->Viewhtm = $this->router->getController() . "/" . "ManageWords.htm";
    }

    function EditWordsAction(){
        $request = Input('post.','','trim,addslashes');

        if ($request['words_name'] == '') {
            ajax_return(array('error' => 1, 'errortip' => "单词文本必须设置!", "bakfuntion" => "warningFromTip"));
        }
        if ($request['words_imgurl'] == '') {
            ajax_return(array('error' => 1, 'errortip' => "单词图片必须设置!", "bakfuntion" => "warningFromTip"));
        }
        if ($request['words_audiourl'] == '') {
            ajax_return(array('error' => 1, 'errortip' => "单词音频必须设置!", "bakfuntion" => "warningFromTip"));
        }
        if ($request['unit_id'] == '') {
            ajax_return(array('error' => 1, 'errortip' => "单元必须设置!", "bakfuntion" => "warningFromTip"));
        }

        if($this->Show_css->selectOne("select words_id from app_textbook_unit_words where unit_id = '{$request['unit_id']}' and words_id <> '{$request['words_id']}' and words_name = '{$request['words_name']}'  ")){
            ajax_return(array('error' => 1, 'errortip' => "该单元已存在此单词，不能重复添加!", "bakfuntion" => "warningFromTip"));
        }
        $unitOne = $this->Show_css->getOne("app_textbook_unit", "unit_id='{$request['unit_id']}'");
        $this->smarty->assign("unitOne", $unitOne);

        $data = array();
        $data['textbook_id'] = $unitOne['textbook_id'];
        $data['unit_id'] = $request['unit_id'];
        $data['words_name'] = $request['words_name'];
        $data['words_imgurl'] = $request['words_imgurl'];
        $data['words_audiourl'] = $request['words_audiourl'];
        $data['words_isopen'] = $request['words_isopen'];
        $data['words_sort'] = $request['words_sort'];
        $data['words_notes'] = $request['words_notes'];
        $data['words_updatatime'] = time();
        if ($this->Show_css->updateData("app_textbook_unit_words", "words_id = '{$request['words_id']}'", $data)) {
            $this->Recordweblog($request['site_id'], $this->Module['module_id'], $this->c, "修改单元单词");
            ajax_return(array('error' => 0, 'errortip' => "修改成功!", "bakfuntion" => "successFromTip", "bakurl" => "/{$this->u}/UnitWords?unit_id={$unitOne['unit_id']}&site_id={$request['site_id']}"));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "修改失败!", "bakfuntion" => "errormotify"));
        }
    }
    //提交处理机制
    function DelWordsAction()
    {
        $request = Input('get.','','trim,addslashes');
        $list_id = Input('get.words_id',0,'trim,addslashes');

        if ($this->Show_css->delData('app_textbook_unit_words', "words_id='{$list_id}'")) {
            $this->Recordweblog($request['site_id'], $this->Module['module_id'], $this->c, "删除单元单词");
            ajax_return(array('error' => 0, 'errortip' => "提交成功!", "bakfuntion" => "refreshpage" ));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "新增失败!", "bakfuntion" => "errormotify"));
        }
    }

    //提交处理机制
    function DelwordAction()
    {
        $request = Input('get.','','trim,addslashes');
        $list_id = Input('get.id',0,'trim,addslashes');

//        var_dump($request);

        if ($this->Show_css->delData('app_textbook_minibookpage_paragraph_word', "word_id='{$list_id}'")) {
            $this->Recordweblog($request['site_id'], $this->Module['module_id'], $this->c, "删除核心单词");
            ajax_return(array('error' => 0, 'errortip' => "提交成功!", "bakfuntion" => "refreshpage" ));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "新增失败!", "bakfuntion" => "errormotify"));
        }
    }

    //提交处理机制
    function DelsentenceAction()
    {
        $request = Input('get.','','trim,addslashes');
        $list_id = Input('get.id',0,'trim,addslashes');

//        var_dump($request);

        if ($this->Show_css->delData('app_textbook_minibookpage_paragraph_sentence', "sentence_id='{$list_id}'")) {
            $this->Recordweblog($request['site_id'], $this->Module['module_id'], $this->c, "删除核心句子");
            ajax_return(array('error' => 0, 'errortip' => "提交成功!", "bakfuntion" => "refreshpage" ));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "新增失败!", "bakfuntion" => "errormotify"));
        }
    }

    //提交处理机制
    function DelParagraphAction()
    {
        $request = Input('get.','','trim,addslashes');
        $list_id = Input('get.id',0,'trim,addslashes');

        if ($this->Show_css->delData('app_textbook_minibookpage_paragraph', "paragraph_id='{$list_id}'")) {
            $this->Recordweblog($request['site_id'], $this->Module['module_id'], $this->c, "删除段落");
            ajax_return(array('error' => 0, 'errortip' => "提交成功!", "bakfuntion" => "refreshpage" ));
        } else {
            ajax_return(array('error' => 1, 'errortip' => "新增失败!", "bakfuntion" => "errormotify"));
        }
    }


    //魔术方法
    public function __call($name, $arguments) {
        $this->smarty->assign("errorTip", "Calling object method '$name' ". implode(', ', $arguments). "\n");
        $this->Viewhtm = "under.htm";
    }
    //魔术函数
    function __destruct()
    {
        $site_id = Input('get.site_id',0,'trim,addslashes');

        if ($this->c =='Wait') {
            $this->smarty->assign("u", $this->router->getController());
            $this->smarty->assign("t", $this->router->getUrl());
            $this->smarty->assign("Viewhtm", $this->Viewhtm);
            $websites = $this->Show_css->getOne("cms_websites","site_id='{$site_id}'");
            $this->smarty->assign("websites",$websites);
            if($site_id){
                if($this->UserLogin['user_type'] == '1' and $this->UserLogin['user_limitsinc'] == '1'){
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id WHERE a.site_id = '{$site_id}' ORDER BY m.module_weight ASC ");
                }else{
                    $moduleList = $this->Show_css->select("SELECT m.* FROM cms_module as m LEFT JOIN cms_moduleapply as a ON a.module_id = m.module_id LEFT JOIN cms_websites_func as c ON c.site_id = a.site_id and c.module_id = a.module_id WHERE a.site_id = '{$site_id}' and c.user_id = '{$this->UserLogin['user_id']}' ORDER BY m.module_weight ASC ");
                }
                $this->smarty->assign("moduleList", $moduleList);
                $this->display("websiteindex.htm");
            }else{
                $this->display("index.htm");
            }
            exit;
        }
    }

}