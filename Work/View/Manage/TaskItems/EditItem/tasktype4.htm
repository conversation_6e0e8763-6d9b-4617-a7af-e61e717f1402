<div class="content">
    <div id="container">
        <div class="bg-f row py20">
            <form action="/{$u}?c={$act}" role="form" method="post" enctype="application/x-www-form-urlencoded" class="AjaxForm">
                <input name="times_id" id="times_id" type="hidden" value="{$dataVar.times_id}">
                <input name="taskitems_id" type="hidden" value="{$dataVar.taskitems_id}">
                <input name="taskitems_tasktype" type="hidden" value="{$dataVar.taskitems_tasktype}">
                <input name="site_id" type="hidden" value="{$websites.site_id}">
                <h2 class="p20">{$stutaskOne.course_cnname}-{$stutaskOne.hour_name} {$tasktypeOne.list_name}管理
                    <span class="fr">
                    <button type="submit" class=" btn btn-success btn-cons"><i class="fa fa-check"></i>提交保存</button>
                    <button type="button" class="btn btn-primary ml10 bakFromurl">返回</button></span>
                </h2>
                <div class="col-md-8" id="Form-Box-Operating">
                    <div class="pl20 py20 f14 col-md-12">
                        <div class="form-group col-md-6">
                            <label for="taskitems_title">任务名称</label>
                            <input name="taskitems_title" id="taskitems_title" value='{$dataVar.taskitems_title|replace:"'":'&#39;'}' type="text" class="form-control" placeholder="请输入任务名称">
                        </div>
                        <div class="form-group col-md-2">
                            <label for="taskitems_taskclass">任务模式</label>
                            <select name="taskitems_taskclass" id="taskitems_taskclass" class="form-control">
                                <option value="1" selected>默认模式</option>
                                <option value="4" selected>新版绘本小程序</option>
                            </select>
                        </div>
                        <div class="form-group col-md-2">
                            <label for="taskitems_isbook">是否预习</label>
                            <select name="taskitems_isbook" id="taskitems_isbook" class="form-control">
                                <option value="0" {if $dataVar.taskitems_isbook == '0'}selected{/if}>否</option>
                                <option value="1" {if $dataVar.taskitems_isbook == '1'}selected{/if}>是</option>
                            </select>
                        </div>
                        <div class="form-group col-md-2">
                            <label for="taskitems_sort">任务排序</label>
                            <input name="taskitems_sort" id="taskitems_sort" value="{$dataVar.taskitems_sort}" type="text" class="form-control" placeholder="请输入任务排序">
                        </div>
                        <div class="form-group col-md-3">
                            <label for="g_times_id">课次</label>
                            <select name="g_times_id" id="g_times_id" class="form-control">
                                <option value="">请选择任务分类</option>
                                {if $timesList}
                                {foreach from=$timesList item=timesVar}
                                <option value="{$timesVar.times_id}" {if $timesVar.times_id eq $dataVar.times_id}selected{/if}>{$timesVar.times_name}</option>
                                {/foreach}
                                {/if}
                            </select>
                        </div>
                        <div class="form-group col-md-3">
                            <label for="taskitems_modetype">任务分类</label>
                            <select name="taskitems_modetype" id="taskitems_modetype" class="form-control">
                                <option value="">请选择任务分类</option>
                                {if $modeList}
                                {foreach from=$modeList item=modeVar}
                                <option value="{$modeVar.modetype_id}" {if $modeVar.modetype_id eq $dataVar.taskitems_modetype}selected{/if}>{$modeVar.modetype_name}</option>
                                {/foreach}
                                {/if}
                            </select>
                        </div>
                        <div class="form-group col-md-2">
                            <label for="taskitems_week">限制周次</label>
                            <select name="taskitems_week" id="taskitems_week" class="form-control">
                                <option value="">请选择限制周次</option>
                                {if $weekList}
                                {foreach from=$weekList item=weekVar}
                                <option value="{$weekVar.weektype_value}" {if $weekVar.weektype_value == $dataVar.taskitems_week}selected{/if}>{$weekVar.weektype_name}</option>
                                {/foreach}
                                {/if}
                            </select>
                        </div>
                        <div class="form-group col-md-6">
                            <label for="readbooks_id">阅读图书</label>
                            <select name="readbooks_id" id="readbooks_id" data-placeholder="请选择阅读图书,可检索书名" class="form-control readbooksAjax">
                                {if $dataVar.readbooks_id}
                                <option value="{$dataVar.readbooks_id}" selected="selected">{$dataVar.readbooks_title}</option>
                                {/if}
                            </select>
                        </div>
                        
                        <div class="form-group col-md-6">
                            <label for="readbooks_id">图书板块</label>
                            <select name="plate_ids" id="plate_ids" data-placeholder="请选择阅读图书板块" class="form-control readbooksPlateAjax">
                                {if $dataVar.plate_ids}
                                <option value="{$dataVar.plate_ids}" selected="selected">{$dataVar.plate_ids_name}</option>
                                {/if}
                            </select>
                        </div>
                        <div class="form-group col-md-4">
                            <label for="taskitems_diamondprice">钻石数量</label>
                            <input name="taskitems_diamondprice" id="taskitems_diamondprice" value="{$dataVar.taskitems_diamondprice}" type="text" class="form-control" placeholder="请输入钻石数量">
                        </div>
                        <div class="form-group col-md-12">
                            <label for="taskitems_content">任务内容</label>
                            <textarea name="taskitems_content" id="taskitems_content" class="form-control" rows="3">{$dataVar.taskitems_content}</textarea>
                        </div>
                        <input id="course_branch" type="hidden" value="{$stutaskOne.course_branch}">
                        <div class="form-group col-md-6">
                            <label for="textbook_id">所属教材</label>
                            <select name="textbook_id" id="textbook_id" class="textbookAjax">
                                <option value="">请选择所属教材</option>
                                {if $dataVar.textbook_id}
                                <option value="{$dataVar.textbook_id}" selected>{$dataVar.textbook_cnname}</option>
                                {/if}
                            </select>
                        </div>
                        <div class="form-group col-md-6">
                            <label for="fucmodule_branch">所属板块</label>
                            <select name="fucmodule_branch" id="fucmodule_branch" class="fucmoduleAjax">
                                <option value="">请选择所属板块</option>
                                {if $dataVar.fucmodule_branch}
                                <option value="{$dataVar.fucmodule_branch}" selected>{$dataVar.fucmodule_name}</option>
                                {/if}
                            </select>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>